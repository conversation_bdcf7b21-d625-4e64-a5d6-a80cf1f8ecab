#ifndef SSINGLEACQUISITION_H
#define SSINGLEACQUISITION_H

#include <QScriptEngine>
#include <QWidget>
#include "ui_uiSingleAcquisition.h"
#include <LibGlobalToolsR/include/sThread.h>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <LibWidget/sChartWidget.h>
#include <cDataFileWrite.h>
#include <sSingleAcquisition/MRMParameterEditor.h>
#include <sSingleAcquisition/Q13SIMParamEditor.h>
#include <USBConnection/HCSCommandUSB.h>
#include "cConfigTQ_HZH.h"
// #include "cParamTQ_HZH.h"
// #include "sSingleAcquisition/PreferenceEditor.h"
#include "sSingleAcquisition/Q13ScanParamEditor.h"
#include <sSingleAcquisition/IonScanParamEditor.h>
#include "MassEventSplitter.h"
#include "sSystem.h"
#include "sTune.h"
// #include "UMAStructure/UMA_HCS_Data.h"
#include "cFooting/cAcqFunction.h"
#include <cFooting/cMemDataStruct.h>
#include <sTune/cFunctionsTune.h>
#include <uiSingleAcquisition.h>

class sSingleAcquisition : public uiSingleAcquisition
{
    Q_OBJECT

public:
    explicit sSingleAcquisition(QScriptEngine *pScriptEngine,
                                sTune *pTune,
                                sSystem *pSystem,
                                QWidget *parent = nullptr);
    ~sSingleAcquisition();
    void initClass(QString &filePath);
    cParamValue::_Segment *getSegmentParam(QByteArray *pArray = nullptr)
    {
        if (pArray)
            return (cParamValue::_Segment *)pArray->data();
        else
            return (cParamValue::_Segment *)mSegment.data();
    }
    QByteArray getSegment() { return mSegment; }
    _CONGIG_OMS::_CONGIG_ACQUISITION_LIT *getConfig()
    {
        return &mCONGIG_ACQUISITION;
    }
    bool startDataAcquisiton(QByteArray &,
                             QString fileHead,
                             QByteArray streamHead = QByteArray(),
                             bool saveFile = false);
    void stopDataAcquisiton()
    {
        if (mAnalyzeThread->isRunning())
        {
            mAnalyzeThread->stop();
            mAnalyzeThread->wait();
        }
    }

private:
    // cDataFilter* mDataFilterDLL= nullptr;
    // cPeakAlgorithm mSignalProcessing;
    QByteArray mSegment;
    _CONGIG_OMS::_CONGIG_ACQUISITION_LIT mCONGIG_ACQUISITION;
    _CONGIG_OMS::_PARAM_FIT mCALIBRATE;
    bool loadConfigFile(_CONGIG_OMS::_CONGIG_ACQUISITION_LIT *pCONGIG_ACQUISITION,
                        bool ifUpdateUI = false,
                        QString qPath = nullptr);
    QByteArray getTuningFile;
    bool loadTuningFile(bool ifUpdateUI = false);

protected:
    typedef QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> ListSTRUCT_DATA;
    ListSTRUCT_DATA mListSTRUCT_DATA;
    /*aquisition and charts*******************************************************/
    MassEventSplitter *m_pMsEvtSplitter;

private:
    //    int m_curMsSegment, m_curMsEvt, m_curMsCh;
    //    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE m_curUMAType;
    //_STRUCT_ScanInfo mScanInfo;
    std::vector<MassEventSplitter::MassSegment> m_msSegments;
    //    int umaChMapMsChs(const UMA_HCS::HCSDataFrame::HCSData &chData,
    //                      QList<TYPEDEF_EVENT>* pMsChs);
    //    void accMsChDataSeg(const QVector<MsChData>& msChsData,
    //                                        QVector<DataSeg>* msChsAccData);
    //    void calcTIC(const UMA_HCS::HCSDataFrame::HCSData &chFrame, const UMA_HCS::HCSSegHead::HCSSegHeadData &segHead);
    void updateScanParam();

    QByteArray mDataBuffer;
    QList<std::vector<double>> *mGraphBuffX; // std::vector<double>* mGraphBuff[2];         //size-allPoint
    QList<std::vector<double>> *mGraphBuffY;
    QList<std::vector<double>> *mAbsc;
    QList<std::vector<double>> *mOrd;
    QList<_STRUCT_GRAPH> *mList_PEAK;
    QList<std::vector<quint32>> mPointTimeSIM;
    void memoryInit()
    {
        mGraphBuffX = _CHART_X_ACQUIS::get();
        mGraphBuffY = _CHART_Y_ACQUIS::get();
        mList_PEAK = _CHART_PEAK_ACQUIS::get();
        mAbsc = _CHART_ABSC_ACQUIS::get();
        mOrd = _CHART_ORD_ACQUIS::get();
    }

    SThread *mAnalyzeThread = nullptr;
    static int analyzeThread(void *pParam, const bool &bRunning);
    int mTimerRefreshID = -1;
    void timerEvent(QTimerEvent *evt);

protected:
    _STRUCT_ADC_TDC m_STRUCT_ADC_TDC;
    bool acquisitionStart()
    {
        mAcqFunction.mOffsetFile = 0;
        mAnalyzeThread->start();
        if (mTimerRefreshID == -1)
        {
            mTimerRefreshID = startTimer(500);
        }
        return true;
    }
    volatile quint32 mCountEvt = 0;
    volatile bool mIsNewMass = false;
    volatile bool mIsNewTIC = false;
    _STRUCT_TIC mSTRUCT_TIC;
    QMutex mGraphBuffMutex;
    // void resizeChart(int size);
    void initTIC()
    {
        mSTRUCT_TIC.clear();
        mChartTIC->clearBuffXIC();
    }
    bool scanAnalyzeXIC(const double *pX, const double *pY,
                        const _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA,
                        uint32_t currentEvt,
                        uint32_t sizeTIC);
    bool simAnalyzeXIC(const std::vector<double> &pX,
                       const std::vector<double> &pY,
                       const _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA,
                       uint32_t currentEvt,
                       uint32_t sizeTIC);
    bool AnalyzeXIC(const ListSTRUCT_DATA &listSTRUCT_DATA,
                    quint32 sizeTIC,
                    const QList<std::vector<double>> *mThreadBuffY,
                    const QList<std::vector<double>> *mThreadBuffX);
    bool dataDismantleTIC(QByteArray &pByteArray,
                          QByteArray &pMarkBody,
                          double &xTIC,
                          double &yTIC);
    quint32 dataDismantleFirst(QByteArray &pByteArray, cParamValue::_Segment *pSegment,
                               QList<std::vector<double>> *pListX,
                               QList<std::vector<double>> *pListY,
                               ListSTRUCT_DATA &pSTRUCT_DATA,
                               bool restart = false);
    bool dataDismantleSecond(const QByteArray &pByteArray,
                             QList<std::vector<double>> *pListY,
                             const ListSTRUCT_DATA &pSTRUCT_DATA);

    bool AnalyzeScan(double *pX, double *pY,
                     _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA,
                     int currentEvt, int countEvt,
                     _STRUCT_PEAK &pSTRUCT_PEAK,
                     quint32 uWidth);

    // void updateScanInfo(int segIndex, int msEvtIndex, int msChIndex, cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE umaType);
    // bool updateASG(QVector<quint32>& pBuffMethodASG/*, QString& paramStr*/);
    /*data file*******************************************************/
protected:
    cAcqFunction mAcqFunction;
    virtual bool onManualScan();
    virtual void onManualStop();
    /*ui********************************************************************/
private:
    UMA_HCS::HCSCommandUSB *mHCSCommUSB;
    // QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET> mList_MZ_PARAM_SET;
    cTQ_StructCMD_HZH::_EVENT_PARAM_SET m_EVENT_PARAM_SET;
    // cTQ_StructCMD_AD81416::_EVENT_PARAM_SET m_EVENT_PARAM_LDAC_SET;
    bool getParamDAQ(_STRUCT_ADC_TDC &pSTRUCT_ADC_TDC)
    {
        bool ok;
        double floatValue = ui.UI_LE_AcqACC_SINGLEACQ->text().toDouble(&ok);
        if (!ok)
            return false;
        pSTRUCT_ADC_TDC.ACC = static_cast<UINT>(floatValue); // daqAccNum;

        floatValue = ui.UI_LE_AcqFreq_SINGLEACQ->text().toDouble(&ok);
        if (!ok)
            return false;
        pSTRUCT_ADC_TDC.Frq = static_cast<UINT>(floatValue); // daqFreqHz;

        floatValue = ui.UI_LE_SCANSTEP_SINGLEACQ->text().toDouble(&ok);
        if (!ok)
            return false;
        pSTRUCT_ADC_TDC.Scan_Step = floatValue;

        if (ui.UI_CB_ACQ_MODE_SINGLEACQ->currentText() == "TDC")
        {
            pSTRUCT_ADC_TDC.Mode = 1;
        }
        else
        {
            pSTRUCT_ADC_TDC.Mode = 0;
        }
        return true;
    }
    bool getQ1ScanParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                        cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getQ3ScanParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                        cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getQ1SimParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                       cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getQ3SimParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                       cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getMrmParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                     cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getProductParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                         cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getPrecursorParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                           cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);
    bool getNeutralLossParam(cTQ_StructCMD_HZH::_EVENT_PARAM &calEVENT_PARAM, cTQ_StructCMD_AD81416::_EVENT_PARAM &calEVENT_PARAM_LDAC,
                             cTQ_StructCMD_HZH::_EVENT_PARAM &pEVENT_PARAM);

private slots:
    //    void onUI_MB_RUN_SINGLEACQ();
    //    void onUI_MB_STOP_SINGLEACQ();
    //    void on_UI_PB_ADVANCE_SINGLEACQ_clicked();
    // void on_UI_TABWIDGET_PARAM_SINGLEACQ_currentChanged(int index);
};

#endif // SSINGLEACQUISITION_H
