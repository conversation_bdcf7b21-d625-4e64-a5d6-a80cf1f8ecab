#include "cAcqFunction.h"
#include "cDebugFunctions.h"

#define SDBG_AcqFunction(msg) //qDebug()<<msg


cAcqFunction::cAcqFunction()
{

}

bool cAcqFunction::dataSplit( /*QVector<double>& sampleData,*/
                             const UMA_HCS::HCSDataFrame::HCSData& chFrame,
                             _STRUCT_ADC_TDC pSTRUCT_ADC_TDC,//uint ACC,
                             uint    muRefVolDigit,
                              //const QList<TYPEDEF_EVENT>& msChsMaped,
                             MsChData& msChsData)
{
    /*QVector<double>*/std::vector<double> sampleData;
    double tmpACC= pSTRUCT_ADC_TDC.ACC;
    sampleData.resize(chFrame.lDataLen);
    for (int i = 0; i < chFrame.lDataLen; ++i)
        sampleData.data()[i] = static_cast<double>(chFrame.lpRawData[i]) /
                tmpACC- muRefVolDigit;
    sampleData.back() = *(sampleData.end() - 2);//最后一个点可能未达到累加次数，使之与倒数第2个数相等
    msChsData.push_back(std::move(sampleData));
    return true;
}

bool cAcqFunction::splitAndAverage(const double* data, const int size, std::vector<double>& averages, double ScanStep, double MzStart, double MzEnd)
{
    if(ScanStep== 0)
        return false;
    int n = (MzEnd- MzStart)/ ScanStep+ 1;
    //int size = data.size();
    if (n < 1 || size< 1)
        return false;

    double baseSize = size / n;
    if(baseSize< 1)
        return false;
    int remainder = size % n;

    averages.resize(n);
    int start = 0;
    for (int i = 0; i < n; ++i) {
        //double sum = std::accumulate(data+ start, data+ start+ baseSize, 0.0);
        averages[i]= data[static_cast<int>(static_cast<double>(i)* baseSize)];//averages[i]= sum / baseSize;//(baseSize > 0) ? (sum / baseSize) : 0.0;
        //start += baseSize;
    }

    return true;
}

bool cAcqFunction::fillChartBuffScan(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                     const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                     const MsChData& pMsChData, //const DataSeg& pDataSeg,
                                     const quint8 q,
                                     double& tic,
                                     std::vector<double>& pGraphBuffX,
                                     std::vector<double>& pGraphBuffY)
{
    if((umaEvt.listChannel.size()!= 1)||(pMsChData.size()!= 1))
        return false;
    const std::vector<double>& pDataSeg= pMsChData.at(0);
    const auto& msCh= umaEvt.listChannel.at(0);
    //const cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& msCh = pTYPEDEF_EVENT/*msChsMaped[chIndex]*/.second;
    int SwitchTimePoint= static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                          * msCh.PN_SwitchTimeMs / 1000.0
                                          / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
    int PauseTimePoint = static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                          * msCh.PauseTimeMs / 1000.0
                                          / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
    //int SettlingTimePoint= static_cast<uint>(daqFreqHz * msCh.SettlingTimeMs / 1000.0 / daqAccNum/*accNum*/);

    double tmpfirstMz, tmpsecondMz;
    if(q==1){
        tmpfirstMz= msCh.Q1_Mz_Start;
        tmpsecondMz= msCh.Q1_Mz_End;
    }else if(q==3){
        tmpfirstMz= msCh.Q3_Mz_Start;
        tmpsecondMz= msCh.Q3_Mz_End;
    }else
        return false;

    int validSize = pDataSeg.size() - SwitchTimePoint- PauseTimePoint/*SettlingTimePoint*/;//m_msSettingPoint;
    if(validSize<0){
        SDBG_AcqFunction("pDataSeg.size,SwitchTimePoint,PauseTimePoint"<<
             pDataSeg.size()<< SwitchTimePoint<< PauseTimePoint);
        return false;
    }
#if 1
//    pGraphBuffY.resize(validSize);
//    pGraphBuffX.resize(validSize);
//    double mzStep = (tmpsecondMz - tmpfirstMz) / (float)validSize;
    const double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;


    if(!splitAndAverage(pSrc, validSize, pGraphBuffY, p_STRUCT_ADC_TDC.Scan_Step, tmpfirstMz, tmpsecondMz))
        return false;
    validSize= pGraphBuffY.size();//根据Scan_Step重新换算现有点数
    pGraphBuffX.resize(validSize);

    tic= 0;
    if(p_STRUCT_ADC_TDC.Mode){//TDC
        for (int i = 0; i < validSize; ++i){
            pGraphBuffX[i] = tmpfirstMz + i * p_STRUCT_ADC_TDC.Scan_Step/*mzStep*/;
            pGraphBuffY[i]*= p_STRUCT_ADC_TDC.Frq;//pGraphBuffY[i] = pSrc[i]* p_STRUCT_ADC_TDC.Frq;
            tic+= pGraphBuffY[i];
        }
    }else{
        for (int i = 0; i < validSize; ++i){
            pGraphBuffX[i] = tmpfirstMz + i * p_STRUCT_ADC_TDC.Scan_Step/*mzStep*/;
            //pGraphBuffY[i] = pSrc[i];
            tic+= pGraphBuffY[i];
        }
    }
#else
        pGraphBuffY.resize(validSize);
        pGraphBuffX.resize(validSize);
        double mzStep = (tmpsecondMz - tmpfirstMz) / (float)validSize;
        const double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;
        tic= 0;
        if(p_STRUCT_ADC_TDC.Mode){//TDC
            for (int i = 0; i < validSize; ++i){
                pGraphBuffX[i] = tmpfirstMz + i * mzStep;
                pGraphBuffY[i] = pSrc[i]* p_STRUCT_ADC_TDC.Frq;
                tic+= pGraphBuffY[i];
            }
        }else{
            for (int i = 0; i < validSize; ++i){
                pGraphBuffX[i] = tmpfirstMz + i * mzStep;
                pGraphBuffY[i] = pSrc[i];
                tic+= pGraphBuffY[i];
            }
        }
#endif

    tic/= validSize;
    return true;
}

bool cAcqFunction::fillChartBuffSIM(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                     const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                    const MsChData& pMsChData, //const DataSeg& pDataSeg,
                                    const quint8 q,
                                    double& tic,
                                    std::vector<double>& pGraphBuffX,
                                    std::vector<double>& pGraphBuffY)
{
   int channelSize= umaEvt.listChannel.size();
   if(channelSize!= pMsChData.size())
       return false;
   //const cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& msCh = pTYPEDEF_EVENT/*msChsMaped[chIndex]*/.second;

   //float eventTime_ms= umaEvt.eventTime_ms();
   pGraphBuffY.resize(channelSize);
   pGraphBuffX.resize(channelSize);
   tic= 0;
   for(int i= 0; i< channelSize; ++i/*const auto& pDataSeg: pMsChData*/){
       const std::vector<double>& pDataSeg= pMsChData.at(i);
       const auto& msCh= umaEvt.listChannel.at(i);
       int SwitchTimePoint= static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                             * static_cast<double>(msCh.PN_SwitchTimeMs) / 1000.0
                                             / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
       int PauseTimePoint = static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                             * static_cast<double>(msCh.PauseTimeMs) / 1000.0
                                             / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
       //int SettlingTimePoint= static_cast<uint>(daqFreqHz * msCh.SettlingTimeMs / 1000.0 / daqAccNum/*accNum*/);
       int validSize = static_cast<int>(pDataSeg.size())- SwitchTimePoint- PauseTimePoint/*SettlingTimePoint*/;//m_msSettingPoint;
       if(validSize< 0){
           SDBG_AcqFunction("pDataSeg.size, SwitchTimePoint, PauseTimePoint"<<
                pDataSeg.size()<<SwitchTimePoint<<PauseTimePoint);
           return false;
       }
       SDBG_AcqFunction("pDataSeg.size, SwitchTimePoint, PauseTimePoint"<<
            pDataSeg.size()<<SwitchTimePoint<<PauseTimePoint);
       if(q==1){
           pGraphBuffX[i]= static_cast<double>(msCh.Q1_Mz_Start);
       }else if(q==3){
           pGraphBuffX[i]= static_cast<double>(msCh.Q3_Mz_Start);
       }else
           return false;

       const double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;
       if(p_STRUCT_ADC_TDC.Mode){//TDC
           pGraphBuffY[i]= std::accumulate(pSrc, pSrc+ validSize- 1, 0.0)
                   / static_cast<double>(validSize)
                   * p_STRUCT_ADC_TDC.Frq;
       }else{
           pGraphBuffY[i]= std::accumulate(pSrc, pSrc+ validSize- 1, 0.0)
                   / static_cast<double>(validSize);
       }
       tic+= pGraphBuffY[i];//tic/ channelSize* (channelSize- 1)+ pGraphBuffY[i]/ channelSize;
   }
   tic/= channelSize;
    return true;
}

bool cAcqFunction::fillChartBuffMRM(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                     const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                    const MsChData& pMsChData, //const DataSeg& pDataSeg,
                                    //const quint8 q,
                                    double& tic,
                                    std::vector<double>& pGraphBuffX,
                                    std::vector<double>& pGraphBuffY)
{
   int channelSize= umaEvt.listChannel.size();
   if(channelSize!= pMsChData.size())
       return false;
   //const cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& msCh = pTYPEDEF_EVENT/*msChsMaped[chIndex]*/.second;

   //float eventTime_ms= umaEvt.eventTime_ms();
   pGraphBuffY.resize(channelSize);
   pGraphBuffX.resize(channelSize);
   tic= 0;
   for(int i= 0; i< channelSize; ++i/*const auto& pDataSeg: pMsChData*/){
       const std::vector<double>& pDataSeg= pMsChData.at(i);
       const auto& msCh= umaEvt.listChannel.at(i);
       int SwitchTimePoint= static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                             * static_cast<double>(msCh.PN_SwitchTimeMs) / 1000.0
                                             / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
       int PauseTimePoint = static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
                                             * static_cast<double>(msCh.PauseTimeMs) / 1000.0
                                             / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
       //int SettlingTimePoint= static_cast<uint>(daqFreqHz * msCh.SettlingTimeMs / 1000.0 / daqAccNum/*accNum*/);
       int validSize = static_cast<int>(pDataSeg.size())- SwitchTimePoint- PauseTimePoint/*SettlingTimePoint*/;//m_msSettingPoint;
       if(validSize< 0){
           SDBG_AcqFunction("pDataSeg.size, SwitchTimePoint, PauseTimePoint"<<
                pDataSeg.size()<<SwitchTimePoint<<PauseTimePoint);
           return false;
       }
       //           if(q==1){
       //           pGraphBuffX[i]= static_cast<double>(msCh.Q1_Mz_Start);
       //       }else if(q==3){
       pGraphBuffX[i]= static_cast<double>(msCh.Q3_Mz_Start);
       //       }else
       //           return false;

       const double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;
       if(p_STRUCT_ADC_TDC.Mode){//TDC
           pGraphBuffY[i]= std::accumulate(pSrc, pSrc+ validSize- 1, 0.0)
                   / static_cast<double>(validSize)
                   * p_STRUCT_ADC_TDC.Frq;
       }else{
           pGraphBuffY[i]= std::accumulate(pSrc, pSrc+ validSize- 1, 0.0)
                   / static_cast<double>(validSize);
       }
       tic+= pGraphBuffY[i];//tic/ channelSize* (channelSize- 1)+ pGraphBuffY[i]/ channelSize;
   }
   tic/= channelSize;
    return true;
}

void cAcqFunction::write2File(qint64& offsetFile,
                              const QByteArray& pSrcData,
                              double xTIC,
                              double yTIC,
                              //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                              double NoSeg,
                              double NoEvt,
                              double NoExp,
                              QByteArray& tempMarkBody/*,
                                                                   _StreamBody::Type_Data typeData*/)
{
    if(!mFileDAQ.isOpened())
        return;
    do{
        mFileDAQ.backup();
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&offsetFile), sizeof(qint64)))
            break;
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&xTIC), sizeof(double)))
            break;
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&yTIC), sizeof(double)))
            break;
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&NoSeg), sizeof(double)))
            break;
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&NoEvt), sizeof(double)))
            break;
        if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&NoExp), sizeof(double)))
            break;
        double tempValue=0;
        //mChartTIC->lockXIC();
//        for(auto mass: pXIC){
//            for(auto paramXIC: mass){
//                uint32_t sizeY=paramXIC->yListXIC.size();
//                if(sizeY<=0)
//                    tempValue=0;
//                else
//                    tempValue=paramXIC->yListXIC[sizeY-1];
//                if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&tempValue), sizeof(double)))
//                    break;
//            }
//        }
        //mChartTIC->unLockXIC();
        //        if(property("VacuumGauge").toInt()){
        //            double tmpMbar= property("CurrentVacuumMbar").toDouble();
        //            if(!mFileDAQ.writeParam(reinterpret_cast<char*>(&tmpMbar), sizeof(double)))
        //                break;
        //        }
        unsigned long frameLength= mFileDAQ.writeData(tempMarkBody, pSrcData);
        if(!frameLength)
            break;
        offsetFile+= frameLength;
        return ;
    }while(0);
    mFileDAQ.recover();
}

bool cAcqFunction::fillFileStructScan(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                      const quint8 q/*q=1||q=3*/,
                                      QByteArray& baEvtParam)
{
    baEvtParam.resize(sizeof(cParamValue::_EventScan));//_EventScan _EventProfile
    cParamValue::_EventScan* p_EventScan = (cParamValue::_EventScan*)baEvtParam.data();
    memset(p_EventScan->title,'\0',30);
    if(q==1){
        p_EventScan->msStart=
                p_EventScan->msStartOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q1_Mz_Start);
        p_EventScan->msEnd=
                p_EventScan->msEndOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q1_Mz_End);

    }else if(q==3){
        p_EventScan->msStart=
                p_EventScan->msStartOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q3_Mz_Start);
        p_EventScan->msEnd=
                p_EventScan->msEndOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q3_Mz_End);
    }else
        return false;
    p_EventScan->msStart=
            p_EventScan->msStartOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q3_Mz_Start);
    p_EventScan->msEnd=
            p_EventScan->msEndOrig= static_cast<double>(p_EVENT_PARAM.listChannel[0].Q3_Mz_End);
    //QByteArray tmpstr=pEvt->title.toLocal8Bit();
    //memcpy(p_EventScan->title,tmpstr.data(),tmpstr.size());
    p_EventScan->type = cParamValue::Type_Scan;
    p_EventScan->holdTimeMs = static_cast<double>(p_EVENT_PARAM.eventTime_ms());
    p_EventScan->msPrecursor= 0;
    p_EventScan->minTimeMs = 0;
    p_EventScan->delayTimeMs= 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PN_SwitchTimeMs);
    p_EventScan->preReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PauseTimeMs);
    p_EventScan->postReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].WaitTimeMs);
return true;
}

bool cAcqFunction::fillFileStructSIM(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                      const quint8 q/*q=1||q=3*/,
                                      QByteArray& baEvtParam)
{
    baEvtParam.resize(sizeof(cParamValue::_EventSIM2048));//_EventScan _EventProfile
    cParamValue::_EventSIM2048* p_EventSIM2048 = (cParamValue::_EventSIM2048*)baEvtParam.data();
    memset(p_EventSIM2048->title,'\0',30);
    int sizeChannel= p_EVENT_PARAM.listChannel.size();
    if(sizeChannel< 1)
        return false;
    if(q==1){
        for(int i= 0; i< sizeChannel; ++i){
            p_EventSIM2048->mass[i]=
                    p_EventSIM2048->massOrig[i]= static_cast<double>(p_EVENT_PARAM.listChannel[i].Q1_Mz_Start);
            p_EventSIM2048->timeMs[i]= p_EVENT_PARAM.listChannel[i].eventTime_ms();
        }
    }else if(q==3){
        for(int i= 0; i< sizeChannel; ++i){
            p_EventSIM2048->mass[i]=
                    p_EventSIM2048->massOrig[i]= static_cast<double>(p_EVENT_PARAM.listChannel[i].Q3_Mz_Start);
            p_EventSIM2048->timeMs[i]= p_EVENT_PARAM.listChannel[i].eventTime_ms();
        }
    }else
        return false;
    //QByteArray tmpstr=pEvt->title.toLocal8Bit();
    //memcpy(p_EventSIM->title,tmpstr.data(),tmpstr.size());
    p_EventSIM2048->type = cParamValue::Type_SIM_2048;
    p_EventSIM2048->holdTimeMs = static_cast<double>(p_EVENT_PARAM.eventTime_ms());
    p_EventSIM2048->msPrecursor= 0;
    p_EventSIM2048->minTimeMs = 0;
    p_EventSIM2048->delayTimeMs= 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PN_SwitchTimeMs);
    p_EventSIM2048->preReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PauseTimeMs);
    p_EventSIM2048->postReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].WaitTimeMs);
    return true;
}

bool cAcqFunction::fillFileStructMRM(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                      QByteArray& baEvtParam)
{
    baEvtParam.resize(sizeof(cParamValue::_EventMRM));//_EventScan _EventProfile
    cParamValue::_EventMRM* p_EventMRM = (cParamValue::_EventMRM*)baEvtParam.data();
    memset(p_EventMRM->title,'\0',30);
    int sizeChannel= p_EVENT_PARAM.listChannel.size();
    if(sizeChannel< 1)
        return false;
        for(int i= 0; i< sizeChannel; ++i){
            p_EventMRM->massPre[i]=
                    p_EventMRM->massPreOrig[i]= static_cast<double>(p_EVENT_PARAM.listChannel[i].Q1_Mz_Start);
            p_EventMRM->mass[i]=
                    p_EventMRM->massOrig[i]= static_cast<double>(p_EVENT_PARAM.listChannel[i].Q3_Mz_Start);
            p_EventMRM->timeMs[i]= p_EVENT_PARAM.listChannel[i].eventTime_ms();
        }
    //QByteArray tmpstr=pEvt->title.toLocal8Bit();
    //memcpy(p_EventSIM->title,tmpstr.data(),tmpstr.size());
    p_EventMRM->type = cParamValue::Type_MRM_2048;
    p_EventMRM->holdTimeMs = static_cast<double>(p_EVENT_PARAM.eventTime_ms());
    p_EventMRM->msPrecursor= 0;
    p_EventMRM->minTimeMs = 0;
    p_EventMRM->delayTimeMs= 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PN_SwitchTimeMs);
    p_EventMRM->preReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].PauseTimeMs);
    p_EventMRM->postReadyTimeMs = 0;//static_cast<double>(p_EVENT_PARAM.listChannel[0].WaitTimeMs);
    return true;
}

bool cAcqFunction::fillSegmentStruct(const QList<cTQ_StructCMD_HZH::_EVENT_PARAM>& p_EVENT_PARAM_list,
                                     QByteArray& graphParam)
{
//    QString fileHead;
//    QString strXIC= mDataAcquisition->getStrPPRatioXIC();
//    QString strSelectXIC= mDataAcquisition->getStrSelectXIC();
//    QByteArray graphParam;
    if(p_EVENT_PARAM_list.size()<1)
        return false;

    graphParam.resize(sizeof(cParamValue::_Segment) - sizeof(cParamValue::_Event));

    int eventSize= 0;
    for(const auto p_EVENT_PARAM: p_EVENT_PARAM_list){
        QByteArray baEvtParam(nullptr);

        switch (p_EVENT_PARAM.Type) {
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_NEG:{
            if(!fillFileStructSIM(p_EVENT_PARAM, 1, baEvtParam))
                return false;
        }break;
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_NEG:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG:{
            if(!fillFileStructSIM(p_EVENT_PARAM, 3, baEvtParam))
                return false;
        }break;
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_NEG:{
            if(!fillFileStructMRM(p_EVENT_PARAM, baEvtParam))
                return false;
        }break;
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
            if(!fillFileStructScan(p_EVENT_PARAM, 1, baEvtParam))
                return false;
        }break;
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS:
        case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG:{
            if(!fillFileStructScan(p_EVENT_PARAM, 3, baEvtParam))
                return false;
        }break;
        default:break;
        }
        graphParam+= baEvtParam;
        ++eventSize;
    }
    cParamValue::_Segment* p_StreamHead =(cParamValue::_Segment*)(graphParam.data());
    p_StreamHead->type = cParamValue::Type_Seg_TripleQ;
    p_StreamHead->lengthReserved=0;
    p_StreamHead->countsEvent = 1;
    p_StreamHead->lengthEvent= eventSize;
    p_StreamHead->length= graphParam.size();

//    QStringList tmpStrList;
//    property2StrList(tmpStrList);
//    tmpStrList<<"起始质量&"+ QString::number(mass_scan_cmd.mass_begin);
//    tmpStrList<<"终止质量&"+ QString::number(mass_scan_cmd.mass_end);
//    tmpStrList<<"单个质量点数&"+ QString::number(mass_scan_cmd.points_per_amu);
//    tmpStrList<<"驻留时间(ms)&"+ QString::number(mass_scan_cmd.dwell_time);
//    QString pPropertyStr= tmpStrList.join("\n");

//    QByteArray streamHead= createStreamHead(graphParam,
//                                            fileHead,
//                                            strXIC,
//                                            QString(),
//                                            mDataAcquisition->getTuningFile,
//                                            );
    return true;
}
//QByteArray streamHead= cDataAcquisitionF::createStreamHead(graphParam,
//                                                           fileHead,
//                                                           strXIC,
//                                                           strSelectXIC,
//                                                           pPropertyStr,
//                                                           mDataAcquisition->getTuningFile,
//                                                           otherLines);
//bool cAcqFunction::fillChartBuff(const TYPEDEF_EVENT& pTYPEDEF_EVENT,
//                                 const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
//                                 const DataSeg& pDataSeg,
//                                 const quint8 q,
//                                 std::vector<double>& pGraphBuffX,
//                                 std::vector<double>& pGraphBuffY)
//{
//const cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& msCh = pTYPEDEF_EVENT/*msChsMaped[chIndex]*/.second;
//int SwitchTimePoint= static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
//                                       * msCh.PN_SwitchTimeMs / 1000.0
//                                       / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
//int PauseTimePoint = static_cast<int>(p_STRUCT_ADC_TDC.Frq/*daqFreqHz*/
//                                       * msCh.PauseTimeMs / 1000.0
//                                       / p_STRUCT_ADC_TDC.ACC/*daqAccNum*//*accNum*/);
////int SettlingTimePoint= static_cast<uint>(daqFreqHz * msCh.SettlingTimeMs / 1000.0 / daqAccNum/*accNum*/);

////std::vector<double>& chData = pDataSeg;

//switch (pTYPEDEF_EVENT.first) {
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS:
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_NEG:
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS:
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_NEG:{
//    //                QVector<double> msX(1), msY(1);
//    //                msX[0] = msCh->firstMz;
//    //                msY[0] = accumulate(pDataSeg.constBegin() + m_msPausePoint, pDataSeg.constEnd(), 0.0);
//    //                emit updateMassData(chIndex, msX, msY);
//}
//    break;
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:/*{
//    tmpfirstMz= msCh.Q1_Mz_Start;
//    tmpsecondMz= msCh.Q1_Mz_End;
//}*/
//    //            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
//    //                tmpfirstMz= msCh.Q1_Mz_Start;
//    //                tmpsecondMz= msCh.Q1_Mz_End;
//    //            }
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
//    double tmpfirstMz, tmpsecondMz;
//    if(q==1){
//        tmpfirstMz= msCh.Q1_Mz_Start;
//        tmpsecondMz= msCh.Q1_Mz_End;
//    }else if(q==3){
//        tmpfirstMz= msCh.Q3_Mz_Start;
//        tmpsecondMz= msCh.Q3_Mz_End;
//    }else
//        return false;
//    //std::vector<double>& pGraphBuffY= (*pDataAcquisiton->mGraphBuffY)[0];
//    //std::vector<double>& pGraphBuffX= (*pDataAcquisiton->mGraphBuffX)[0];

//    int validSize = pDataSeg.size() - SwitchTimePoint- PauseTimePoint/*SettlingTimePoint*/;//m_msSettingPoint;
//    if(validSize<0){
//        SDBG_AcqFunction("pDataSeg.size,SwitchTimePoint,PauseTimePoint"<<
//                  pDataSeg.size()<<SwitchTimePoint<<PauseTimePoint);
//        continue;
//    }
//    pDataAcquisiton->mGraphBuffMutex.lock();
//    pGraphBuffY.resize(validSize);
//    pGraphBuffX.resize(validSize);
////                memcpy(pGraphBuffY.data(), pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/,
////                       validSize * sizeof (double));
//    double mzStep = (tmpsecondMz - tmpfirstMz) / (float)validSize;
//    const double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;
//    if(p_STRUCT_ADC_TDC.Mode){//TDC
//        for (int i = 0; i < validSize; ++i){
//            pGraphBuffX[i] = tmpfirstMz + i * mzStep;
//            pGraphBuffY[i] = pSrc[i]* p_STRUCT_ADC_TDC.Frq;
//        }
//    }else{
//        for (int i = 0; i < validSize; ++i){
//            pGraphBuffX[i] = tmpfirstMz + i * mzStep;
//            pGraphBuffY[i] = pSrc[i];
//        }
//    }
//    pDataAcquisiton->mIsNewMass= true;
//    pDataAcquisiton->mGraphBuffMutex.unlock();
//}break;
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
//case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:{
//    tmpfirstMz= msCh.Q3_Mz_Start;
//    tmpsecondMz= msCh.Q3_Mz_End;
//    //(*pDataAcquisiton->mGraphBuffY)[0].resize(msChSize);
//    //std::vector<double>& pGraphBuffY= (*pDataAcquisiton->mGraphBuffY)[0];
//    //std::vector<double>& pGraphBuffX= (*pDataAcquisiton->mGraphBuffX)[0];
//    //pGraphBuffY.resize(msChSize);

//    uint validSize = pDataSeg.size() - SwitchTimePoint- PauseTimePoint/*SettlingTimePoint*/;//m_msSettingPoint;
//    //QVector<double> msX(validSize, 0), msY(validSize, 0);

//    pDataAcquisiton->mGraphBuffMutex.lock();
//    pGraphBuffY.resize(validSize);
//    pGraphBuffX.resize(validSize);
////                memcpy(pGraphBuffY.data(), pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/,
////                       validSize * sizeof (double));
//    double mzStep = (tmpsecondMz - tmpfirstMz) / (float)validSize;
////                for (int i = 0; i < validSize; ++i)
////                    pGraphBuffX[i] = tmpfirstMz + i * mzStep;
//    double* pSrc= pDataSeg.data()+ SwitchTimePoint+ PauseTimePoint/*SettlingTimePoint*/;
//    if(p_STRUCT_ADC_TDC.Mode){//TDC
//        for (int i = 0; i < validSize; ++i){
//            pGraphBuffX[i] = tmpfirstMz + i * mzStep;
//            pGraphBuffY[i] = pSrc[i]* p_STRUCT_ADC_TDC.Frq;
//        }
//    }else{
//        for (int i = 0; i < validSize; ++i){
//            pGraphBuffX[i] = tmpfirstMz + i * mzStep;
//            pGraphBuffY[i] = pSrc[i];
//        }
//    }

//    pDataAcquisiton->mIsNewMass= true;
//    pDataAcquisiton->mGraphBuffMutex.unlock();
//    //emit updateMassData(chIndex, msX, msY);
//}
//    break;
//default:break;
//}
//}
