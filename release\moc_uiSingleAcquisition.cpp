/****************************************************************************
** Meta object code from reading C++ file 'uiSingleAcquisition.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiSingleAcquisition.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiSingleAcquisition.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiSingleAcquisition_t {
    QByteArrayData data[12];
    char stringdata0[257];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiSingleAcquisition_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiSingleAcquisition_t qt_meta_stringdata_uiSingleAcquisition = {
    {
QT_MOC_LITERAL(0, 0, 19), // "uiSingleAcquisition"
QT_MOC_LITERAL(1, 20, 21), // "onUI_MB_RUN_SINGLEACQ"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 22), // "onUI_MB_STOP_SINGLEACQ"
QT_MOC_LITERAL(4, 66, 34), // "on_UI_PB_ADVANCE_SINGLEACQ_cl..."
QT_MOC_LITERAL(5, 101, 36), // "on_UI_LE_AcqACC_SINGLEACQ_tex..."
QT_MOC_LITERAL(6, 138, 4), // "arg1"
QT_MOC_LITERAL(7, 143, 39), // "on_UI_LE_SCANSTEP_SINGLEACQ_t..."
QT_MOC_LITERAL(8, 183, 14), // "onUpdateFreAcq"
QT_MOC_LITERAL(9, 198, 5), // "speed"
QT_MOC_LITERAL(10, 204, 46), // "on_UI_TABWIDGET_PARAM_SINGLEA..."
QT_MOC_LITERAL(11, 251, 5) // "index"

    },
    "uiSingleAcquisition\0onUI_MB_RUN_SINGLEACQ\0"
    "\0onUI_MB_STOP_SINGLEACQ\0"
    "on_UI_PB_ADVANCE_SINGLEACQ_clicked\0"
    "on_UI_LE_AcqACC_SINGLEACQ_textEdited\0"
    "arg1\0on_UI_LE_SCANSTEP_SINGLEACQ_textChanged\0"
    "onUpdateFreAcq\0speed\0"
    "on_UI_TABWIDGET_PARAM_SINGLEACQ_currentChanged\0"
    "index"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiSingleAcquisition[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x09 /* Protected */,
       3,    0,   50,    2, 0x09 /* Protected */,
       4,    0,   51,    2, 0x09 /* Protected */,
       5,    1,   52,    2, 0x09 /* Protected */,
       7,    1,   55,    2, 0x09 /* Protected */,
       8,    1,   58,    2, 0x0a /* Public */,
      10,    1,   61,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::Int,    9,
    QMetaType::Void, QMetaType::Int,   11,

       0        // eod
};

void uiSingleAcquisition::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiSingleAcquisition *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onUI_MB_RUN_SINGLEACQ(); break;
        case 1: _t->onUI_MB_STOP_SINGLEACQ(); break;
        case 2: _t->on_UI_PB_ADVANCE_SINGLEACQ_clicked(); break;
        case 3: _t->on_UI_LE_AcqACC_SINGLEACQ_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->on_UI_LE_SCANSTEP_SINGLEACQ_textChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->onUpdateFreAcq((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 6: _t->on_UI_TABWIDGET_PARAM_SINGLEACQ_currentChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiSingleAcquisition::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiSingleAcquisition.data,
    qt_meta_data_uiSingleAcquisition,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiSingleAcquisition::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiSingleAcquisition::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiSingleAcquisition.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiSingleAcquisition::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
