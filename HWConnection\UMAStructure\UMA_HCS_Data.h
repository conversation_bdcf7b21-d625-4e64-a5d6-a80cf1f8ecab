﻿#pragma once

#include <QtGlobal>
#include <QObject>
#include "DataIO/FileShared.h"
#include "UMAStructure/UMAEventSegmentSplitter.h"

/**
 * @file UMA_HCS_Data.h
 * @brief 用于UMA数据文件读写
 */

namespace UMA_HCS {

/**
 * @brief The magic number for UMA HCS
 *
 *
 *
 */
constexpr uint64_t MAGIC_ID_UMA_HCS_DATA_FILE_META = 0xe6822048685da727ULL;
constexpr uint64_t MAGIC_ID_UMA_HCS_SEG_HEAD_META = 0xe6822048685da727ULL;
constexpr uint64_t MAGIC_ID_UMA_HCS_DATA_FRAME = 0xe6822048685da727ULL;

/**
 * @brief The HCSDataFileMeta class
 *
 * Not finished now
 */
//class /*HWCONNECTION_EXPORT*/ HCSDataFileMeta : public UMA::MetaInfoBase
//{
//public:
//    typedef struct
//    {
//        uint64_t date;
//        uint64_t detectorType;
//        uint64_t massMethodMD5Upper;
//        uint64_t massMethodMD5Lower;
//        std::vector<UMA_Event> events;
//    } HCSFileMetaData;
//public:
//    HCSDataFileMeta();
//    virtual ~HCSDataFileMeta();
//    virtual uint64_t size() const override;
//    virtual const void* data() const override;
//    virtual bool deserialize(char *data, uint64_t size) override;
//    virtual uint64_t magicId() const override;

//    void setData(const HCSFileMetaData& _data);
//    const HCSFileMetaData& innerData() const;
//private:
//    HCSFileMetaData mData;
//    std::vector<char> mBuffer;

//    void prepareBuffer(const HCSFileMetaData& _data);
//};

//class /*HWCONNECTION_EXPORT*/ HCSSegHead : public UMA::MetaInfoBase
//{
//public:
//    typedef struct
//    {
//        uint64_t lUMAEventNo;
//        uint64_t lUMAChannelId;
//        uint64_t lUMAChannelNo;
//        uint64_t lDataBodySize;
//        double dTime;
//        double dTIC;
//    } HCSSegHeadData;

//public:
//    HCSSegHead();
//    virtual ~HCSSegHead();
//    virtual uint64_t size() const override;
//    virtual const void* data() const override;
//    virtual bool deserialize(char *data, uint64_t size) override;
//    virtual uint64_t magicId() const override;

//    void setData(const HCSSegHeadData& head);
//    const HCSSegHeadData& innerData() const;
//private:
//    HCSSegHeadData mData;
//};



class /*HWCONNECTION_EXPORT*/ HCSDataFrame// : public UMA::DataFrameBase
{
public:
    typedef struct
    {
        uint64_t lUMAChannelId;
        uint64_t lUMAChannelNo;
        uint64_t lUMAEventNo;
        uint64_t lUMASegmentNo;
        uint64_t lDataLen;
        double dTimeMs;
        double dTIC;
        //uint64_t lMassChannelCount;///< the ms trigger count
        //uint64_t lMassTriggerId;   ///< the first mass trigger index
        //uint64_t* lpMassChannelIndex;
        uint32_t* lpRawData;
    } HCSData;
public:
    HCSDataFrame();
//    virtual ~HCSDataFrame() override;
//    virtual uint64_t size() const override;
//    virtual const void * data() const override;
//    virtual bool deserialize(char *data, uint64_t size) override;
//    virtual uint64_t magicId() const override;

    //bool getChannelData(uint64_t massChannelId, std::vector<uint32_t>* pData);
    void setData(uint64_t channel, double timeMs, double dTIC, /*uint64_t triggerIndex,*/
                 const std::vector<uint32_t> & data/*, const std::vector<uint64_t>& index*/);
    void copy(HCSDataFrame* other);
    void setEvSplitter(const std::shared_ptr<UMAEventSegmentSplitter>& splitter);
    const HCSData& innerData() const;
private:
    HCSData mData;
    std::vector<uint32_t> mRawDataContainer;
    //std::vector<uint64_t> mChannelIndexContainer;///< ms Channel offset
    //std::vector<char> mBuffer;
    std::shared_ptr<UMAEventSegmentSplitter> mEvSplitterPtr;
    //void prepareBuffer(std::vector<char>* buffer);
};

}
