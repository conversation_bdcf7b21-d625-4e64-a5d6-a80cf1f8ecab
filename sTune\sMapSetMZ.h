#pragma once

#include <UI/uiTune/uiMapSetMZ.h>

class sMapSetMZ : public uiMapSetMZ
{
    Q_OBJECT
public:

    explicit sMapSetMZ(QWidget *parent = nullptr);
    ~sMapSetMZ();
    virtual void initClass(QString& filePath);
    QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET>& getMzVoltagesHDAC(){
        return mList_MZ_PARAM_SET_HDAC;
    }
    QList<cTQ_StructCMD_AD81416::_MZ_PARAM_SET>& getMzVoltagesLDAC(){
        return mList_MZ_PARAM_SET_LDAC;
    }

private:
    QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET> mList_MZ_PARAM_SET_HDAC;
    QList<cTQ_StructCMD_AD81416::_MZ_PARAM_SET> mList_MZ_PARAM_SET_LDAC;
//    QVector<double> mGainHDAC, mOffsetHDAC;
//    QVector<double> mGainLDAC, mOffsetLDAC;
    bool updateMemoryParam();
    bool voltages2MzHDAC(const HZH::paramMZ_Voltages& mzVoltages,
                                const _STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC,
                                          cTQ_StructCMD_HZH::_MZ_PARAM_SET& p_MZ_PARAM_SET);
    bool voltages2MzLDAC(const HZH::paramMZ_Voltages& mzVoltages,
                                const _STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC,
                                     cTQ_StructCMD_AD81416::_MZ_PARAM_SET& p_MZ_PARAM_SET);
};

