﻿#pragma once

#include "uiBaseParamEditor.h"
#include "ui_uiIonScanParamEditor.h"

class uiIonScanParamEditor : public uiBaseParamEditor
{
public:
    uiIonScanParamEditor(const QString& type, QWidget* parent = nullptr);
    ~uiIonScanParamEditor();
    //void saveParameter() override;
    virtual void initClass(QString& filePath);

protected:
    Ui::uiIonScanParamEditor ui;
    const QString m_strEventType;
    virtual bool initUI(QString& filePath);

private:
    void initPage(const QString& eventType);

};

