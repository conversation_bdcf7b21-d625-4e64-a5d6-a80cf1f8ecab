#include "uiCtrlTFG.h"

uiCtrlTFG::uiCtrlTFG(QWidget *parent) :
    QWidget(parent)/*,
      ui(new Ui::uiCtrlTFG)*/
{
    ui.setupUi(this);
    ui.Heat_PW_ON->setProperty("sw", 0x00);
    ui.CID_Rel_ON->setProperty("sw", 0x00);
    //QSenddata.resize(70);
    //memset(QSenddata.data(),0,70);
    //serialPort1 = new QSerialPort;
    //connect(serialPort1, &QSerialPort::readyRead, this, &uiCtrlTFG::serialPort1DataReceived);

    mDeviceSerial= sCommManager::getCommManager();
    mDeviceNumber= mDeviceSerial->addSerial(0x7523, 0x1A86, 3000, BAUD115200);
    SerialDeviceManager::SerialCMD tempSerialCMD(120, stateReqCMD(),
                                                          checkStateReq, this);//, 3000
    mDeviceSerial->setOpenCMD(mDeviceNumber, tempSerialCMD);
    tempSerialCMD.TimeOutMS_CMD= 3000;
    mDeviceSerial->startThrough(mDeviceNumber, tempSerialCMD, 500);



}

uiCtrlTFG::~uiCtrlTFG()
{
    //delete ui;
}

void uiCtrlTFG::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiCtrlTFG::initUI(QString& filePath)
{
    ui.comboBox2->addItem(QStringLiteral("9600"), QSerialPort::Baud9600);
    ui.comboBox2->addItem(QStringLiteral("19200"), QSerialPort::Baud19200);
    ui.comboBox2->addItem(QStringLiteral("38400"), QSerialPort::Baud38400);
    ui.comboBox2->addItem(QStringLiteral("115200"), QSerialPort::Baud115200);
    ui.comboBox2->setCurrentIndex(3);

    on_update_SerialPort_clicked();
    return true;
}

bool uiCtrlTFG::checkStateReq(QByteArray& QRecivedata, void* pThis)
{
    if (QRecivedata.size()< 120)
        return false;
    if (QRecivedata.at(3) != 0x04)
        return false;
    ((uiCtrlTFG*)pThis)->mQRecivedata= QRecivedata;
    return true;
}

bool uiCtrlTFG::checkParamReq(QByteArray& QRecivedata, void* pThis)
{
    if (QRecivedata.size()< 120)
        return false;
    if (QRecivedata.at(3) != 0x03)
        return false;
    //((uiCtrlTFG*)pThis)->mQRecivedata= QRecivedata;
    return true;
}

void uiCtrlTFG::on_Monitor_ON_clicked()
{
    if(timer1!=-1)
        killTimer(timer1);
    timer1=-1;
    if(ui.Monitor_ON->text()== "打开电压监控"){
        ui.Monitor_ON->setText("关闭电压监控");
        ui.Monitor_ON->setStyleSheet("QPushButton { background-color: LightCoral; }");
        //ui.Monitor_ON.BackColor = Color.LightCoral;
        timer1= startTimer(1000);
    }else{
        ui.Monitor_ON->setText("打开电压监控");
        ui.Monitor_ON->setStyleSheet("QPushButton { background-color: LightGray; }");
    }
}

void uiCtrlTFG::on_Purge_Gas_ON_clicked()
{
    if(ui.Purge_Gas_ON->text()=="开进气"){
        //Purge_Gas_on_D = 0xFF;
        ui.Purge_Gas_ON->setText("关进气");
        ui.Purge_Gas_ON->setStyleSheet("QPushButton { background-color: LightCoral; }");
        ui.Rpump_ON->setEnabled(false);//Rpump_ON.Enabled = false;
    }else{
        //Purge_Gas_on_D = 0x00;
        ui.Purge_Gas_ON->setText("开进气");
        ui.Purge_Gas_ON->setStyleSheet("QPushButton { background-color: LightGray; }");
        ui.Rpump_ON->setEnabled(true);//Rpump_ON.Enabled= true;
    }
}

void uiCtrlTFG::on_IG_ON_clicked()
{
    if(ui.IG_ON->text()=="开IG"){
        //IG_on_D = 0xFF;
        ui.IG_ON->setText("关IG");
        ui.IG_ON->setStyleSheet("QPushButton { background-color: LightCoral; }");
    }else{
        //IG_on_D = 0x00;
        ui.IG_ON->setText("开IG");
        ui.IG_ON->setStyleSheet("QPushButton { background-color: LightGray; }");
    }
}

void uiCtrlTFG::on_Tpump_Frq_textChanged(const QString &arg1)
{
    if(Tpump_Frq_value>4.9){
        ui.Tpump_Frq->setStyleSheet("QLineEdit { background-color: LightGray; }");
        //ui.Tpump_Frq.BackColor = Color.LightGreen;
        ui.HED_HV_ON->setEnabled(true);//.Enabled = true;
        ui.DET_HV_ON->setEnabled(true);//.Enabled = true;
    }else{
        ui.Tpump_Frq->setStyleSheet("QLineEdit { background-color: LightCoral; }");

        //HED_HV_on = false;
        //HED_HV_on_D = 0x00;
        ui.HED_HV_ON->setEnabled(false);
        ui.HED_HV_ON->setText("HED_ON");
        ui.HED_HV_ON->setStyleSheet("QPushButton { background-color: LightGray; }");//.BackColor =Color.LightGray;

        //DET_HV_on = false;
        //DET_HV_on_D = 0x00;
        ui.DET_HV_ON->setEnabled(false);
        ui.DET_HV_ON->setText("DET_ON");
        ui.DET_HV_ON->setStyleSheet("QPushButton { background-color: LightGray; }");//.BackColor = Color.LightGray;
    }
}

void uiCtrlTFG::on_Tpump_ON_clicked()
{
    if(ui.Tpump_ON->text()=="开分子泵"){
        //Tpump_on_D = 0xFF;
        ui.Tpump_ON->setText( "关分子泵");
        ui.Tpump_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
        ui.Rpump_ON->setEnabled(false);
        ui.IG_ON->setEnabled(true);
    }else{
        //Tpump_on_D = 0x00;
        ui.Tpump_ON->setText( "开分子泵");
        ui.Tpump_ON->setStyleSheet("QPushButton { background-color:LightGray;}");
        ui.Rpump_ON->setEnabled(true);
        ui.IG_ON->setEnabled(false);
    }
    //SetParameter();
}

void uiCtrlTFG::on_Rpump_ON_clicked()
{
    if(ui.Rpump_ON->text()== "开机械泵"){
        //Rpump_on_D = 0xFF;
        ui.Rpump_ON->setText( "关机械泵");
        ui.Rpump_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
        ui.Tpump_ON->setEnabled(true);
        ui.Purge_Gas_ON->setEnabled(false);
    }else{
        //Rpump_on_D = 0x00;
        ui.Rpump_ON->setText( "开机械泵");
        ui.Rpump_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
        ui.Tpump_ON->setEnabled(false);
        ui.Purge_Gas_ON->setEnabled(true);
    }
    //SetParameter();
}

void uiCtrlTFG::on_Auto_ShutDown_clicked()
{
    if(ui.Auto_ShutDown->text()== "自动关真空"){
        //Auto_ShutDown_on_D = 0xFF;
        ui.Auto_ShutDown->setText( "手动关真空");
        ui.Auto_ShutDown->setStyleSheet("QPushButton { background-color:Green; }");
        ui.Rpump_ON->setEnabled(false);
        ui.Tpump_ON->setEnabled(false);
        ui.IG_ON->setEnabled(false);
        ui.Purge_Gas_ON->setEnabled(false);
    }else{
        //Auto_ShutDown_on_D = 0x00;
        ui.Auto_ShutDown->setText( "自动关真空");
        ui.Auto_ShutDown->setStyleSheet("QPushButton { background-color:LightGray; }");
        ui.Tpump_ON->setEnabled(true);
        ui.IG_ON->setEnabled(true);
    }
}

void uiCtrlTFG::on_Auto_StartUp_clicked()
{
    if(ui.Auto_StartUp->text()== "自动开真空"){
        //Auto_StartUP_on_D = 0xFF;
        ui.Auto_StartUp->setText( "手动开真空");
        ui.Auto_StartUp->setStyleSheet("QPushButton { background-color:Green; }");
        ui.Rpump_ON->setEnabled(false);
        ui.Tpump_ON->setEnabled(false);
        ui.IG_ON->setEnabled(false);
        ui.Purge_Gas_ON->setEnabled(false);
    }else{
        //Auto_StartUP_on_D = 0x00;
        ui.Auto_StartUp->setText( "自动开真空");
        ui.Auto_StartUp->setStyleSheet("QPushButton { background-color:LightGray; }");
        ui.Rpump_ON->setEnabled(true);
        ui.Purge_Gas_ON->setEnabled(true);
    }
}

void uiCtrlTFG::on_IG12_RF_ON_clicked()
{
    if(ui.IG12_RF_ON->text()== "IG12_ON"){
        //IG12_RF_on_D = 0xFF;
        ui.IG12_RF_ON->setText( "IG12_OFF");
        ui.IG12_RF_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //IG12_RF_on_D = 0x00;
        ui.IG12_RF_ON->setText( "IG12_ON");
        ui.IG12_RF_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_CC_RF_ON_clicked()
{
    if(ui.CC_RF_ON->text()== "CC_ON"){
        //CC_RF_on_D = 0xFF;
        ui.CC_RF_ON->setText( "CC_OFF");
        ui.CC_RF_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //CC_RF_on_D = 0x00;
        ui.CC_RF_ON->setText( "CC_ON");
        ui.CC_RF_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_Q1_RF_ON_clicked()
{
    if(ui.Q1_RF_ON->text()== "Q1_ON"){
        //Q1_RF_on_D = 0xFF;
        ui.Q1_RF_ON->setText( "Q1_OFF");
        ui.Q1_RF_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //Q1_RF_on_D = 0x00;
        ui.Q1_RF_ON->setText( "Q1_ON");
        ui.Q1_RF_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_Q1_ABMode_clicked()
{
    if(ui.Q1_ABMode->text()== "B模式"){
        //Q1_AB_mode_D = 0xFF;
        ui.Q1_ABMode->setText( "A模式");
    }else{
        //Q1_AB_mode_D = 0x00;
        ui.Q1_ABMode->setText( "B模式");
    }
}

void uiCtrlTFG::on_Q3_RF_on_ctrl_clicked()
{
    if( ui.Q3_RF_on_ctrl->text()== "Q3_ON"){
        //Q3_RF_on_D = 0xFF;
        ui.Q3_RF_on_ctrl->setText( "Q3_OFF");
        ui.Q3_RF_on_ctrl->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //Q3_RF_on_D = 0x00;
        ui.Q3_RF_on_ctrl->setText( "Q3_ON");
        ui.Q3_RF_on_ctrl->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_Q3_ABmode_ctrl_clicked()
{
    if(ui.Q3_ABmode_ctrl->text()== "B模式"){
        //Q3_AB_mode_D = 0xFF;
        ui.Q3_ABmode_ctrl->setText( "A模式");
    }else{
        //Q3_AB_mode_D = 0x00;
        ui.Q3_ABmode_ctrl->setText( "B模式");
    }
}

void uiCtrlTFG::on_Heat_PW_ON_clicked()
{
    //Heat_PW_on = !Heat_PW_on;
    if(ui.Heat_PW_ON->property("sw")==0x00/*Heat_PW_on*/){
        ui.Heat_PW_ON->setProperty("sw", 0xFF);
        //Heat_PW_on_D = 0xFF;
        ui.Heat_PW_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
        ui.Heat_ON->setEnabled(true);
    }else{
        ui.Heat_PW_ON->setProperty("sw", 0x00);
        //Heat_PW_on_D = 0x00;
        ui.Heat_PW_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
        ui.Heat_ON->setEnabled(false);
    }
    //SetParameter();
}

void uiCtrlTFG::on_Heat_ON_clicked()
{
    if(ui.Heat_ON->text()== "加热开"){
        //Heat_on_D = 0xFF;
        ui.Heat_ON->setText( "加热关");
        ui.Heat_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //Heat_on_D = 0x00;
        ui.Heat_ON->setText( "加热开");
        ui.Heat_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
    //SetParameter();
}

void uiCtrlTFG::on_CID_Gas_ON_clicked()
{
    if(ui.CID_Gas_ON->text()=="CIDON"){
        //CID_Gas_on_D = 0xFF;
        ui.CID_Gas_ON->setText( "CIDOFF");
        ui.CID_Gas_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //CID_Gas_on_D = 0x00;
        ui.CID_Gas_ON->setText( "CIDON");
        ui.CID_Gas_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_CID_Rel_ON_clicked()
{
    //CID_Rel_on = !CID_Rel_on;
    if(ui.CID_Rel_ON->property("sw")==0x00/*CID_Rel_on*/){
        ui.CID_Rel_ON->setProperty("sw", 0xFF);
        //CID_Rel_on_D = 0xFF;
        ui.CID_Rel_ON->setStyleSheet("QPushButton { background-color:LightCoral;}");
    }else{
        ui.CID_Rel_ON->setProperty("sw", 0x00);
        //CID_Rel_on_D = 0x00;
        ui.CID_Rel_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_ESI_HV_ON_clicked()
{
    if(ui.ESI_HV_ON->text()=="ESI_ON"){
        //ESI_HV_on_D = 0xFF;
        ui.ESI_HV_ON->setText( "ESI_OFF");
        ui.ESI_HV_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //ESI_HV_on_D = 0x00;
        ui.ESI_HV_ON->setText( "ESI_ON");
        ui.ESI_HV_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_CUR_HV_ON_clicked()
{
    if(ui.CUR_HV_ON->text()== "CUR_ON"){
        //Curtain_HV_on_D = 0xFF;
        ui.CUR_HV_ON->setText( "CUR_OFF");
        ui.CUR_HV_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //Curtain_HV_on_D = 0x00;
        ui.CUR_HV_ON->setText( "CUR_ON");
        ui.CUR_HV_ON->setStyleSheet("QPushButton { background-color:LightGray;}");
    }
}

void uiCtrlTFG::on_HED_HV_ON_clicked()
{
    if(ui.HED_HV_ON->text()== "HED_ON"){
        //HED_HV_on_D = 0xFF;
        ui.HED_HV_ON->setText( "HED_OFF");
        ui.HED_HV_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //HED_HV_on_D = 0x00;
        ui.HED_HV_ON->setText( "HED_ON");
        ui.HED_HV_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_DET_HV_ON_clicked()
{
    if(ui.DET_HV_ON->text()== "DET_ON"){
        //DET_HV_on_D = 0xFF;
        ui.DET_HV_ON->setText( "DET_OFF");
        ui.DET_HV_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //DET_HV_on_D = 0x00;
        ui.DET_HV_ON->setText( "DET_ON");
        ui.DET_HV_ON->setStyleSheet("QPushButton { background-color:LightGray; }");
    }
}

void uiCtrlTFG::on_IG0_RF_ON_clicked()
{
    if(ui.IG0_RF_ON->text()== "IG0_ON"){
        //IG0_RF_on_D = 0xFF;
        ui.IG0_RF_ON->setText( "IG0_OFF");
        ui.IG0_RF_ON->setStyleSheet("QPushButton { background-color:LightCoral; }");
    }else{
        //IG0_RF_on_D = 0x00;
        ui.IG0_RF_ON->setText( "IG0_ON");
        ui.IG0_RF_ON->setStyleSheet("QPushButton { background-color:LightGray;}");
    }
}

void uiCtrlTFG::on_update_SerialPort_clicked()
{
    //serialPort1->close();
    ui.comboBox1->clear();
    QString description;
    QString manufacturer;
    QString serialNumber;
    QList<QSerialPortInfo> serialPortInfos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &serialPortInfo : serialPortInfos) {
        QStringList list; // 用于存储串口信息的字符串列表
        description = serialPortInfo.description(); // 获取串口描述
        manufacturer = serialPortInfo.manufacturer(); // 获取制造商信息
        serialNumber = serialPortInfo.serialNumber(); // 获取序列号

        // 将串口的相关信息添加到列表中，如果信息为空则使用blankString代替
        list << serialPortInfo.portName()
             << (!description.isEmpty() ? description : QString())
             << (!manufacturer.isEmpty() ? manufacturer : QString())
             << (!serialNumber.isEmpty() ? serialNumber : QString())
             << serialPortInfo.systemLocation()
             << (serialPortInfo.vendorIdentifier() ? QString::number(serialPortInfo.vendorIdentifier(), 16) : QString())
             << (serialPortInfo.productIdentifier() ? QString::number(serialPortInfo.productIdentifier(), 16) : QString());
        ui.comboBox1->addItem(list.first(), list);
    }
    if(ui.comboBox1->count()>0)
        ui.comboBox1->setCurrentIndex(0);
}

void uiCtrlTFG::on_Open_SerialPort_clicked()
{
//    if(ui.Open_SerialPort->text()== tr("打开\n串口")){
//        QString PortName= ui.comboBox1->currentText();
//        serialPort1->setPortName(PortName); // 设置串口名称

//        //connect(serialPort1, &QSerialPort::readyRead, this, &uiCtrlTFG::serialPort1DataReceived);

//        if (serialPort1->open(QIODevice::ReadWrite)) {

//            ui.comboBox1->setEnabled(false);
//            ui.comboBox2->setEnabled(false);
//            ui.Open_SerialPort->setText(tr("关闭\n串口"));//
//            serialPort1->setBaudRate(115200); // 设置波特率
//            serialPort1->setDataBits(QSerialPort::Data8); // 设置数据位
//            serialPort1->setParity(QSerialPort::NoParity); // 设置奇偶校验位
//            serialPort1->setStopBits(QSerialPort::OneStop); // 设置停止位
//            //serialPort1->setFlowControl(QSerialPort::NoFlowControl); // 设置流控
//            serialPort1->clear(QSerialPort::AllDirections);
//            QObject::connect(serialPort1, SIGNAL(readyRead()), this, SLOT(serialPort1DataReceived()));

//        }
//    }else{
//        serialPort1->close();
//        ui.comboBox1->setEnabled(true);
//        ui.comboBox2->setEnabled(true);
//        ui.Open_SerialPort->setText(tr("打开\n串口"));
//    }
}

void uiCtrlTFG::timerEvent(QTimerEvent *evt)
{
    if(evt->timerId() == timer1){
        serialPort1DataReceived();
    }
}

void uiCtrlTFG::on_Scan_Ctrl_clicked()
{
    QByteArray tempWriteBuff=SetParamCMD();
    mDeviceSerial->openWrite(mDeviceNumber, SerialDeviceManager::SerialCMD(120, tempWriteBuff,
                                                                    checkParamReq, this));
}
