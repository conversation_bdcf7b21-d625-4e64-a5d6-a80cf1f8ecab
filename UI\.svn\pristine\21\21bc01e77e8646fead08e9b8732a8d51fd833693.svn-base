<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiCtrlTFG</class>
 <widget class="QWidget" name="uiCtrlTFG">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1567</width>
    <height>691</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_7">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>温度控制</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QTableWidget" name="UI_TW_TEMP_CTRL_CT">
            <property name="minimumSize">
             <size>
              <width>480</width>
              <height>300</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>480</width>
              <height>300</height>
             </size>
            </property>
            <row>
             <property name="text">
              <string>设定温度</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>实际温度</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>实际温度D</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>P参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>I参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>D参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>PWM</string>
             </property>
            </row>
            <column>
             <property name="text">
              <string>ESI</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>APCI</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>Cutain</string>
             </property>
            </column>
            <item row="0" column="0">
             <property name="text">
              <string>25</string>
             </property>
            </item>
            <item row="0" column="1">
             <property name="text">
              <string>25</string>
             </property>
            </item>
            <item row="0" column="2">
             <property name="text">
              <string>25</string>
             </property>
            </item>
            <item row="1" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="3" column="0">
             <property name="text">
              <string>25</string>
             </property>
            </item>
            <item row="3" column="1">
             <property name="text">
              <string>50</string>
             </property>
            </item>
            <item row="3" column="2">
             <property name="text">
              <string>50</string>
             </property>
            </item>
            <item row="4" column="0">
             <property name="text">
              <string>15</string>
             </property>
            </item>
            <item row="4" column="1">
             <property name="text">
              <string>20</string>
             </property>
            </item>
            <item row="4" column="2">
             <property name="text">
              <string>20</string>
             </property>
            </item>
            <item row="5" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QPushButton" name="Heat_PW_ON">
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="text">
               <string>加热
电源</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_6">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_16">
              <property name="text">
               <string>风扇</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="Heat_Fan_State">
              <property name="styleSheet">
               <string notr="true">QLabel { background-color: LightCoral; }</string>
              </property>
              <property name="text">
               <string>状态</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_7">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="Heat_ON">
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="text">
               <string>加热
开</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_8">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_18">
              <property name="text">
               <string>加热管</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="HeaterID">
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>16777215</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>小</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>大</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="title">
          <string>气流控制</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QTableWidget" name="UI_TW_GAS_CTRL_CT">
            <property name="minimumSize">
             <size>
              <width>600</width>
              <height>300</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>600</width>
              <height>300</height>
             </size>
            </property>
            <row>
             <property name="text">
              <string>设定流量</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>实际流量</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>实际流量D</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>P参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>I参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>D参数</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>PID</string>
             </property>
            </row>
            <column>
             <property name="text">
              <string>加热气</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>雾化气</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>CurtainHV</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>HV_on</string>
             </property>
            </column>
            <item row="0" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="0" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="0" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="0" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="1" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="2" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="3" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="3" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="3" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="3" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="4" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="4" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="4" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="4" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="5" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="0">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="1">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="2">
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item row="6" column="3">
             <property name="text">
              <string>0</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <widget class="QPushButton" name="CID_Gas_ON">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="text">
               <string>CID
ON</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="CID_Rel_ON">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="text">
               <string>CID
Rel</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QGroupBox" name="groupBox_6">
         <property name="title">
          <string>IG_RF控制</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="0" column="1">
           <widget class="QPushButton" name="IG0_RF_ON">
            <property name="text">
             <string>IG0_ON</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLineEdit" name="IG12_RF_Tuning">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="IG0_RF_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>RF状态</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLineEdit" name="IG0_RF_Tuning">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QLabel" name="IG12_RF_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>RF状态</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="IG12_RF_ON">
            <property name="text">
             <string>IG12_ON</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1" colspan="2">
           <widget class="QLabel" name="IG_Fan_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>风扇状态</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="0" rowspan="4">
           <widget class="QLabel" name="label_19">
            <property name="text">
             <string>Tuning</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_7">
         <property name="title">
          <string>CC_RF控制</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_5">
          <item row="0" column="0">
           <widget class="QPushButton" name="CC_RF_ON">
            <property name="text">
             <string>CC_ON</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLineEdit" name="CC_RF_Tuning">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_25">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>RF状态</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="CC_Fan_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>风扇状态</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_9">
         <property name="title">
          <string>Q1_RF控制</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_6">
          <item row="0" column="1">
           <widget class="QPushButton" name="Q1_ABMode">
            <property name="text">
             <string>A模式</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="Q1_RF_ON">
            <property name="text">
             <string>Q1_ON</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLineEdit" name="Q1_FB_Temp">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLineEdit" name="Q1_RF_Tuning">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="Q1_Fan_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>风扇状态</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_28">
            <property name="text">
             <string>Tuning</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_29">
            <property name="text">
             <string>反馈温度</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="Q1_RF_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>RF状态</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_10">
         <property name="title">
          <string>Q3_RF控制</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_8">
          <item row="0" column="1">
           <widget class="QPushButton" name="Q3_ABmode_ctrl">
            <property name="text">
             <string>A模式</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_31">
            <property name="text">
             <string>Tuning</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLineEdit" name="Q3_FB_Temp">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_32">
            <property name="text">
             <string>反馈温度</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="Q3_RF_on_ctrl">
            <property name="text">
             <string>Q3_ON</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLineEdit" name="Q3_RF_Tuning">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="Q3_Fan_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>风扇状态</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="Q3_RF_State">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>RF状态</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_8">
         <property name="title">
          <string>离子源</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_7">
          <item row="2" column="0">
           <widget class="QLineEdit" name="IS_ID">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_21">
            <property name="text">
             <string>离子源</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="IS_Door">
            <property name="styleSheet">
             <string notr="true">QLabel { background-color: LightCoral; }</string>
            </property>
            <property name="text">
             <string>离子门</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <property name="rightMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QGroupBox" name="groupBox_11">
         <property name="title">
          <string>Lens_DC监控</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_9">
          <item row="1" column="2">
           <widget class="QLineEdit" name="Lens_DC3">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QLabel" name="label_40">
            <property name="text">
             <string>DC6</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLineEdit" name="Lens_DC1">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QLabel" name="label_45">
            <property name="text">
             <string>DC11</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="label_38">
            <property name="text">
             <string>DC4</string>
            </property>
           </widget>
          </item>
          <item row="2" column="5">
           <widget class="QLabel" name="label_48">
            <property name="text">
             <string>DC14</string>
            </property>
           </widget>
          </item>
          <item row="2" column="7">
           <widget class="QLabel" name="label_50">
            <property name="text">
             <string>DC16</string>
            </property>
           </widget>
          </item>
          <item row="0" column="6">
           <widget class="QLabel" name="label_41">
            <property name="text">
             <string>DC7</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="label_39">
            <property name="text">
             <string>DC5</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_35">
            <property name="text">
             <string>DC1</string>
            </property>
           </widget>
          </item>
          <item row="1" column="4">
           <widget class="QLineEdit" name="Lens_DC5">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLineEdit" name="Lens_DC4">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="label_44">
            <property name="text">
             <string>DC10</string>
            </property>
           </widget>
          </item>
          <item row="1" column="7">
           <widget class="QLineEdit" name="Lens_DC8">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="6">
           <widget class="QLabel" name="label_49">
            <property name="text">
             <string>DC15</string>
            </property>
           </widget>
          </item>
          <item row="1" column="5">
           <widget class="QLineEdit" name="Lens_DC6">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="4">
           <widget class="QLabel" name="label_47">
            <property name="text">
             <string>DC13</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_43">
            <property name="text">
             <string>DC9</string>
            </property>
           </widget>
          </item>
          <item row="1" column="6">
           <widget class="QLineEdit" name="Lens_DC7">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLineEdit" name="Lens_DC2">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="label_36">
            <property name="text">
             <string>DC2</string>
            </property>
           </widget>
          </item>
          <item row="0" column="7">
           <widget class="QLabel" name="label_42">
            <property name="text">
             <string>DC8</string>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QLabel" name="label_46">
            <property name="text">
             <string>DC12</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_37">
            <property name="text">
             <string>DC3</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLineEdit" name="Lens_DC9">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLineEdit" name="Lens_DC10">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QLineEdit" name="Lens_DC11">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QLineEdit" name="Lens_DC12">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="4">
           <widget class="QLineEdit" name="Lens_DC13">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="5">
           <widget class="QLineEdit" name="Lens_DC14">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="6">
           <widget class="QLineEdit" name="Lens_DC15">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="7">
           <widget class="QLineEdit" name="Lens_DC16">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <item>
          <widget class="QPushButton" name="Monitor_ON">
           <property name="minimumSize">
            <size>
             <width>132</width>
             <height>56</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton { background-color: LightGray; }</string>
           </property>
           <property name="text">
            <string>打开电压监控</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="Scan_Ctrl">
           <property name="minimumSize">
            <size>
             <width>132</width>
             <height>56</height>
            </size>
           </property>
           <property name="text">
            <string>下发参数</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_5">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QLineEdit" name="COMLen"/>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="title">
        <string>真空控制</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QPushButton" name="Auto_StartUp">
          <property name="text">
           <string>自动开真空</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QPushButton" name="Rpump_ON">
          <property name="text">
           <string>开机械泵</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLabel" name="label_13">
          <property name="text">
           <string>PG真空度</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QPushButton" name="IG_ON">
          <property name="text">
           <string>开IG</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QPushButton" name="Auto_ShutDown">
          <property name="text">
           <string>自动关真空</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLineEdit" name="PG_P">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QPushButton" name="Tpump_ON">
          <property name="text">
           <string>开分子泵</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLineEdit" name="Tpump_Frq">
          <property name="styleSheet">
           <string notr="true">QLineEdit { background-color: LightGray; }</string>
          </property>
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0" colspan="2">
         <widget class="QLabel" name="Tpump_Fan_State">
          <property name="styleSheet">
           <string notr="true">QLabel { background-color: LightCoral; }</string>
          </property>
          <property name="text">
           <string>分子泵风扇状态</string>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="label_12">
          <property name="text">
           <string>分子泵转速</string>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QLineEdit" name="IG_P">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QPushButton" name="Purge_Gas_ON">
          <property name="text">
           <string>开进气</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QLabel" name="label_14">
          <property name="text">
           <string>IG真空度</string>
          </property>
         </widget>
        </item>
        <item row="4" column="2">
         <widget class="QLineEdit" name="Rpump_Frq">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_4">
       <property name="title">
        <string>高压控制</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="2" column="3">
         <widget class="QLabel" name="label_5">
          <property name="text">
           <string>V</string>
          </property>
         </widget>
        </item>
        <item row="3" column="3">
         <widget class="QLabel" name="label_6">
          <property name="text">
           <string>V</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLabel" name="DET_HV_State">
          <property name="styleSheet">
           <string notr="true">QLabel { background-color: LightCoral; }</string>
          </property>
          <property name="text">
           <string>状态</string>
          </property>
         </widget>
        </item>
        <item row="4" column="3">
         <widget class="QLabel" name="label_7">
          <property name="text">
           <string>V</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>V</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QLineEdit" name="ESI_HV_IFB">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="CUR_HV_State">
          <property name="styleSheet">
           <string notr="true">QLabel { background-color: LightCoral; }</string>
          </property>
          <property name="text">
           <string>状态</string>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>uA</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QLineEdit" name="ESI_HV_VFB">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QLineEdit" name="Curtain_HV_VFB">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QLineEdit" name="HED_HV_VFB">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="HED_HV_State">
          <property name="styleSheet">
           <string notr="true">QLabel { background-color: LightCoral; }</string>
          </property>
          <property name="text">
           <string>状态</string>
          </property>
         </widget>
        </item>
        <item row="4" column="2">
         <widget class="QLineEdit" name="DET_HV_VFB">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QPushButton" name="CUR_HV_ON">
          <property name="text">
           <string>CUR_ON</string>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QPushButton" name="HED_HV_ON">
          <property name="text">
           <string>HED_ON</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QPushButton" name="DET_HV_ON">
          <property name="text">
           <string>DET_ON</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0" rowspan="2">
         <widget class="QPushButton" name="ESI_HV_ON">
          <property name="text">
           <string>ESI_ON</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1" rowspan="2">
         <widget class="QLabel" name="ESI_HV_State">
          <property name="styleSheet">
           <string notr="true">QLabel { background-color: LightCoral; }</string>
          </property>
          <property name="text">
           <string>状态</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_5">
       <property name="title">
        <string>串口设置</string>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="1" column="0">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>波特率</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QComboBox" name="comboBox2"/>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="comboBox1"/>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>端口号</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="Open_SerialPort">
          <property name="minimumSize">
           <size>
            <width>56</width>
            <height>56</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>56</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="text">
           <string>打开
串口</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QPushButton" name="update_SerialPort">
          <property name="maximumSize">
           <size>
            <width>56</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="text">
           <string>刷新</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
