/********************************************************************************
** Form generated from reading UI file 'sConfirmWidget.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SCONFIRMWIDGET_H
#define UI_SCONFIRMWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sConfirmWidget
{
public:

    void setupUi(QWidget *sConfirmWidget)
    {
        if (sConfirmWidget->objectName().isEmpty())
            sConfirmWidget->setObjectName(QString::fromUtf8("sConfirmWidget"));
        sConfirmWidget->resize(506, 420);

        retranslateUi(sConfirmWidget);

        QMetaObject::connectSlotsByName(sConfirmWidget);
    } // setupUi

    void retranslateUi(QWidget *sConfirmWidget)
    {
        sConfirmWidget->setWindowTitle(QApplication::translate("sConfirmWidget", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class sConfirmWidget: public Ui_sConfirmWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SCONFIRMWIDGET_H
