/****************************************************************************
** Meta object code from reading C++ file 'uiQ13ScanParamEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiSingleAcquisition/uiQ13ScanParamEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiQ13ScanParamEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiQ13ScanParamEditor_t {
    QByteArrayData data[8];
    char stringdata0[197];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiQ13ScanParamEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiQ13ScanParamEditor_t qt_meta_stringdata_uiQ13ScanParamEditor = {
    {
QT_MOC_LITERAL(0, 0, 20), // "uiQ13ScanParamEditor"
QT_MOC_LITERAL(1, 21, 28), // "on_lineEdit_endMZ_textEdited"
QT_MOC_LITERAL(2, 50, 0), // ""
QT_MOC_LITERAL(3, 51, 4), // "arg1"
QT_MOC_LITERAL(4, 56, 30), // "on_lineEdit_startMZ_textEdited"
QT_MOC_LITERAL(5, 87, 32), // "on_lineEdit_pauseTime_textEdited"
QT_MOC_LITERAL(6, 120, 43), // "on_lineEdit_Polarity_switch_t..."
QT_MOC_LITERAL(7, 164, 32) // "on_lineEdit_eventTime_textEdited"

    },
    "uiQ13ScanParamEditor\0on_lineEdit_endMZ_textEdited\0"
    "\0arg1\0on_lineEdit_startMZ_textEdited\0"
    "on_lineEdit_pauseTime_textEdited\0"
    "on_lineEdit_Polarity_switch_time_textEdited\0"
    "on_lineEdit_eventTime_textEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiQ13ScanParamEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x08 /* Private */,
       4,    1,   42,    2, 0x08 /* Private */,
       5,    1,   45,    2, 0x08 /* Private */,
       6,    1,   48,    2, 0x08 /* Private */,
       7,    1,   51,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,

       0        // eod
};

void uiQ13ScanParamEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiQ13ScanParamEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_lineEdit_endMZ_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->on_lineEdit_startMZ_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->on_lineEdit_pauseTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->on_lineEdit_Polarity_switch_time_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->on_lineEdit_eventTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiQ13ScanParamEditor::staticMetaObject = { {
    &uiBaseParamEditor::staticMetaObject,
    qt_meta_stringdata_uiQ13ScanParamEditor.data,
    qt_meta_data_uiQ13ScanParamEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiQ13ScanParamEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiQ13ScanParamEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiQ13ScanParamEditor.stringdata0))
        return static_cast<void*>(this);
    return uiBaseParamEditor::qt_metacast(_clname);
}

int uiQ13ScanParamEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = uiBaseParamEditor::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
