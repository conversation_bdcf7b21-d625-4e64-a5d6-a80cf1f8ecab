/********************************************************************************
** Form generated from reading UI file 'uiCalibrationView.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UICALIBRATIONVIEW_H
#define UI_UICALIBRATIONVIEW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiCalibrationView
{
public:
    QGridLayout *gridLayout;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_2;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_5;
    QLineEdit *UI_LE_SPEED_CAL;
    QPushButton *UI_PB_ADDMASS_CAL;
    QSpacerItem *verticalSpacer;
    QPushButton *UI_PB_DELETEMASS_CAL;
    QSpacerItem *verticalSpacer_2;
    QGroupBox *groupBox_3;
    QVBoxLayout *verticalLayout_8;
    QWidget *UI_W_ACQ_CAL;
    QHBoxLayout *UI_LAYOUT_ACQ_CAL;
    QVBoxLayout *verticalLayout_9;
    QWidget *UI_W_METHOD_CAL;
    QVBoxLayout *UI_LAYOUT_METHOD_CAL;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label;
    QComboBox *UI_CB_ACQ_MODE_CAL;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *UI_PB_ADVANCE_SINGLEACQ;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_2;
    QLineEdit *UI_LE_AcqFreq_CAL;
    QLabel *label_3;
    QLineEdit *UI_LE_AcqACC_CAL;
    QScrollArea *scrollArea;
    QWidget *UI_SA_SPEEDMAP_CAL;
    QVBoxLayout *UI_LAYOUT_SPEEDMAP_CAL;

    void setupUi(QWidget *uiCalibrationView)
    {
        if (uiCalibrationView->objectName().isEmpty())
            uiCalibrationView->setObjectName(QString::fromUtf8("uiCalibrationView"));
        uiCalibrationView->resize(1094, 590);
        gridLayout = new QGridLayout(uiCalibrationView);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        widget_2 = new QWidget(uiCalibrationView);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMaximumSize(QSize(128, 16777215));
        verticalLayout_2 = new QVBoxLayout(widget_2);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        groupBox_2 = new QGroupBox(widget_2);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setMaximumSize(QSize(16777215, 80));
        verticalLayout_5 = new QVBoxLayout(groupBox_2);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(0, 0, 0, 0);
        UI_LE_SPEED_CAL = new QLineEdit(groupBox_2);
        UI_LE_SPEED_CAL->setObjectName(QString::fromUtf8("UI_LE_SPEED_CAL"));
        UI_LE_SPEED_CAL->setMaximumSize(QSize(16777215, 16777215));

        verticalLayout_5->addWidget(UI_LE_SPEED_CAL);

        UI_PB_ADDMASS_CAL = new QPushButton(groupBox_2);
        UI_PB_ADDMASS_CAL->setObjectName(QString::fromUtf8("UI_PB_ADDMASS_CAL"));

        verticalLayout_5->addWidget(UI_PB_ADDMASS_CAL);


        verticalLayout_2->addWidget(groupBox_2);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);

        UI_PB_DELETEMASS_CAL = new QPushButton(widget_2);
        UI_PB_DELETEMASS_CAL->setObjectName(QString::fromUtf8("UI_PB_DELETEMASS_CAL"));

        verticalLayout_2->addWidget(UI_PB_DELETEMASS_CAL);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer_2);


        gridLayout->addWidget(widget_2, 0, 2, 1, 1);

        groupBox_3 = new QGroupBox(uiCalibrationView);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        verticalLayout_8 = new QVBoxLayout(groupBox_3);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        UI_W_ACQ_CAL = new QWidget(groupBox_3);
        UI_W_ACQ_CAL->setObjectName(QString::fromUtf8("UI_W_ACQ_CAL"));
        UI_LAYOUT_ACQ_CAL = new QHBoxLayout(UI_W_ACQ_CAL);
        UI_LAYOUT_ACQ_CAL->setObjectName(QString::fromUtf8("UI_LAYOUT_ACQ_CAL"));
        UI_LAYOUT_ACQ_CAL->setContentsMargins(0, 0, 0, 0);
        verticalLayout_9 = new QVBoxLayout();
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        UI_W_METHOD_CAL = new QWidget(UI_W_ACQ_CAL);
        UI_W_METHOD_CAL->setObjectName(QString::fromUtf8("UI_W_METHOD_CAL"));
        UI_W_METHOD_CAL->setMaximumSize(QSize(320, 16777215));
        UI_LAYOUT_METHOD_CAL = new QVBoxLayout(UI_W_METHOD_CAL);
        UI_LAYOUT_METHOD_CAL->setObjectName(QString::fromUtf8("UI_LAYOUT_METHOD_CAL"));
        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label = new QLabel(UI_W_METHOD_CAL);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout_4->addWidget(label);

        UI_CB_ACQ_MODE_CAL = new QComboBox(UI_W_METHOD_CAL);
        UI_CB_ACQ_MODE_CAL->addItem(QString());
        UI_CB_ACQ_MODE_CAL->addItem(QString());
        UI_CB_ACQ_MODE_CAL->setObjectName(QString::fromUtf8("UI_CB_ACQ_MODE_CAL"));

        horizontalLayout_4->addWidget(UI_CB_ACQ_MODE_CAL);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_3);

        UI_PB_ADVANCE_SINGLEACQ = new QPushButton(UI_W_METHOD_CAL);
        UI_PB_ADVANCE_SINGLEACQ->setObjectName(QString::fromUtf8("UI_PB_ADVANCE_SINGLEACQ"));

        horizontalLayout_4->addWidget(UI_PB_ADVANCE_SINGLEACQ);


        UI_LAYOUT_METHOD_CAL->addLayout(horizontalLayout_4);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        label_2 = new QLabel(UI_W_METHOD_CAL);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout_5->addWidget(label_2);

        UI_LE_AcqFreq_CAL = new QLineEdit(UI_W_METHOD_CAL);
        UI_LE_AcqFreq_CAL->setObjectName(QString::fromUtf8("UI_LE_AcqFreq_CAL"));

        horizontalLayout_5->addWidget(UI_LE_AcqFreq_CAL);

        label_3 = new QLabel(UI_W_METHOD_CAL);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout_5->addWidget(label_3);

        UI_LE_AcqACC_CAL = new QLineEdit(UI_W_METHOD_CAL);
        UI_LE_AcqACC_CAL->setObjectName(QString::fromUtf8("UI_LE_AcqACC_CAL"));

        horizontalLayout_5->addWidget(UI_LE_AcqACC_CAL);


        UI_LAYOUT_METHOD_CAL->addLayout(horizontalLayout_5);


        verticalLayout_9->addWidget(UI_W_METHOD_CAL);


        UI_LAYOUT_ACQ_CAL->addLayout(verticalLayout_9);


        verticalLayout_8->addWidget(UI_W_ACQ_CAL);


        gridLayout->addWidget(groupBox_3, 1, 1, 1, 2);

        scrollArea = new QScrollArea(uiCalibrationView);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setWidgetResizable(true);
        UI_SA_SPEEDMAP_CAL = new QWidget();
        UI_SA_SPEEDMAP_CAL->setObjectName(QString::fromUtf8("UI_SA_SPEEDMAP_CAL"));
        UI_SA_SPEEDMAP_CAL->setGeometry(QRect(0, 0, 957, 462));
        UI_LAYOUT_SPEEDMAP_CAL = new QVBoxLayout(UI_SA_SPEEDMAP_CAL);
        UI_LAYOUT_SPEEDMAP_CAL->setObjectName(QString::fromUtf8("UI_LAYOUT_SPEEDMAP_CAL"));
        scrollArea->setWidget(UI_SA_SPEEDMAP_CAL);

        gridLayout->addWidget(scrollArea, 0, 1, 1, 1);


        retranslateUi(uiCalibrationView);

        QMetaObject::connectSlotsByName(uiCalibrationView);
    } // setupUi

    void retranslateUi(QWidget *uiCalibrationView)
    {
        uiCalibrationView->setWindowTitle(QApplication::translate("uiCalibrationView", "Form", nullptr));
        groupBox_2->setTitle(QApplication::translate("uiCalibrationView", "From Speed", nullptr));
        UI_LE_SPEED_CAL->setText(QApplication::translate("uiCalibrationView", "0", nullptr));
        UI_PB_ADDMASS_CAL->setText(QApplication::translate("uiCalibrationView", "Add", nullptr));
        UI_PB_DELETEMASS_CAL->setText(QApplication::translate("uiCalibrationView", "Delete", nullptr));
        groupBox_3->setTitle(QApplication::translate("uiCalibrationView", "Acquisition", nullptr));
        label->setText(QApplication::translate("uiCalibrationView", "AcquisitionMode:", nullptr));
        UI_CB_ACQ_MODE_CAL->setItemText(0, QApplication::translate("uiCalibrationView", "ADC", nullptr));
        UI_CB_ACQ_MODE_CAL->setItemText(1, QApplication::translate("uiCalibrationView", "TDC", nullptr));

        UI_PB_ADVANCE_SINGLEACQ->setText(QApplication::translate("uiCalibrationView", "Advance", nullptr));
        label_2->setText(QApplication::translate("uiCalibrationView", "AcqFreq(Hz):", nullptr));
        UI_LE_AcqFreq_CAL->setText(QApplication::translate("uiCalibrationView", "1000000", nullptr));
        label_3->setText(QApplication::translate("uiCalibrationView", "AcqACC:", nullptr));
        UI_LE_AcqACC_CAL->setText(QApplication::translate("uiCalibrationView", "32", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiCalibrationView: public Ui_uiCalibrationView {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UICALIBRATIONVIEW_H
