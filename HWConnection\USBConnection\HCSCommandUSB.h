﻿#pragma once

#include <QObject>
#include <cParamCCS.h>

#include "sCommandUSB.h"
#include "UMAStructure/UMA_HCS_Data.h"
#include "UMAStructure/UMAEventSegmentSplitter.h"
#include "USBConnection/HCSCommunicationCMD.h"

namespace UMA_HCS {

/**
 * @brief The HCSCommandUSB class
 * 用于HCS DAQ数据处理
 */
class /*HWCONNECTION_EXPORT*/ HCSCommandUSB : public CommandUSB
{
    Q_OBJECT
public:
    typedef struct _HCS_DAQ_DATA
    {
        QMutex qLock;
        uint64_t channel;
        //double dTIC;
        HCSDataFrame frame;
    } HCS_DAQ_DATA;
public:
    HCSCommandUSB(QObject* parent=nullptr);

//    void setEventConfig(const QList<UMA_Event>& umaEvents, const QString& methodPath = QString());
//    const QList<UMA_Event>& eventConfig() const;

//    int startDAQ(ParamCCS::_DAQ_CONFIG& p_DAQ_CONFIG, bool saveFile=false) = delete;
    int getDataDAQ(_DAQ_DATA* p_DAQ_DATA, bool always = false) = delete;
    int getDataDAQ(QByteArray& srcData, bool always = false) = delete;
    int getDataDAQ(std::vector<double>& srcData, bool always = false) = delete;
    int getDataDAQ(char** srcData, uint& nLength, bool always) = delete;

    /**
     * @brief getDataDAQ
     * Get a structure with a whole UMA scan data
     * @return the status
     */
    bool getDataDAQ(HCSDataFrame* frame);
    int startDAQ(libControlCCS::ParamCCS::_DAQ_CONFIG& p_DAQ_CONFIG, _StreamBody::Type_Data typeData);
//    int scanCCS(char* pData, uint nLength) = delete;
//    int scanHCS(char* pData, uint nLength){
//        return CommandUSB::scanCCS(pData, nLength);
//    }
//    int startCCS() = delete;
//    int startHCS(){
//        return CommandUSB::startCCS();
//    }
//    int stopCCS() = delete;
//    int stopHCS(){
//        return CommandUSB::stopCCS();
//    }

    std::shared_ptr<UMAEventSegmentSplitter> getEvSegSplitterPtr() const;

private:
    static int processHCSDataThread(void *pParam, const bool &bRunning);
    HCSCommunicationCMD* mCommunicationHandle;
    uint64_t mChannelGet;
    HCS_DAQ_DATA mHCSSampledData;
//    QList<UMA_Event> mUMAEventConfig;
//    HCSDataFileMeta mDataFileMeta;
    std::shared_ptr<UMAEventSegmentSplitter> mEvSegSplitterPtr;
    //UMA::UMADataIOv2* mDataIOPtr;
protected:
    virtual void createCommunicationHandle() override;
};

}


