#ifndef UIEDITMRM_H
#define UIEDITMRM_H

#include <QWidget>

namespace Ui {
class uiEditMRM;
}

class uiEditMRM : public QWidget
{
    Q_OBJECT

public:
    explicit uiEditMRM(QWidget *parent = nullptr);
    ~uiEditMRM();
    void setParam(QString& str){

    }
    QString getParam(){
        QString str;
        return str;
    }
private slots:
    void on_comboBox_2_activated(int index);

private:
    Ui::uiEditMRM *ui;
};

#endif // UIEDITMRM_H
