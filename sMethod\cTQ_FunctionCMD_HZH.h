#pragma once
#include <QtGlobal>
#include <QByteArray>
#include <QList>

#include <uiMethod/cTQ_StructCMD_HZH.h>

class cTQ_FunctionCMD_HZH
{
public:
    cTQ_FunctionCMD_HZH();
    static int cTQ_CMD_MS_Start(QByteArray& pBuffer, bool extTrigger= false);
    static int cTQ_CMD_MS_Start(QVector<quint32>& p<PERSON><PERSON><PERSON>, bool extTrigger= false);
    static int cTQ_CMD_MS_Halt(QByteArray& pBuffer);
    static int cTQ_CMD_MS_Halt(QVector<quint32>& pBuffer);
    static int cTQ_CMD_MS_Configure(QVector<quint32>& pBuffer);
    //static int cTQ_CMD_MS_Configure(QVector<quint32>& pBuffer);
//    static int cTQ_CMD_MS_Event_Param_Set(QVector<quint32>& pBuffer,
//                                          _EVENT_PARAM_SET& pEVENT_PARAM_SET);
    static int cTQ_CMD_MS_Event_Param_Set(QByteArray& pBuffer,
                                          cTQ_StructCMD_HZH::_EVENT_PARAM_SET& pEVENT_PARAM_SET);
    static int cTQ_CMD_MS_MZ_Param_Set(QByteArray& pBuffer,
                                       QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET>& list_MZ_PARAM_SET);
    static int linearInterpolation(cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                   const QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET>& list_MZ_PARAM_SET);
    static int linearInterpolation(const cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type,
                                   cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& pChannel,
                                   const QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET>& list_MZ_PARAM_SET);

    static void Checksum(QVector<quint32>& pBuffer);
    static void Checksum(QByteArray& pBuffer);
    static quint32 checkSum(quint32* data, size_t size);

private:
    static QByteArray createEvent(cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM,
                                  quint32& currentFrame,
                                  quint32& currentEvent);
    static QByteArray createParamFrameMZ(cTQ_StructCMD_HZH::_MZ_PARAM_SET& p_MZ_PARAM_SET,
                                  quint32& currentFrame);
};

