#pragma once

#include <QFile>
#include <QWidget>



class cTuneXML: public QWidget
{
    Q_OBJECT
public:
    cTuneXML(QWidget *parent = nullptr);
    ~cTuneXML(){
        if(mFile){
            try {
                mFile->close();
            } catch (...) {}
            delete mFile;
            mFile= nullptr;
        }
    }
    void domRead(QString fileName);
    void domWrite();
private:
    QFile* mFile= nullptr;
};

