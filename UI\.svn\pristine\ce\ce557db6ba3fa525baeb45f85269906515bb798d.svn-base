#pragma once

#include "uiMainWindow.h"

class uiMainWindow;
class uiInitMainWindow : public QObject
{
    Q_OBJECT
public:
    uiInitMainWindow(uiMainWindow *p);
    ~uiInitMainWindow();
    friend class uiMainWindow;

private:
    uiMainWindow* pMainWindow= nullptr;
    uiWindowMenu* mWindowMenu = nullptr;

private:
    void initUI();
    void createTitle(){}
    void createMenuBar();
    void createToolBar();

private slots:
    void onUI_MPB_MENU_MAIN_MAINW();
    void onUI_MPB_MENU_SYSTEM_MAINW();
    void onUI_MPB_MENU_MANUAL_MAINW();
    void onUI_MPB_LIST_MAINW();
    void onUI_MPB_MENU_CREATE_LIST_MAINW();
    void onUI_MPB_MENU_SINAL_SCAN_MAINW();
    void onUI_MPB_MENU_TUNE_MAINW();
    void onUI_MPB_MENU_STATE_MAINW();
};
