#include "sCalibrationMass.h"

#include <QSettings>


sCalibrationMass::sCalibrationMass(sMapSetMZ* pMapSetMZ,
                                   QWidget *parent) :
    uiCalibrationMass(pMapSetMZ, parent)
{

}

sCalibrationMass::~sCalibrationMass()
{

}

void sCalibrationMass::initClass(QString& filePath)
{
    mCalibrationViewQ1POS= new sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS, (sMapSetMZ*)mMapSetMZ, this);
    mCalibrationViewQ1POS->initClass(filePath);
    mCalibrationViewQ1NEG= new sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG, (sMapSetMZ*)mMapSetMZ, this);
    mCalibrationViewQ1NEG->initClass(filePath);
    mCalibrationViewQ3POS= new sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS, (sMapSetMZ*)mMapSetMZ, this);
    mCalibrationViewQ3POS->initClass(filePath);
    mCalibrationViewQ3NEG= new sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG, (sMapSetMZ*)mMapSetMZ, this);
    mCalibrationViewQ3NEG->initClass(filePath);
    initUI(filePath);
}

