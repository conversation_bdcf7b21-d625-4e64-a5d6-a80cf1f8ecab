/****************************************************************************
** Meta object code from reading C++ file 'STCalandarWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../LibWidget/sCalandar/STCalandarWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'STCalandarWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_STCalandarWidget_t {
    QByteArrayData data[9];
    char stringdata0[109];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_STCalandarWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_STCalandarWidget_t qt_meta_stringdata_STCalandarWidget = {
    {
QT_MOC_LITERAL(0, 0, 16), // "STCalandarWidget"
QT_MOC_LITERAL(1, 17, 16), // "DateSelectSignal"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 4), // "date"
QT_MOC_LITERAL(4, 40, 14), // "HaveDateSelect"
QT_MOC_LITERAL(5, 55, 12), // "JumpLastYear"
QT_MOC_LITERAL(6, 68, 13), // "JumpLastMonth"
QT_MOC_LITERAL(7, 82, 13), // "JumpNextMonth"
QT_MOC_LITERAL(8, 96, 12) // "JumpNextYear"

    },
    "STCalandarWidget\0DateSelectSignal\0\0"
    "date\0HaveDateSelect\0JumpLastYear\0"
    "JumpLastMonth\0JumpNextMonth\0JumpNextYear"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_STCalandarWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    1,   47,    2, 0x0a /* Public */,
       5,    0,   50,    2, 0x0a /* Public */,
       6,    0,   51,    2, 0x0a /* Public */,
       7,    0,   52,    2, 0x0a /* Public */,
       8,    0,   53,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QDate,    3,

 // slots: parameters
    QMetaType::Void, QMetaType::QDate,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void STCalandarWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<STCalandarWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->DateSelectSignal((*reinterpret_cast< QDate(*)>(_a[1]))); break;
        case 1: _t->HaveDateSelect((*reinterpret_cast< QDate(*)>(_a[1]))); break;
        case 2: _t->JumpLastYear(); break;
        case 3: _t->JumpLastMonth(); break;
        case 4: _t->JumpNextMonth(); break;
        case 5: _t->JumpNextYear(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (STCalandarWidget::*)(QDate );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&STCalandarWidget::DateSelectSignal)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject STCalandarWidget::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_STCalandarWidget.data,
    qt_meta_data_STCalandarWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *STCalandarWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *STCalandarWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_STCalandarWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int STCalandarWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void STCalandarWidget::DateSelectSignal(QDate _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
