#pragma once

#include <QWidget>
#include <qUiWidget.h>
#include "ui_uiMapSetMZ.h"
#include <uiMethod/cTQ_StructCMD_AD81416.h>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <QTableWidget>
#include <cGlobalStruct.h>
#include <LibWidget/SpreadSheet/spreadsheet.h>
#include <uiTools/mzvoltagetableeditor.h>

class uiMapSetMZ : public qUiWidget
{
    Q_OBJECT

public:
    struct _STRUCT_ADJUST_DAC{
        QVector<double> gainDAC, offsetDAC;
        int size(){
            return gainDAC.size()> offsetDAC.size()? offsetDAC.size(): gainDAC.size();
        }
        void resize(int asize){
            gainDAC.resize(asize);
            offsetDAC.resize(asize);
        }
    };
    explicit uiMapSetMZ(QWidget *parent = nullptr);
    virtual ~uiMapSetMZ();
    virtual void initClass(QString& filePath){
        initUI(filePath);//GlobalConfigParam::getConfigParam()->getFilePathSysIni()
    }
    bool getMzHDACFromTable(QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET>& pList){
        _STRUCT_ADJUST_DAC tmp_STRUCT_ADJUST_DAC;
        if((!getGainOffsetHDAC(tmp_STRUCT_ADJUST_DAC))|| (tmp_STRUCT_ADJUST_DAC.size()!=(NUM_HDAC)))
            return false;
        foreach (auto& p, mlistVoltageTableEditor) {
            if(!p)
                return false;
            cTQ_StructCMD_HZH::_MZ_PARAM_SET tmp_MZ_PARAM_SET;
            HZH::paramMZ_Voltages mzVoltages;
            if(!getMzHDACFromTable(mzVoltages, p))
                return false;
            tmp_MZ_PARAM_SET.Type= static_cast<cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE>(
                        p->property("type").toUInt());
            if(!voltages2MzHDAC(mzVoltages, tmp_STRUCT_ADJUST_DAC, tmp_MZ_PARAM_SET))
                return false;
            pList<< tmp_MZ_PARAM_SET;
        }
        return true;
    }
    bool getMzLDACFromTable(QList<cTQ_StructCMD_AD81416::_MZ_PARAM_SET>& pList){
        _STRUCT_ADJUST_DAC tmp_STRUCT_ADJUST_DAC;
        if((!getGainOffsetLDAC(tmp_STRUCT_ADJUST_DAC))|| (tmp_STRUCT_ADJUST_DAC.size()!=(NUM_LDAC)))
            return false;
        foreach (auto& p, mlistVoltageTableEditor) {
            if(!p)
                return false;
            cTQ_StructCMD_AD81416::_MZ_PARAM_SET tmp_MZ_PARAM_SET;
            HZH::paramMZ_Voltages mzVoltages;
            if(!getMzLDACFromTable(mzVoltages, p))
                return false;
            tmp_MZ_PARAM_SET.Type= static_cast<cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE>(
                        p->property("type").toUInt());
            if(!voltages2MzLDAC(mzVoltages, tmp_STRUCT_ADJUST_DAC, tmp_MZ_PARAM_SET))
                return false;
            pList<< tmp_MZ_PARAM_SET;
        }
        return true;
    }
    bool getGainOffsetHDAC(_STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC/*QVector<double>& gain, QVector<double>& offset*/);
    bool getGainOffsetLDAC(_STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC);

private:
    Ui::uiMapSetMZ ui;
    QString toStringHDAC();
    bool fromString(QString& str);
    /**
     * @brief PreferenceDeveloper::getMzVoltageTablesContent
     * 按行获取表中的存储的mz对应参数表， 遇到mz参数为空时跳过，获取下一行
     * @return
     */
    bool getMzHDACFromTable(HZH::paramMZ_Voltages& paramMZ_Voltages,
                                mzVoltageTableEditor* table);
    bool getMzLDACFromTable(HZH::paramMZ_Voltages& paramMZ_Voltages,
                                mzVoltageTableEditor* table);


signals:
    void mzVoltageMapChanged(const HZH::paramMZ_Voltages& mzVoltages);

private slots:
    void on_UI_PB_SAVE_SETMZ_clicked(){
        ui.UI_PB_SAVE_SETMZ->setEnabled(false);
        ConfigParam* pConfigParam= GlobalConfigParam::getConfigParam();
        QString filePath= pConfigParam->getFilePathSysIni();
        saveIniToFile(filePath);
        ui.UI_PB_SAVE_SETMZ->setEnabled(true);
    }
    void on_UI_PB_UPDATE_SETMZ_clicked(){
        ui.UI_PB_UPDATE_SETMZ->setEnabled(false);
        if(updateMemoryParam())
            updateMZVoltageTablesHeader();
        ui.UI_PB_UPDATE_SETMZ->setEnabled(true);
    }

protected:
    QList<mzVoltageTableEditor*> mlistVoltageTableEditor;
    bool initUI(QString& filePath);
    void updateMZVoltageTablesHeader(/*mzVoltageTableEditor **/);
    void setValue(QTableWidget* tb, int r, int c, double value);
    bool saveIniToFile(QString& filePath);
    bool loadIniFromFile(QString& filePath, QList<mzVoltageTableEditor*>& listVoltageTableEditor);

    virtual bool updateMemoryParam(){
        return false;
    }
    virtual bool voltages2MzHDAC(const HZH::paramMZ_Voltages& ,
                                           const _STRUCT_ADJUST_DAC& ,
                                           cTQ_StructCMD_HZH::_MZ_PARAM_SET&){
        return false;
    }
    virtual bool voltages2MzLDAC(const HZH::paramMZ_Voltages& ,
                                           const _STRUCT_ADJUST_DAC& ,
                                           cTQ_StructCMD_AD81416::_MZ_PARAM_SET&){
        return false;
    }
};

