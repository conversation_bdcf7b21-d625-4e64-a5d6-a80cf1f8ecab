#include "sClickWidget.h"

sClickWidget::sClickWidget(QWidget *parent) :
    QWidget(parent),
    mParent(parent)
{

}

void sClickWidget::mousePressEvent(QMouseEvent *event)
{
    if(!mParent)
        return;
    if(event->button()==Qt::LeftButton){
        if(mWorkWidget)
            relativePos = event->globalPos() - mWorkWidget->pos();
        else
            relativePos = event->globalPos() - mParent->pos();//relativePos = this->pos()- event->globalPos();//
    }
}

void sClickWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if(event->button()==Qt::LeftButton){
        if(relativePos!= QPoint(0,0))
            emit clicked();
        relativePos = QPoint(0,0);
    }
}

void sClickWidget::mouseMoveEvent(QMouseEvent *event)
{
    if(!mWorkWidget)
        return;
    if(relativePos!= QPoint(0,0)){
        if(mWorkWidget->isMaximized()){
            return;
        }
        emit moved();
        mWorkWidget->move(event->globalPos() - relativePos);//mParent->move(event->pos() + relativePos);//mParent->move(event->globalPos() + relativePos);//mParent->move(event->pos() + relativePos);//
    }
}
