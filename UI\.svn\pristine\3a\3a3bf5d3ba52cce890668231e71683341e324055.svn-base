﻿#pragma once

#include <uiMethod/cParamMZ_Voltages.h>
#include <LibWidget/SpreadSheet/spreadsheet.h>
#define NUM_HDAC 12
//#define NUM_LDAC 36
#define NUM_DAC48 48

class mzVoltageTableEditor : public Spreadsheet
{
    Q_OBJECT
public:
    mzVoltageTableEditor(QWidget *parent = nullptr);

    HZH::paramMZ_Voltages curMzVoltageMap() const;
    void setCurMzVoltageMap(const HZH::paramMZ_Voltages &curMzVoltageMap, bool show = true);
    void setValue(int r, int c, double value);

private slots:
    void onCurrentCellChanged(int curRow, int curCol, int preRow, int preCol);
    void updateCurMzVolMapsFromTable();
    void updateCurMzVolMapsToTable(const HZH::paramMZ_Voltages& map);

signals:
    void curMzVoltageMapChanged(const HZH::paramMZ_Voltages& map);

private:
    void createCustomActions() override;

private:
    double m_currentMZ= 0;
    HZH::paramMZ_Voltages m_curMzVoltageMap;
};

