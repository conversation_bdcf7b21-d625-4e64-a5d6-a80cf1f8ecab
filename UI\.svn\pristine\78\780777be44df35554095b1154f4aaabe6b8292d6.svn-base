/********************************************************************************
** Form generated from reading UI file 'uiQ13ScanParamEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIQ13SCANPARAMEDITOR_H
#define UI_UIQ13SCANPARAMEDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiQ13ScanParamEditor
{
public:
    QWidget *uiQ13ScanParamEditorWidget;
    QVBoxLayout *verticalLayout;
    QGridLayout *uiQ13ScanParamEditorLayout;
    QLabel *label_scanSpeed;
    QLineEdit *lineEdit_startMZ;
    QLabel *label_5;
    QLineEdit *lineEdit_scanSpeed_rOnly;
    QLineEdit *lineEdit_endMZ;
    QLabel *label_6;

    void setupUi(QWidget *uiQ13ScanParamEditor)
    {
        if (uiQ13ScanParamEditor->objectName().isEmpty())
            uiQ13ScanParamEditor->setObjectName(QString::fromUtf8("uiQ13ScanParamEditor"));
        uiQ13ScanParamEditor->resize(447, 402);
        uiQ13ScanParamEditorWidget = new QWidget(uiQ13ScanParamEditor);
        uiQ13ScanParamEditorWidget->setObjectName(QString::fromUtf8("uiQ13ScanParamEditorWidget"));
        uiQ13ScanParamEditorWidget->setGeometry(QRect(60, 150, 308, 105));
        verticalLayout = new QVBoxLayout(uiQ13ScanParamEditorWidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        uiQ13ScanParamEditorLayout = new QGridLayout();
        uiQ13ScanParamEditorLayout->setObjectName(QString::fromUtf8("uiQ13ScanParamEditorLayout"));
        uiQ13ScanParamEditorLayout->setContentsMargins(-1, -1, 0, 0);
        label_scanSpeed = new QLabel(uiQ13ScanParamEditorWidget);
        label_scanSpeed->setObjectName(QString::fromUtf8("label_scanSpeed"));
        label_scanSpeed->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        uiQ13ScanParamEditorLayout->addWidget(label_scanSpeed, 2, 0, 1, 1);

        lineEdit_startMZ = new QLineEdit(uiQ13ScanParamEditorWidget);
        lineEdit_startMZ->setObjectName(QString::fromUtf8("lineEdit_startMZ"));

        uiQ13ScanParamEditorLayout->addWidget(lineEdit_startMZ, 0, 1, 1, 2);

        label_5 = new QLabel(uiQ13ScanParamEditorWidget);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        uiQ13ScanParamEditorLayout->addWidget(label_5, 0, 0, 1, 1);

        lineEdit_scanSpeed_rOnly = new QLineEdit(uiQ13ScanParamEditorWidget);
        lineEdit_scanSpeed_rOnly->setObjectName(QString::fromUtf8("lineEdit_scanSpeed_rOnly"));
        lineEdit_scanSpeed_rOnly->setFrame(false);
        lineEdit_scanSpeed_rOnly->setReadOnly(true);

        uiQ13ScanParamEditorLayout->addWidget(lineEdit_scanSpeed_rOnly, 2, 1, 1, 2);

        lineEdit_endMZ = new QLineEdit(uiQ13ScanParamEditorWidget);
        lineEdit_endMZ->setObjectName(QString::fromUtf8("lineEdit_endMZ"));

        uiQ13ScanParamEditorLayout->addWidget(lineEdit_endMZ, 1, 1, 1, 2);

        label_6 = new QLabel(uiQ13ScanParamEditorWidget);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        uiQ13ScanParamEditorLayout->addWidget(label_6, 1, 0, 1, 1);

        uiQ13ScanParamEditorLayout->setColumnStretch(0, 3);

        verticalLayout->addLayout(uiQ13ScanParamEditorLayout);


        retranslateUi(uiQ13ScanParamEditor);

        QMetaObject::connectSlotsByName(uiQ13ScanParamEditor);
    } // setupUi

    void retranslateUi(QWidget *uiQ13ScanParamEditor)
    {
        uiQ13ScanParamEditor->setWindowTitle(QApplication::translate("uiQ13ScanParamEditor", "Form", nullptr));
        label_scanSpeed->setText(QApplication::translate("uiQ13ScanParamEditor", "Scan Speed(u/sec):", nullptr));
        lineEdit_startMZ->setText(QApplication::translate("uiQ13ScanParamEditor", "2", nullptr));
        label_5->setText(QApplication::translate("uiQ13ScanParamEditor", "Start m/z:", nullptr));
        lineEdit_scanSpeed_rOnly->setText(QApplication::translate("uiQ13ScanParamEditor", "60", nullptr));
        lineEdit_endMZ->setText(QApplication::translate("uiQ13ScanParamEditor", "8", nullptr));
        label_6->setText(QApplication::translate("uiQ13ScanParamEditor", "End m/z:", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiQ13ScanParamEditor: public Ui_uiQ13ScanParamEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIQ13SCANPARAMEDITOR_H
