/********************************************************************************
** Form generated from reading UI file 'uiQueue.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIQUEUE_H
#define UI_UIQUEUE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "LibWidget/sMyButton.h"

QT_BEGIN_NAMESPACE

class Ui_uiQueue
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *UI_W_TOOLBAR_BATCH;
    QHBoxLayout *horizontalLayout;
    MyWidget::sMyButton *UI_MB_START_QUEUE;
    MyWidget::sMyButton *UI_MB_STOP_QUEUE;
    MyWidget::sMyButton *UI_MB_PRINT_QUEUE;
    MyWidget::sMyButton *UI_MB_MANAGE_QUEUE;
    QSpacerItem *horizontalSpacer;
    QTableWidget *UI_TW_QUEUE;

    void setupUi(QWidget *uiQueue)
    {
        if (uiQueue->objectName().isEmpty())
            uiQueue->setObjectName(QString::fromUtf8("uiQueue"));
        uiQueue->resize(817, 567);
        verticalLayout = new QVBoxLayout(uiQueue);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        UI_W_TOOLBAR_BATCH = new QWidget(uiQueue);
        UI_W_TOOLBAR_BATCH->setObjectName(QString::fromUtf8("UI_W_TOOLBAR_BATCH"));
        horizontalLayout = new QHBoxLayout(UI_W_TOOLBAR_BATCH);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        UI_MB_START_QUEUE = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_START_QUEUE->setObjectName(QString::fromUtf8("UI_MB_START_QUEUE"));

        horizontalLayout->addWidget(UI_MB_START_QUEUE);

        UI_MB_STOP_QUEUE = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_STOP_QUEUE->setObjectName(QString::fromUtf8("UI_MB_STOP_QUEUE"));

        horizontalLayout->addWidget(UI_MB_STOP_QUEUE);

        UI_MB_PRINT_QUEUE = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_PRINT_QUEUE->setObjectName(QString::fromUtf8("UI_MB_PRINT_QUEUE"));

        horizontalLayout->addWidget(UI_MB_PRINT_QUEUE);

        UI_MB_MANAGE_QUEUE = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_MANAGE_QUEUE->setObjectName(QString::fromUtf8("UI_MB_MANAGE_QUEUE"));

        horizontalLayout->addWidget(UI_MB_MANAGE_QUEUE);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        verticalLayout->addWidget(UI_W_TOOLBAR_BATCH);

        UI_TW_QUEUE = new QTableWidget(uiQueue);
        UI_TW_QUEUE->setObjectName(QString::fromUtf8("UI_TW_QUEUE"));

        verticalLayout->addWidget(UI_TW_QUEUE);


        retranslateUi(uiQueue);

        QMetaObject::connectSlotsByName(uiQueue);
    } // setupUi

    void retranslateUi(QWidget *uiQueue)
    {
        uiQueue->setWindowTitle(QApplication::translate("uiQueue", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiQueue: public Ui_uiQueue {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIQUEUE_H
