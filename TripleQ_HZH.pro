QT       += core gui script xml concurrent serialport network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17#c++11

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    sMainWindow.cpp \
    #$$PWD/sMainWindow/sWindowLog.cpp \
    MassEventSplitter.cpp \
    cDebugFunctions.cpp \
    cFooting/cAcqFunction.cpp \
    sMethod/cTQ_FunctionCMD_AD81416.cpp \
    sMethod/cTQ_FunctionCMD_HZH.cpp \
    sState.cpp \
    sSystem.cpp \
    sBatch.cpp \
    sQueue.cpp \
    sTune.cpp \
    sTune/sStaticASG.cpp \
    sTune/cFunctionsTune.cpp \
    sTune/cTuneXML.cpp \
    sTune/sCalibrationMass.cpp \
    sTune/sCalibrationMass/AcqFunctionTune.cpp \
    sTune/sCalibrationMass/sCalibrationMassItem.cpp \
    sTune/sCalibrationMass/sCalibrationView.cpp \
    sTune/sMapSetMZ.cpp \
    sSingleAcquisition.cpp \
    sSingleAcquisition/sSingleAcquisitionUI.cpp \
    sSingleAcquisition/AcqFunctionSingle.cpp \
    sSingleAcquisition/sRunStopAcq.cpp \
    sSingleAcquisition/IonScanParamEditor.cpp \
    sSingleAcquisition/MRMParameterEditor.cpp \
    sSingleAcquisition/MSParameterParser.cpp \
    sSingleAcquisition/Q13SIMParamEditor.cpp \
    sSingleAcquisition/Q13ScanParamEditor.cpp

HEADERS += \
    sMainWindow.h \
    MassEventSplitter.h \
    cDebugFunctions.h \
    cFooting/cAcqFunction.h \
    cFooting/cMemDataStruct.h \
    sState.h \
    sSystem.h \
    sBatch.h \
    sQueue.h \
    sTune.h \
    sTune/sStaticASG.h \
    sTune/cFunctionsTune.h \
    sTune/cTuneXML.h \
    sTune/sCalibrationMass.h \
    sTune/sCalibrationMass/sCalibrationMassItem.h \
    sTune/sCalibrationMass/sCalibrationView.h \
    sTune/sMapSetMZ.h \
    sSingleAcquisition.h \
    sSingleAcquisition/IonScanParamEditor.h \
    sSingleAcquisition/MRMParameterEditor.h \
    sSingleAcquisition/MSParameterParser.h \
    sSingleAcquisition/Q13SIMParamEditor.h \
    sSingleAcquisition/Q13ScanParamEditor.h \
    sMethod/cTQ_FunctionCMD_AD81416.h \
    sMethod/cTQ_FunctionCMD_HZH.h \
    sMethod/cFrameData.h \
    sMethod/parmeter_structs.h \
    sMethod/xml_attr_tag.h

include($$PWD/LibDataFileR/LibDataFile.pri)
include($$PWD/LibDataFilterR/LibDataFilter.pri)
include($$PWD/LibPeakAlgorithmR/LibPeakAlgorithm.pri)
include($$PWD/HWConnection/HWConnection.pri)
include($$PWD/UI/uiTripleQ_HZH.pri)
include($$PWD/UI/uiSingleAcquisition.pri)

# Add UI generated headers path
INCLUDEPATH += $$PWD/UI/build-uiTripleQ_HZH-Desktop_Qt_5_12_5_MinGW_32_bit-Debug

# Add Boost library path
INCLUDEPATH += D:/StudyAndWork/QtProjects/TripleQ_HZH/Libs/Libs/boost/include

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

