<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>sCalibrationView</class>
 <widget class="QWidget" name="sCalibrationView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1094</width>
    <height>590</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="2">
    <widget class="QWidget" name="widget_2" native="true">
     <property name="maximumSize">
      <size>
       <width>128</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>80</height>
         </size>
        </property>
        <property name="title">
         <string>From Speed</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLineEdit" name="UI_LE_SPEED_CAL">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="UI_PB_ADDMASS_CAL">
           <property name="text">
            <string>Add</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="UI_PB_DELETEMASS_CAL">
        <property name="text">
         <string>Delete</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="1" colspan="2">
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>Acquisition</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_8">
      <item>
       <widget class="QWidget" name="UI_W_ACQ_CAL" native="true">
        <layout class="QHBoxLayout" name="UI_LAYOUT_ACQ_CAL">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_9">
           <item>
            <widget class="QWidget" name="UI_W_METHOD_CAL" native="true">
             <property name="maximumSize">
              <size>
               <width>320</width>
               <height>16777215</height>
              </size>
             </property>
             <layout class="QVBoxLayout" name="UI_LAYOUT_METHOD_CAL">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <item>
                 <widget class="QLabel" name="label">
                  <property name="text">
                   <string>AcquisitionMode:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="UI_CB_ACQ_MODE_CAL">
                  <item>
                   <property name="text">
                    <string>ADC</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>TDC</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_3">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QPushButton" name="UI_PB_ADVANCE_SINGLEACQ">
                  <property name="text">
                   <string>Advance</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <item>
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>AcqFreq(Hz):</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="UI_LE_AcqFreq_CAL">
                  <property name="text">
                   <string>1000000</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_3">
                  <property name="text">
                   <string>AcqACC:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="UI_LE_AcqACC_CAL">
                  <property name="text">
                   <string>32</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="UI_SA_SPEEDMAP_CAL">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>958</width>
        <height>473</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="UI_LAYOUT_SPEEDMAP_CAL"/>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
