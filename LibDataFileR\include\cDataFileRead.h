#pragma once
#include "DataFile_global.h"
#include <QFile>
#include <QString>
#include <QVector>
#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif
#include <QCoreApplication>

class DATAFILE_EXPORT cDataFileRead
{
public:
    cDataFileRead();
    static bool loadFileTIC(double Period,//in<-单页时间限制
                            std::vector<qint64>& pIndexArray,//out->帧数据起始位置表
                            std::vector<double>& pTicX,//out->时间轴数据表
                            std::vector<double>& pTicY,//out->数据轴数据表
                            QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,//out->XIC结构
                            QList<std::vector<double>>& pStructLines,//out->其他数据列
                            QByteArray& pStreamHead,//out->文件头结构数据，需另解析
                            QVector<qint64>& pPageTIC,//out->根据单页时间所得的分页表
                            QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.P"
            );
    static bool loadFileTIC(double Period,//in<-单页时间限制
                            std::vector<qint64>& pIndexArray,//out->帧数据起始位置表
                            std::vector<double>& pTicX,//out->时间轴数据表
                            std::vector<double>& pTicY,//out->数据轴数据表
                            //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,//out->XIC结构
                            QList<std::vector<double>>& pStructLines,//out->其他数据列
                            QByteArray& pStreamHead,//out->文件头结构数据，需另解析
                            QVector<qint64>& pPageTIC,//out->根据单页时间所得的分页表
                            QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.P"
            );
    static bool loadFileTIC(std::vector<qint64>& pIndexArray,//out->帧数据起始位置表
                            std::vector<double>& pTicX,//out->时间轴数据表
                            std::vector<double>& pTicY,//out->数据轴数据表
                            QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,//out->XIC结构
                            QList<std::vector<double>>& pStructLines,//out->其他数据列
                            const QByteArray& pStreamHead,//in<-文件头结构数据
                            qint64 page,//in<-页号
                            const QVector<qint64>& pPageTIC,//in<-根据单页时间所得的分页表
                            QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.P"
            );
    static bool loadFileTIC(std::vector<qint64>& pIndexArray,//out->帧数据起始位置表
                            std::vector<double>& pTicX,//out->时间轴数据表
                            std::vector<double>& pTicY,//out->数据轴数据表
                            //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,//out->XIC结构
                            QList<std::vector<double>>& pStructLines,//out->其他数据列
                            const QByteArray& pStreamHead,//in<-文件头结构数据
                            qint64 page,//in<-页号
                            const QVector<qint64>& pPageTIC,//in<-根据单页时间所得的分页表
                            QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.P"
            );
    static bool loadFileMass(int index,//in<-帧数据位置
                             const std::vector<qint64>& pIndexArray,//in<-帧数据起始位置表
                             QByteArray& pData,//out->数据序列
                             QByteArray& pStreamBody,//out->参数序列
                             QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.D"
            );
    static bool loadFileMass(int nFrameB,//in<-帧数据起始位置
                             int nFrameE,//in<-帧数据起始位置
                             const std::vector<qint64>& pIndexArray,//in<-帧数据起始位置表
                             QByteArray& pData,//out->数据序列
                             QByteArray& pStreamBody,//out->参数序列
                             QString pFilePathName= QString()//in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.D"
            );
    /**
     * @brief insertPropertyStr
     * @param srcStreamHead
     * @param properties 输入属性 字符串属性，<property1,value1&...> <property2,value2&value3&...>
     * @param destStreamHead
     * @return 0: 正常; -1:pStreamHead拆解错误; -2:pStreamHead的原Type_Property_Str中已存在同名Property;
     */
    static int insertPropertyStr(const QByteArray& srcStreamHead,
                                 const QMap<QString, QString>& properties,
                                 QByteArray& destStreamHead);
    /**
     * @brief insertTypeChildParam
     * @param pStreamHead
     * @param newChildParam
     * @return 0: 正常; -1:pStreamHead拆解错误; -2:pStreamHead中已存在相同ChildParam;
     */
    static int insertTypeChildParam(const QByteArray& srcStreamHead,
                                    const QMap<cParamValue::Type_Child_Param, QByteArray>& newChildParam,
                                    QByteArray& destStreamHead);
private:
    template<typename T>
    static void fillData(const QByteArray& pSrcData, QByteArray& pDstData, int countACC, int nFrameB, int nFrameE);
    static bool loadDataHead(QString pFilePath,
                             double Period,
                             std::vector<qint64>& pIndexArray,
                             std::vector<double>& pTicX,
                             std::vector<double>& pTicY,
                             QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                             QList<std::vector<double>>& pStructLines,
                             QByteArray& pStreamHead,
                             QVector<qint64>& pPageTIC);
    static bool loadDataHead(QString pFilePath,
                             double Period,
                             std::vector<qint64>& pIndexArray,
                             std::vector<double>& pTicX,
                             std::vector<double>& pTicY,
                             //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                             QList<std::vector<double>>& pStructLines,
                             QByteArray& pStreamHead,
                             QVector<qint64>& pPageTIC);
    static bool loadDataHead(QString pFilePath,
                             qint64 page,
                             std::vector<qint64>& pIndexArray,
                             std::vector<double>& pTicX,
                             std::vector<double>& pTicY,
                             QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                             QList<std::vector<double>>& pStructLines,
                             const QByteArray& pStreamHead,
                             const QVector<qint64>& pPageTIC);
    static bool loadDataHead(QString pFilePath,
                             qint64 page,
                             std::vector<qint64>& pIndexArray,
                             std::vector<double>& pTicX,
                             std::vector<double>& pTicY,
                             //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                             QList<std::vector<double>>& pStructLines,
                             const QByteArray& pStreamHead,
                             const QVector<qint64>& pPageTIC);
    static bool loadDataBody(QString pFilePath,
                             const std::vector<qint64>& pIndexArray,
                             int nFrameB, int nFrameE,
                             QByteArray& pDataY,
                             QByteArray& pStreamBody);
    static int getNumXIC(const QByteArray& pStreamHead, QString& XicString);
    static QString getNumXIC(const QByteArray& pStreamHead);
    static int updateXIC(const QString& XicString,
                         QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC);
    static int updateXIC(const QString& XicString);

    static int getNumOtherLine(const QString& str,
                               QList<std::vector<double>>& pStructLines);
    static QString getNumOtherLine(const QByteArray& pStreamHead);
};

