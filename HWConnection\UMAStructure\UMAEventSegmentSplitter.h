﻿#pragma once

#include <uiMethod/cTQ_StructCMD_HZH.h>

namespace UMA_HCS {

class /*HWCONNECTION_EXPORT*/ UMAEventSegmentSplitter
{
public:
    typedef struct EventSegment{
        double startTimeMin;
        double endTimeMin;
        uint64_t channelCounts;
        //std::vector<UMA_Event> events;
        QList<cTQ_StructCMD_HZH::_EVENT_PARAM > events;
        int umaChCount() const{
            int cnt = 0;
            for (auto& evt : events)
                cnt += evt.listChannel.size();
            return cnt;
        }
    } EventSegment;
public:
    UMAEventSegmentSplitter();

    void setUMAEvents(const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& events);
    //void setUMAEvents(const std::vector<UMA_Event>& events);
    const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& umaEvents() const;//const std::vector<UMA_Event>& umaEvents() const;
    bool getUMAEventNo(uint64_t lChannelId, uint64_t* lpEventNo);
    bool getUMAChannelNo(uint64_t lChannelId, uint64_t* lpChannelNo);
    bool getUMAChannelPeroid(uint64_t lChannelId, uint64_t* lpTimeMs);
    bool getUMASegmentNo(uint64_t lChannelId, uint64_t* lpSegmentNo);
    bool getUMATime(uint64_t lChannelId, uint64_t* lpTimeMs);
    void getTriggerInfo(int channel, int* segIndex, int* evtIndex, int* chIndex);
    uint64_t segmentCount() const;
    const std::vector<EventSegment>& segment() const;
private:
    cTQ_StructCMD_HZH::_EVENT_PARAM_SET mEvents;
    //std::vector<UMA_Event> mEvents;
    std::vector<EventSegment> mSegments;

//    void prepareSegmentData(const std::vector<UMA_Event>& events,
//                            std::vector<EventSegment> *pSegments);
    void prepareSegmentData(const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& events,
                            std::vector<EventSegment> *pSegments);
    void calculateSegmentChannels(std::vector<EventSegment>* segment);

};

}

