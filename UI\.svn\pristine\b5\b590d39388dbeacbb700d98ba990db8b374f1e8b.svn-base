#ifndef COMMSERIAL_H
#define COMMSERIAL_H

#include <qdebug.h>
#include <QMutex>
#include <QElapsedTimer>
#include <QCoreApplication>
#include <QSerialPort>
#include "3rd_qextserialport/qextserialport.h"

class cCommSerial : public QextSerialPort
{
    Q_OBJECT
public:
    struct _STRUCT_SERIAL{
        quint16 PID_SERIAL= 29987;
        quint16 VID_SERIAL= 6790;
        qint32 DelayTimeMs_SERIAL= 1000;
        BaudRateType BaudRate_SERIAL= BAUD115200;
        ParityType Parity_SERIAL= PAR_NONE;
        DataBitsType DataBits_SERIAL= DATA_8;
        StopBitsType StopBits_SERIAL= STOP_1;
        QIODevice::OpenMode OpenModeFlag_SERIAL= QIODevice::ReadWrite;
        _STRUCT_SERIAL(quint16 PID= 29987, quint16 VID= 6790,
                       qint32 DelayTimeMs= 1000,
                       BaudRateType baudRate= BAUD115200,
                       ParityType parity= PAR_NONE,
                       DataBitsType dataBits= DATA_8,
                       StopBitsType stopBits= STOP_1,
                       QIODevice::OpenMode openModeFlag= QIODevice::ReadWrite);
        _STRUCT_SERIAL & operator=(const _STRUCT_SERIAL &obj);
    };

    explicit cCommSerial(QObject *parent = nullptr);
    ~cCommSerial();
    QString openComm(const _STRUCT_SERIAL& pSTRUCT_SERIAL, /*const */QStringList& excludedList,
                  const QByteArray& writeArray, QByteArray& readArray, quint16 nRead, qint32 delayMS);
    QByteArray writeComm(const QByteArray& writeArray, quint16 nRead, qint32 delayMS);
    qint64 write(const QByteArray& lpBuffer,
                 qint32 nTimeOutMs= 500);
    QByteArray read(qint64 lengthR,
                    qint32 nTimeOutMs= 500);
    bool ioControl(
            int dwIoControlCode,
            void* lpInBuffer = nullptr,
            unsigned int nInBufferSize = 0,
            void* lpOutBuffer = nullptr,
            unsigned int nOutBufferSize = 0,
            unsigned int* lpBytesReturned = nullptr,
            unsigned int nTimeOutMs = ~0
            );
    bool trylock(int timeout= 0);
    void lock();
    void unlock();

private:
    QMutex mMutex;
    quint16 mPID= 29987,
        mVID= 6790;
    BaudRateType mBaudRate= BAUD115200;
    qint32    mDelayMS=3000;
    ParityType mTypeParity= PAR_NONE;
    QByteArray mOpenCMD;
    quint16 mOpenREQ=0;

    QString findDevice(quint16 PID= 29987, quint16 VID= 6790);
    QList<QString> findDevices(quint16 PID= 29987, quint16 VID= 6790);
    bool open(const QString& portName, const _STRUCT_SERIAL& pSTRUCT_SERIAL);

public slots:
    void onReadyRead();
    void handleBytesWritten(qint64 nBytes);
    //void handleError(PortSettings::SerialPortError err);
};

#endif // COMMSERIAL_H
