﻿#include "mzVoltageTableEditor.h"
#include <QTableWidgetItem>
#include <LibWidget/SpreadSheet/cell.h>
#include "uiMethod/cTQ_StructCMD_AD81416.h"

mzVoltageTableEditor::mzVoltageTableEditor(QWidget *parent)
    : Spreadsheet(parent),
    m_currentMZ(0)
{
    setAutoRecalculate(false);
    createCustomActions();

    connect(this, &QTableWidget::currentCellChanged,
            this, &mzVoltageTableEditor::onCurrentCellChanged);
    connect(this, &QTableWidget::itemChanged,
            this, &mzVoltageTableEditor::updateCurMzVolMapsFromTable);
}

void mzVoltageTableEditor::onCurrentCellChanged(int curRow, int curCol, int preRow, int preCol)
{
    Q_UNUSED(curCol)
    Q_UNUSED(preCol)

    if (item(curRow, 0))
        item(curRow, 0)->setBackground(Qt::green);

    if (curRow == preRow)
        return;

    if (-1 != preRow && cell(preRow, 0))
        item(preRow, 0)->setBackground(Qt::white);
}

void mzVoltageTableEditor::updateCurMzVolMapsFromTable()
{
    HZH::paramMZ_Voltages mzVoltages;
    double mz;
    HZH::ParamAdvanceBase param;
    double paramBuffer[NUM_DAC48];
    bool ok;
    Spreadsheet* table = this;
    for (int row = 0; row < table->rowCount(); ++row) {
        if (table->item(row, 0) == nullptr
            || table->item(row, 0) ->text().isEmpty())
            continue;
        mz = table->item(row, 0)->text().toDouble(&ok);
        if (ok){
            for (int col = 0; col < NUM_DAC48; ++col) {
                paramBuffer[col] = table->text(row, col+1).toDouble(&ok);
                if (false == ok) break;
            }
            if (ok){
                memcpy(param.voltages, paramBuffer, (NUM_DAC48)*sizeof (double));
                //memcpy(param.RF, paramBuffer+12, 3*sizeof (double));
                mzVoltages.mzVoltages.insert(mz, param);
            }
        }
    }

    setCurMzVoltageMap(mzVoltages, false);
}

void mzVoltageTableEditor::updateCurMzVolMapsToTable(const HZH::paramMZ_Voltages& mzVoltages)
{
    auto it = mzVoltages.mzVoltages.begin();
    HZH::ParamAdvanceBase param;
    int row = 1;
    double* pTmp = reinterpret_cast<double*>(&param);

    blockSignals(true);
    clearContents();

    while (it != mzVoltages.mzVoltages.end()) {
        setValue(row, 1, it.key());//mz
        param = it.value();
        for (int i = 0; i < NUM_HDAC+ NUM_LDAC; ++i) {
            setValue(row, i+2, pTmp[i]);
        }
        ++row;
        ++it;
    }
    blockSignals(false);
}

void mzVoltageTableEditor::createCustomActions()
{
    m_customMenu->addSeparator();
    m_customMenu->addAction("Remove MZ", this, [&]{
        int row = currentRow();
        if (row >= 0)
            removeRow(row);
    });
}

HZH::paramMZ_Voltages mzVoltageTableEditor::curMzVoltageMap() const
{
    return m_curMzVoltageMap;
}

void mzVoltageTableEditor::setCurMzVoltageMap(const HZH::paramMZ_Voltages &curMzVoltageMap, bool show)
{
    if (m_curMzVoltageMap == curMzVoltageMap)
        return;

    emit curMzVoltageMapChanged(curMzVoltageMap);
    if (show)
        updateCurMzVolMapsToTable(curMzVoltageMap);

    m_curMzVoltageMap = curMzVoltageMap;
}

/**
 * @brief mzVoltageTableEditor::setValue
 * 向第r行，第c列插入值value。 r和c的起始索引为 1
 * @param r
 * @param c
 * @param value
 */
void mzVoltageTableEditor::setValue(int r, int c, double value)
{
    if (r < 1 || c < 1)
        return;
    Cell* item = static_cast<Cell*>(this->item(r-1, c-1));
    if (nullptr == item){
        item = new Cell;
        setItem(r-1, c-1, item);
    }
    item->setFormula(QString::number(value));
}
