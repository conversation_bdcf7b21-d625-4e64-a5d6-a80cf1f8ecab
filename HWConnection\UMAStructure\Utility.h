#pragma once

#include <numeric>
#include <algorithm>
#include <cstdint>

namespace UMA_HCS {
namespace Numeric {

template <class T1, class T2>
bool isAlmostEqual(const T1& var1, const T2& var2)
{
    constexpr double episilon1 = std::numeric_limits<T1>::epsilon();
    constexpr double episilon2 = std::numeric_limits<T2>::epsilon();
    constexpr double episilon = episilon1 > episilon2 ? episilon1 : episilon2;
    return std::abs(var1 - var2) < episilon;
}

template <class T1, class T2, class T3>
bool isInRange(const T1& lower, const T2& upper, const T3& var)
{
    return lower <= var && upper >= var;
}

}   // end namespace Numeric
}   // end namespace UMA_HCS

