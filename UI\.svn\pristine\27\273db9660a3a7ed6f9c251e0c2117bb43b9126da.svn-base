#ifndef CPUBLICSTRUCTHZH_H
#define CPUBLICSTRUCTHZH_H

#include <uiMethod/cTQ_StructCMD_HZH.h>
typedef unsigned int UINT;

struct _STRUCT_ADC_TDC{
    UINT Frq;// 采集频率
    UINT ACC;// 累加次数
    UINT Mode;//TDC:1; ADC:0
};

//typedef std::vector<double> DataSeg;///< 数据段
typedef QVector</*DataSeg*/std::vector<double>> MsChData;///< uma ch 的各段数据
typedef QVector<MsChData> MsEvtData; ///< uma evt 各通道数据
typedef QVector<MsEvtData> MsSegData;///< segment 各event数据



struct _STRUCT_ScanInfo{
    int segIndex= 0,
    msEvtIndex= 0,
    msChIndex= 0;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
    _STRUCT_ScanInfo(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE UMAType= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS):
    type(UMAType){

    }
};

#endif // CPUBLICSTRUCTHZH_H
