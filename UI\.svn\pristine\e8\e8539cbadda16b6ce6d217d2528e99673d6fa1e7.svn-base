<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiBaseParamEditor</class>
 <widget class="QWidget" name="uiBaseParamEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>273</width>
    <height>423</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="uiBaseParamEditorLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QLabel" name="label_8">
       <property name="layoutDirection">
        <enum>Qt::RightToLeft</enum>
       </property>
       <property name="text">
        <string>Gating Voltage(mV):</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="UI_LE_gating_mv">
       <property name="text">
        <string>75</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Polarity</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item row="1" column="2">
       <widget class="QLineEdit" name="lineEdit_Polarity_switch_time_rOnly">
        <property name="maximumSize">
         <size>
          <width>65</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>1</string>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QLineEdit" name="lineEdit_Polarity_switch_time">
        <property name="maximumSize">
         <size>
          <width>65</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="Polarity_switch_time">
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Switch Time(ms)</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QPushButton" name="UI_PB_Polarity">
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>+</string>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="label_6">
       <property name="text">
        <string>Event Time(ms):</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEdit_eventTime">
       <property name="maximumSize">
        <size>
         <width>65</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
       <property name="frame">
        <bool>true</bool>
       </property>
       <property name="readOnly">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>Pause Time(ms):</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_pauseTime_rOnly">
       <property name="maximumSize">
        <size>
         <width>65</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>1</string>
       </property>
       <property name="frame">
        <bool>false</bool>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_pauseTime">
       <property name="maximumSize">
        <size>
         <width>65</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>1</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>Wait Time(ms):</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_waitTime_rOnly">
       <property name="text">
        <string>1</string>
       </property>
       <property name="frame">
        <bool>false</bool>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,0,1,1,2">
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>Acq.Time</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_startTime">
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_endTime">
       <property name="text">
        <string>100</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>min</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>183</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
