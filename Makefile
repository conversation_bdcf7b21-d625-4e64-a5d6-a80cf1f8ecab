#############################################################################
# Makefile for building: TripleQ_HZH
# Generated by qmake (3.1) (Qt 5.12.5)
# Project:  TripleQ_HZH.pro
# Template: app
# Command: D:/Qt5.12.5/5.12.5/mingw73_32/bin/qmake.exe -o Makefile TripleQ_HZH.pro -spec win32-g++ CONFIG+=qtquickcompiler
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = D:/Qt5.12.5/5.12.5/mingw73_32/bin/qmake.exe
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = cp -f
INSTALL_PROGRAM = cp -f
INSTALL_DIR   = cp -f -R
QINSTALL      = D:/Qt5.12.5/5.12.5/mingw73_32/bin/qmake.exe -install qinstall
QINSTALL_PROGRAM = D:/Qt5.12.5/5.12.5/mingw73_32/bin/qmake.exe -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
SUBTARGETS    =  \
		release \
		debug


release: FORCE
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: FORCE
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: TripleQ_HZH.pro ../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/win32-g++/qmake.conf ../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/spec_pre.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/qdevice.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/device_config.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/sanitize.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/gcc-base.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/g++-base.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/angle.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/windows_vulkan_sdk.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/windows-vulkan.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/g++-win32.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/qconfig.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3danimation.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3danimation_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dcore.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dcore_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dextras.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dextras_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dinput.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dinput_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dlogic.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dlogic_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquick.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquick_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickextras.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickinput.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickrender.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3drender.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3drender_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axbase.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axbase_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axcontainer.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axcontainer_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axserver.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axserver_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bluetooth.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bluetooth_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bootstrap_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_charts.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_charts_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_concurrent.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_concurrent_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_core.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_core_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_dbus.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_dbus_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designer.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designer_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designercomponents_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_edid_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_egl_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_fb_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gamepad.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gamepad_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gui.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gui_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_help.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_help_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_location.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_location_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimedia.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimedia_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_network.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_network_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_nfc.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_nfc_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_opengl.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_opengl_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_openglextensions.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_openglextensions_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioning.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioning_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioningquick.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioningquick_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_printsupport.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_printsupport_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qml.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qml_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmldebug_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmltest.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmltest_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quick.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quick_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickparticles_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickshapes_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickwidgets.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_remoteobjects.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_repparser.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_repparser_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_script.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_script_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scripttools.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scripttools_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scxml.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scxml_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sensors.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sensors_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialbus.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialbus_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialport.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialport_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sql.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sql_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_svg.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_svg_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_testlib.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_testlib_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_texttospeech.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_texttospeech_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_theme_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uiplugin.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uitools.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uitools_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_webchannel.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_webchannel_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_websockets.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_websockets_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_widgets.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_widgets_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_winextras.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_winextras_private.pri \
		../../../../EdgeDownloads/QtXlsxWriter-master/QtXlsxWriter-master/mkspecs/modules-inst/qt_lib_xlsx.pri \
		../../../../EdgeDownloads/QtXlsxWriter-master/QtXlsxWriter-master/mkspecs/modules-inst/qt_lib_xlsx_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xlsx.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xml.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xml_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt_functions.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt_config.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/win32-g++/qmake.conf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/spec_post.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exclusive_builds.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/toolchain.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/default_pre.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/default_pre.prf \
		LibDataFileR/LibDataFile.pri \
		LibDataFilterR/LibDataFilter.pri \
		LibPeakAlgorithmR/LibPeakAlgorithm.pri \
		HWConnection/LibControlCCS_R/LibControlCCS.pri \
		HWConnection/HWConnection.pri \
		../../../../QT/GlobalStruct/libGlobalStruct.pri \
		UI/LibGlobalToolsR/LibGlobalTools.pri \
		UI/LibMatrixR/LibMatrix.pri \
		UI/LibXlsx/qtxlsx.pri \
		C:/Qwt-6.1.1-svn/features/qwtconfig.pri \
		C:/Qwt-6.1.1-svn/features/qwtfunctions.pri \
		C:/Qwt-6.1.1-svn/features/qwt.prf \
		UI/LibWidget/LibWidget.pri \
		UI/LibSerialDeviceManagerR/LibSerialDeviceManager.pri \
		UI/uiSingleAcquisition.pri \
		UI/uiManualMode.pri \
		UI/uiTripleQ_HZH.pri \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/resolve_config.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exclusive_builds_post.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/default_post.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/resources.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qtquickcompiler.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/precompile_header.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/warn_on.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/moc.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/opengl.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/uic.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qmake_use.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/file_copies.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/windows.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/testcase_targets.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exceptions.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/yacc.prf \
		../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/lex.prf \
		TripleQ_HZH.pro \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Svg.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Widgets.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Gui.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Script.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Xml.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Concurrent.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5SerialPort.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Network.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Core.prl \
		../../../../Qt5.12.5/5.12.5/mingw73_32/lib/qtmain.prl
	$(QMAKE) -o Makefile TripleQ_HZH.pro -spec win32-g++ CONFIG+=qtquickcompiler
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/spec_pre.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/qdevice.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/device_config.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/sanitize.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/gcc-base.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/g++-base.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/angle.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/windows_vulkan_sdk.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/windows-vulkan.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/common/g++-win32.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/qconfig.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3danimation.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3danimation_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dcore.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dcore_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dextras.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dextras_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dinput.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dinput_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dlogic.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dlogic_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquick.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquick_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickextras.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickextras_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickinput.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickinput_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickrender.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickrender_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3drender.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_3drender_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_accessibility_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axbase.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axbase_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axcontainer.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axcontainer_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axserver.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_axserver_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bluetooth.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bluetooth_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_bootstrap_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_charts.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_charts_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_concurrent.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_concurrent_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_core.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_core_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_dbus.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_dbus_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designer.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designer_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_designercomponents_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_edid_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_egl_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_fb_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gamepad.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gamepad_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gui.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_gui_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_help.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_help_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_location.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_location_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimedia.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimedia_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_network.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_network_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_nfc.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_nfc_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_opengl.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_opengl_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_openglextensions.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_openglextensions_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_packetprotocol_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioning.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioning_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioningquick.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_positioningquick_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_printsupport.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_printsupport_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qml.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qml_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmldebug_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmldevtools_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmltest.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qmltest_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quick.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quick_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickparticles_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickshapes_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickwidgets.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_quickwidgets_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_remoteobjects.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_remoteobjects_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_repparser.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_repparser_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_script.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_script_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scripttools.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scripttools_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scxml.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_scxml_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sensors.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sensors_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialbus.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialbus_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialport.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_serialport_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sql.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_sql_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_svg.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_svg_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_testlib.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_testlib_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_texttospeech.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_texttospeech_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_theme_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uiplugin.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uitools.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_uitools_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_webchannel.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_webchannel_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_websockets.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_websockets_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_widgets.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_widgets_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_winextras.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_winextras_private.pri:
../../../../EdgeDownloads/QtXlsxWriter-master/QtXlsxWriter-master/mkspecs/modules-inst/qt_lib_xlsx.pri:
../../../../EdgeDownloads/QtXlsxWriter-master/QtXlsxWriter-master/mkspecs/modules-inst/qt_lib_xlsx_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xlsx.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xml.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xml_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt_functions.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt_config.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/win32-g++/qmake.conf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/spec_post.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exclusive_builds.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/toolchain.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/default_pre.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/default_pre.prf:
LibDataFileR/LibDataFile.pri:
LibDataFilterR/LibDataFilter.pri:
LibPeakAlgorithmR/LibPeakAlgorithm.pri:
HWConnection/LibControlCCS_R/LibControlCCS.pri:
HWConnection/HWConnection.pri:
../../../../QT/GlobalStruct/libGlobalStruct.pri:
UI/LibGlobalToolsR/LibGlobalTools.pri:
UI/LibMatrixR/LibMatrix.pri:
UI/LibXlsx/qtxlsx.pri:
C:/Qwt-6.1.1-svn/features/qwtconfig.pri:
C:/Qwt-6.1.1-svn/features/qwtfunctions.pri:
C:/Qwt-6.1.1-svn/features/qwt.prf:
UI/LibWidget/LibWidget.pri:
UI/LibSerialDeviceManagerR/LibSerialDeviceManager.pri:
UI/uiSingleAcquisition.pri:
UI/uiManualMode.pri:
UI/uiTripleQ_HZH.pri:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/resolve_config.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exclusive_builds_post.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/default_post.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/resources.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qtquickcompiler.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/precompile_header.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/warn_on.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qt.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/moc.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/opengl.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/uic.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/qmake_use.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/file_copies.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/win32/windows.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/testcase_targets.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/exceptions.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/yacc.prf:
../../../../Qt5.12.5/5.12.5/mingw73_32/mkspecs/features/lex.prf:
TripleQ_HZH.pro:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Svg.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Widgets.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Gui.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Script.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Xml.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Concurrent.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5SerialPort.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Network.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/Qt5Core.prl:
../../../../Qt5.12.5/5.12.5/mingw73_32/lib/qtmain.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile TripleQ_HZH.pro -spec win32-g++ CONFIG+=qtquickcompiler

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash

release-mocclean:
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile
