﻿#ifndef SPREADSHEET_H
#define SPREADSHEET_H

#include <QTableWidget>
#include <QDataStream>
#include <QAction>
#include <QMenu>

class Cell;
class SpreadsheetCompare;

class Spreadsheet : public QTableWidget
{
    Q_OBJECT

public:
    Spreadsheet(QWidget *parent = nullptr);

    bool autoRecalculate() const { return autoRecalc; }
    QString currentLocation() const;
    QString currentFormula() const;
    QTableWidgetSelectionRange selectedRange() const;
//    void clear();
    void resetTableSize(int row, int column);
//    bool readFile(const QString &fileName);
//    bool writeFile(const QString &fileName);
    void sort(const SpreadsheetCompare &compare);
    friend QDataStream &operator <<(QDataStream& out, const Spreadsheet& sp);
    friend QDataStream &operator >>(QDataStream& in, Spreadsheet& sp);
    QString text(int row, int column) const;
    bool load(const QString& sxlsFile);
    Cell *cell(int row, int column) const;

public slots:
    void cut();
    void copy();
    void paste();
    void del();
    void selectCurrentRow();
    void selectCurrentColumn();
    void recalculate();
    void setAutoRecalculate(bool recalc);
    void findNext(const QString &str, Qt::CaseSensitivity cs);
    void findPrevious(const QString &str, Qt::CaseSensitivity cs);

signals:
    void modified();

private slots:
    void somethingChanged();
    void removeCurColumn();
    void find();
    void onCustomContextMenuRequested(const QPoint &pos);

protected:
    virtual void createCustomActions();
    //custom data
    QMenu* m_customMenu;

private:
//    enum { MagicNumber = 0x7F51C883, RowCount = 999, ColumnCount = 26 };
    QString formula(int row, int column) const;
    void setFormula(int row, int column, const QString &formula);

    bool autoRecalc;
};

class SpreadsheetCompare
{
public:
    bool operator()(const QStringList &row1,
                    const QStringList &row2) const;

    enum { KeyCount = 3 };
    int keys[KeyCount];
    bool ascending[KeyCount];
};

#endif
