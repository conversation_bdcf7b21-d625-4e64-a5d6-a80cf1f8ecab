/****************************************************************************
** Meta object code from reading C++ file 'uiCalibrationMassItem.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiTune/uiCalibrationMass/uiCalibrationMassItem.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiCalibrationMassItem.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiCalibrationMassItem_t {
    QByteArrayData data[16];
    char stringdata0[260];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiCalibrationMassItem_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiCalibrationMassItem_t qt_meta_stringdata_uiCalibrationMassItem = {
    {
QT_MOC_LITERAL(0, 0, 21), // "uiCalibrationMassItem"
QT_MOC_LITERAL(1, 22, 7), // "focused"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 5), // "speed"
QT_MOC_LITERAL(4, 37, 9), // "onClicked"
QT_MOC_LITERAL(5, 47, 26), // "on_UI_PB_APPLY_CAL_clicked"
QT_MOC_LITERAL(6, 74, 27), // "on_UI_PB_CANCEL_CAL_clicked"
QT_MOC_LITERAL(7, 102, 10), // "onAddFront"
QT_MOC_LITERAL(8, 113, 11), // "onAddBehind"
QT_MOC_LITERAL(9, 125, 8), // "onRemove"
QT_MOC_LITERAL(10, 134, 45), // "on_UI_TW_TABLE_CAL_customCont..."
QT_MOC_LITERAL(11, 180, 3), // "pos"
QT_MOC_LITERAL(12, 184, 30), // "on_UI_PB_Calculate_CAL_clicked"
QT_MOC_LITERAL(13, 215, 26), // "on_UI_TW_TABLE_CAL_clicked"
QT_MOC_LITERAL(14, 242, 11), // "QModelIndex"
QT_MOC_LITERAL(15, 254, 5) // "index"

    },
    "uiCalibrationMassItem\0focused\0\0speed\0"
    "onClicked\0on_UI_PB_APPLY_CAL_clicked\0"
    "on_UI_PB_CANCEL_CAL_clicked\0onAddFront\0"
    "onAddBehind\0onRemove\0"
    "on_UI_TW_TABLE_CAL_customContextMenuRequested\0"
    "pos\0on_UI_PB_Calculate_CAL_clicked\0"
    "on_UI_TW_TABLE_CAL_clicked\0QModelIndex\0"
    "index"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiCalibrationMassItem[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   64,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    0,   67,    2, 0x08 /* Private */,
       5,    0,   68,    2, 0x08 /* Private */,
       6,    0,   69,    2, 0x08 /* Private */,
       7,    0,   70,    2, 0x08 /* Private */,
       8,    0,   71,    2, 0x08 /* Private */,
       9,    0,   72,    2, 0x08 /* Private */,
      10,    1,   73,    2, 0x08 /* Private */,
      12,    0,   76,    2, 0x08 /* Private */,
      13,    1,   77,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   11,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14,   15,

       0        // eod
};

void uiCalibrationMassItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiCalibrationMassItem *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->focused((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->onClicked(); break;
        case 2: _t->on_UI_PB_APPLY_CAL_clicked(); break;
        case 3: _t->on_UI_PB_CANCEL_CAL_clicked(); break;
        case 4: _t->onAddFront(); break;
        case 5: _t->onAddBehind(); break;
        case 6: _t->onRemove(); break;
        case 7: _t->on_UI_TW_TABLE_CAL_customContextMenuRequested((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 8: _t->on_UI_PB_Calculate_CAL_clicked(); break;
        case 9: _t->on_UI_TW_TABLE_CAL_clicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiCalibrationMassItem::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiCalibrationMassItem::focused)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiCalibrationMassItem::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_uiCalibrationMassItem.data,
    qt_meta_data_uiCalibrationMassItem,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiCalibrationMassItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiCalibrationMassItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiCalibrationMassItem.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int uiCalibrationMassItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void uiCalibrationMassItem::focused(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
