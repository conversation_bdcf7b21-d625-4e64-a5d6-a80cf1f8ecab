/********************************************************************************
** Form generated from reading UI file 'sFileWidget.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SFILEWIDGET_H
#define UI_SFILEWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sFileWidget
{
public:
    QVBoxLayout *verticalLayout;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label;
    QLineEdit *lineEdit;
    QTableWidget *tableWidget;
    QHBoxLayout *horizontalLayout;
    QLineEdit *E_FILENAME;
    QPushButton *PB_OK;
    QPushButton *PB_CANCEL;

    void setupUi(QWidget *sFileWidget)
    {
        if (sFileWidget->objectName().isEmpty())
            sFileWidget->setObjectName(QString::fromUtf8("sFileWidget"));
        sFileWidget->resize(581, 324);
        verticalLayout = new QVBoxLayout(sFileWidget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        scrollArea = new QScrollArea(sFileWidget);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 579, 322));
        verticalLayout_2 = new QVBoxLayout(scrollAreaWidgetContents);
        verticalLayout_2->setSpacing(3);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(3, 3, 3, 3);
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label = new QLabel(scrollAreaWidgetContents);
        label->setObjectName(QString::fromUtf8("label"));
        QFont font;
        font.setFamily(QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221"));
        font.setPointSize(12);
        label->setFont(font);

        horizontalLayout_2->addWidget(label);

        lineEdit = new QLineEdit(scrollAreaWidgetContents);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));
        lineEdit->setFont(font);

        horizontalLayout_2->addWidget(lineEdit);


        verticalLayout_2->addLayout(horizontalLayout_2);

        tableWidget = new QTableWidget(scrollAreaWidgetContents);
        tableWidget->setObjectName(QString::fromUtf8("tableWidget"));
        QFont font1;
        font1.setPointSize(12);
        tableWidget->setFont(font1);

        verticalLayout_2->addWidget(tableWidget);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        E_FILENAME = new QLineEdit(scrollAreaWidgetContents);
        E_FILENAME->setObjectName(QString::fromUtf8("E_FILENAME"));
        E_FILENAME->setMinimumSize(QSize(0, 40));
        E_FILENAME->setMaximumSize(QSize(16777215, 40));
        QPalette palette;
        QBrush brush(QColor(228, 218, 208, 255));
        brush.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Base, brush);
        palette.setBrush(QPalette::Inactive, QPalette::Base, brush);
        QBrush brush1(QColor(240, 240, 240, 255));
        brush1.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Disabled, QPalette::Base, brush1);
        E_FILENAME->setPalette(palette);
        QFont font2;
        font2.setFamily(QString::fromUtf8("Microsoft YaHei UI"));
        font2.setPointSize(12);
        font2.setBold(false);
        font2.setWeight(50);
        E_FILENAME->setFont(font2);
        E_FILENAME->setAutoFillBackground(true);
        E_FILENAME->setReadOnly(true);

        horizontalLayout->addWidget(E_FILENAME);

        PB_OK = new QPushButton(scrollAreaWidgetContents);
        PB_OK->setObjectName(QString::fromUtf8("PB_OK"));
        PB_OK->setMinimumSize(QSize(75, 44));
        PB_OK->setMaximumSize(QSize(16777215, 44));
        QFont font3;
        font3.setFamily(QString::fromUtf8("Microsoft YaHei UI"));
        font3.setPointSize(12);
        font3.setBold(true);
        font3.setWeight(75);
        PB_OK->setFont(font3);
        PB_OK->setStyleSheet(QString::fromUtf8("background-color: #3c3c3c;\n"
"color:#E4DAD0;\n"
"border-style: outset;\n"
"border-width:2px;\n"
"border-radius:10px;\n"
"border-color:#E4DAD0;"));

        horizontalLayout->addWidget(PB_OK);

        PB_CANCEL = new QPushButton(scrollAreaWidgetContents);
        PB_CANCEL->setObjectName(QString::fromUtf8("PB_CANCEL"));
        PB_CANCEL->setMinimumSize(QSize(75, 44));
        PB_CANCEL->setMaximumSize(QSize(16777215, 44));
        PB_CANCEL->setFont(font3);
        PB_CANCEL->setStyleSheet(QString::fromUtf8("background-color: #3c3c3c;\n"
"color:#E4DAD0;\n"
"border-style: outset;\n"
"border-width:2px;\n"
"border-radius:10px;\n"
"border-color:#E4DAD0;"));

        horizontalLayout->addWidget(PB_CANCEL);


        verticalLayout_2->addLayout(horizontalLayout);

        scrollArea->setWidget(scrollAreaWidgetContents);

        verticalLayout->addWidget(scrollArea);


        retranslateUi(sFileWidget);

        QMetaObject::connectSlotsByName(sFileWidget);
    } // setupUi

    void retranslateUi(QWidget *sFileWidget)
    {
        sFileWidget->setWindowTitle(QApplication::translate("sFileWidget", "Form", nullptr));
        label->setText(QApplication::translate("sFileWidget", "FrameCount:", nullptr));
        lineEdit->setInputMask(QString());
        lineEdit->setText(QApplication::translate("sFileWidget", "1", nullptr));
        PB_OK->setText(QApplication::translate("sFileWidget", "OK", nullptr));
        PB_CANCEL->setText(QApplication::translate("sFileWidget", "Cancel", nullptr));
    } // retranslateUi

};

namespace Ui {
    class sFileWidget: public Ui_sFileWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SFILEWIDGET_H
