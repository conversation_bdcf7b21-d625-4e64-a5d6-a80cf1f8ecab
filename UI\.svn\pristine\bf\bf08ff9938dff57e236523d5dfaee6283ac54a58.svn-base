#pragma once

#include <QWidget>
#include <uiDataAcqManual.h>
#include <uiDeviceControl.h>
#include <uiMassTuneManual.h>
#include <uiOptimizationManual.h>
#include "ui_uiManualMode.h"

class uiManualMode : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiManualMode(QScriptEngine* pScriptEngine,
                          QWidget *parent = nullptr);
    ~uiManualMode();
    virtual void initClass(QString& filePath);

protected:
    Ui::uiManualMode ui;
    bool initUI(QString& filePath);
    virtual void createToolBar();
    uiDataAcqManual* mDataAcqManual;
    uiDeviceControl* mDeviceControl;
    uiMassTuneManual* mMassTuneManual;
    uiOptimizationManual* mOptimizationManual;
private slots:
    void on_UI_PB_DeviceControl_clicked();
    void on_UI_PB_DataAcqManual_clicked();
    void on_UI_PB_MassTuneManual_clicked();
    void on_UI_PB_OptimizationManual_clicked();
};

