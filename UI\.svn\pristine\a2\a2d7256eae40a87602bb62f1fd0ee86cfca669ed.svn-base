#ifndef SCTRLTFG_H
#define SCTRLTFG_H

#include <QWidget>
#include "ui_sCtrlTFG.h"
#include <QSerialPortInfo>
//#include <QSerialPort>
#include <LibSerialDeviceManagerR/include/SerialDeviceManager.h>

class sCtrlTFG : public QWidget
{
    Q_OBJECT

public:
    static bool checkStateReq(QByteArray& QRecivedata, void* pThis);
    static bool checkParamReq(QByteArray& QRecivedata, void* pThis);
    explicit sCtrlTFG(QWidget *parent = nullptr);
    ~sCtrlTFG();
    QByteArray mQRecivedata;

private slots:
    void on_Monitor_ON_clicked();

    void on_Purge_Gas_ON_clicked();

    void on_IG_ON_clicked();

    void on_Tpump_Frq_textChanged(const QString &arg1);

    void on_Tpump_ON_clicked();

    void on_Rpump_ON_clicked();

    void on_Auto_ShutDown_clicked();

    void on_Auto_StartUp_clicked();

    void on_IG12_RF_ON_clicked();

    void on_CC_RF_ON_clicked();

    void on_Q1_RF_ON_clicked();

    void on_Q1_ABMode_clicked();

    void on_Q3_RF_on_ctrl_clicked();

    void on_Q3_ABmode_ctrl_clicked();

    void on_Heat_PW_ON_clicked();

    void on_Heat_ON_clicked();

    void on_CID_Gas_ON_clicked();

    void on_CID_Rel_ON_clicked();

    void on_ESI_HV_ON_clicked();

    void on_CUR_HV_ON_clicked();

    void on_HED_HV_ON_clicked();

    void on_DET_HV_ON_clicked();

    void on_IG0_RF_ON_clicked();

    void on_update_SerialPort_clicked();

    void on_Open_SerialPort_clicked();
    void on_Scan_Ctrl_clicked();

private:
    Ui::sCtrlTFG ui;
    int mDeviceNumber=-1;
    SerialDeviceManager* mDeviceSerial= nullptr;
    double Tpump_Frq_value/*, Rpump_Frq_value, PG_P_value, IG_P_value*/;
    int timer1 = -1;
    //QSerialPort* serialPort1=nullptr;
    void timerEvent(QTimerEvent *evt);

private:
    QByteArray SetParamCMD(){
        double ESI_Set_Temp_value = ui.UI_TW_TEMP_CTRL_CT->item(0,0)->text().toDouble();//double.Parse(ui.ESIHeatSetTemp.text());
        double APCI_Set_Temp_value = ui.UI_TW_TEMP_CTRL_CT->item(0,1)->text().toDouble();//double.Parse(APCIHeatSetTemp.Text);
        double Curtain_Set_Temp_value = ui.UI_TW_TEMP_CTRL_CT->item(0,2)->text().toDouble();//double.Parse(CurtainHeatSetTemp.Text);
        quint8 ESI_Temp_P_value = ui.UI_TW_TEMP_CTRL_CT->item(3,0)->text().toUInt();//byte.Parse(ESIHeatP.Text);
        quint8 ESI_Temp_I_value = ui.UI_TW_TEMP_CTRL_CT->item(4,0)->text().toUInt();//byte.Parse(ESIHeatI.Text);
        quint8 ESI_Temp_D_value = ui.UI_TW_TEMP_CTRL_CT->item(5,0)->text().toUInt();//byte.Parse(ESIHeatD.Text);
        quint8 APCI_Temp_P_value = ui.UI_TW_TEMP_CTRL_CT->item(3,1)->text().toUInt();//byte.Parse(APCIHeatP.Text);
        quint8 APCI_Temp_I_value = ui.UI_TW_TEMP_CTRL_CT->item(4,1)->text().toUInt();//byte.Parse(APCIHeatI.Text);
        quint8 APCI_Temp_D_value = ui.UI_TW_TEMP_CTRL_CT->item(5,1)->text().toUInt();//byte.Parse(APCIHeatD.Text);
        quint8 Curtain_Temp_P_value = ui.UI_TW_TEMP_CTRL_CT->item(3,2)->text().toUInt();//byte.Parse(CurtainHeatP.Text);
        quint8 Curtain_Temp_I_value = ui.UI_TW_TEMP_CTRL_CT->item(4,2)->text().toUInt();//byte.Parse(CurtainHeatI.Text);
        quint8 Curtain_Temp_D_value = ui.UI_TW_TEMP_CTRL_CT->item(5,2)->text().toUInt();//byte.Parse(CurtainHeatD.Text);

        double Neb_Gas_Set_Flow_value = ui.UI_TW_GAS_CTRL_CT->item(0,0)->text().toDouble();//double.Parse(Neb_Gas_SetFlow.Text);
        double Heat_Gas_Set_Flow_value = ui.UI_TW_GAS_CTRL_CT->item(0,1)->text().toDouble();//double.Parse(Heat_Gas_SetFlow.Text);
        double Curtain_Gas_Set_Flow_value = ui.UI_TW_GAS_CTRL_CT->item(0,2)->text().toDouble();//double.Parse(Curtain_Gas_SetFlow.Text);
        double CID_Gas_Set_Flow_value = ui.UI_TW_GAS_CTRL_CT->item(0,3)->text().toDouble();//double.Parse(CID_Gas_SetFlow.Text);
        quint8 Neb_Gas_P_value = ui.UI_TW_GAS_CTRL_CT->item(3,0)->text().toUInt();//byte.Parse(Neb_Gas_P.Text);
        quint8 Neb_Gas_I_value = ui.UI_TW_GAS_CTRL_CT->item(4,0)->text().toUInt();//byte.Parse(Neb_Gas_I.Text);
        quint8 Neb_Gas_D_value = ui.UI_TW_GAS_CTRL_CT->item(5,0)->text().toUInt();//byte.Parse(Neb_Gas_D.Text);
        quint8 Heat_Gas_P_value = ui.UI_TW_GAS_CTRL_CT->item(3,1)->text().toUInt();//byte.Parse(Heat_Gas_P.Text);
        quint8 Heat_Gas_I_value = ui.UI_TW_GAS_CTRL_CT->item(4,1)->text().toUInt();//byte.Parse(Heat_Gas_I.Text);
        quint8 Heat_Gas_D_value = ui.UI_TW_GAS_CTRL_CT->item(5,1)->text().toUInt();//byte.Parse(Heat_Gas_D.Text);
        quint8 Curtain_Gas_P_value = ui.UI_TW_GAS_CTRL_CT->item(3,2)->text().toUInt();//byte.Parse(Curtain_Gas_P.Text);
        quint8 Curtain_Gas_I_value = ui.UI_TW_GAS_CTRL_CT->item(4,2)->text().toUInt();//byte.Parse(Curtain_Gas_I.Text);
        quint8 Curtain_Gas_D_value = ui.UI_TW_GAS_CTRL_CT->item(5,2)->text().toUInt();//byte.Parse(Curtain_Gas_D.Text);
        quint8 CID_Gas_P_value = ui.UI_TW_GAS_CTRL_CT->item(3,3)->text().toUInt();//byte.Parse(CID_Gas_P.Text);
        quint8 CID_Gas_I_value = ui.UI_TW_GAS_CTRL_CT->item(4,3)->text().toUInt();//byte.Parse(CID_Gas_I.Text);
        quint8 CID_Gas_D_value = ui.UI_TW_GAS_CTRL_CT->item(5,3)->text().toUInt();//byte.Parse(CID_Gas_D.Text);

        double ESI_Set_Temp_Gain, ESI_Set_Temp_Offset;
        if (ui.HeaterID->currentText() == "小"){
            ESI_Set_Temp_Gain = 28.971;
            ESI_Set_Temp_Offset = 3629.3;
        }else if(ui.HeaterID->currentText() == "大"){
            ESI_Set_Temp_Gain = 21.441;
            ESI_Set_Temp_Offset = 3666.4;
        }
        //将数据转成0-32768
        quint16 ESI_Set_Temp_D = (uint)(ESI_Set_Temp_value * 28.971 + 3629.3);
        quint16 APCI_Set_Temp_D = (uint)(APCI_Set_Temp_value * 32768/360);
        quint16 Curtain_Set_Temp_D = (uint)(Curtain_Set_Temp_value * 18.504+3975.1);

        quint16 Neb_Set_Flow_D = (uint)(Neb_Gas_Set_Flow_value * 65535/15);
        quint16 Heat_Set_Flow_D = (uint)(Heat_Gas_Set_Flow_value * 65535/15);
        quint16 Curtain_Set_Flow_D = (uint)(Curtain_Gas_Set_Flow_value * 65535/5);
        quint16 CID_Set_Flow_D = (uint)(CID_Gas_Set_Flow_value * 65535/5);

        QByteArray QSenddata(70, 0);
        QSenddata[0] = 0x01;
        QSenddata[1] = 0xF3;
        QSenddata[2] = 0x01;
        QSenddata[3] = 0x03;

        QSenddata[4] = (ESI_Set_Temp_D & 0xFF);
        QSenddata[5] = ((ESI_Set_Temp_D >> 8) & 0xFF);
        QSenddata[6] = (APCI_Set_Temp_D & 0xFF);
        QSenddata[7] = ((APCI_Set_Temp_D >> 8) & 0xFF);
        QSenddata[8] = (Curtain_Set_Temp_D & 0xFF);
        QSenddata[9] = ((Curtain_Set_Temp_D >> 8) & 0xFF);
        QSenddata[10] = (ESI_Temp_P_value & 0xFF);
        QSenddata[11] = (ESI_Temp_I_value & 0xFF);
        QSenddata[12] = (ESI_Temp_D_value & 0xFF);
        QSenddata[13] = (APCI_Temp_P_value & 0xFF);
        QSenddata[14] = (APCI_Temp_I_value & 0xFF);
        QSenddata[15] = (APCI_Temp_D_value & 0xFF);
        QSenddata[16] = (Curtain_Temp_P_value & 0xFF);
        QSenddata[17] = (Curtain_Temp_I_value & 0xFF);
        QSenddata[18] = (Curtain_Temp_D_value & 0xFF);
        QSenddata[19] = (Neb_Set_Flow_D & 0xFF);
        QSenddata[20] = ((Neb_Set_Flow_D >> 8) & 0xFF);
        QSenddata[21] = (Heat_Set_Flow_D & 0xFF);
        QSenddata[22] = ((Heat_Set_Flow_D >> 8) & 0xFF);
        QSenddata[23] = (Curtain_Set_Flow_D & 0xFF);
        QSenddata[24] = ((Curtain_Set_Flow_D >> 8) & 0xFF);
        QSenddata[25] = (CID_Set_Flow_D & 0xFF);
        QSenddata[26] = ((CID_Set_Flow_D >> 8 ) & 0xFF);
        QSenddata[27] = (Neb_Gas_P_value & 0xFF);
        QSenddata[28] = (Neb_Gas_I_value & 0xFF);
        QSenddata[29] = (Neb_Gas_D_value & 0xFF);
        QSenddata[30] = (Heat_Gas_P_value & 0xFF);
        QSenddata[31] = (Heat_Gas_I_value & 0xFF);
        QSenddata[32] = (Heat_Gas_D_value & 0xFF);
        QSenddata[33] = (Curtain_Gas_P_value & 0xFF);
        QSenddata[34] = (Curtain_Gas_I_value & 0xFF);
        QSenddata[35] = (Curtain_Gas_D_value & 0xFF);
        QSenddata[36] = (CID_Gas_P_value & 0xFF);
        QSenddata[37] = (CID_Gas_I_value & 0xFF);
        QSenddata[38] = (CID_Gas_D_value & 0xFF);
        QSenddata[39] = ui.IG0_RF_ON->text()== "IG0_ON"? 0xFF: 0x00;//(IG0_RF_on_D & 0xFF);
        QSenddata[40] = ui.IG12_RF_ON->text()== "IG12_ON"? 0xFF: 0x00;//(IG12_RF_on_D & 0xFF);
        QSenddata[41] = ui.CC_RF_ON->text()== "CC_ON"? 0xFF: 0x00;//(CC_RF_on_D & 0xFF);
        QSenddata[42] = ui.Q1_RF_ON->text()== "Q1_ON"? 0xFF: 0x00;//(Q1_RF_on_D & 0xFF);
        QSenddata[43] = ui.Q1_ABMode->text()== "B模式"? 0xFF: 0x00;//(Q1_AB_mode_D & 0xFF);
        QSenddata[44] = ui.Q3_RF_on_ctrl->text()== "Q3_ON"? 0xFF: 0x00;//(Q3_RF_on_D & 0xFF);
        QSenddata[45] = ui.Q3_ABmode_ctrl->text()== "B模式"? 0xFF: 0x00;//(Q3_AB_mode_D & 0xFF);
        QSenddata[46] = ui.ESI_HV_ON->text()=="ESI_ON"? 0xFF: 0x00;//(ESI_HV_on_D & 0xFF);
        QSenddata[47] = ui.CUR_HV_ON->text()== "CUR_ON"? 0xFF: 0x00;//(Curtain_HV_on_D & 0xFF);
        QSenddata[48] = ui.HED_HV_ON->text()== "HED_ON"? 0xFF: 0x00;//(HED_HV_on_D & 0xFF);
        QSenddata[49] = ui.DET_HV_ON->text()== "DET_ON"? 0xFF: 0x00;//(DET_HV_on_D & 0xFF);
        QSenddata[50] = ui.Auto_StartUp->text()== "自动开真空"? 0xFF: 0x00;//(Auto_StartUP_on_D & 0xFF);
        QSenddata[51] = ui.Auto_ShutDown->text()== "自动关真空"? 0xFF: 0x00;//(Auto_ShutDown_on_D & 0xFF);
        QSenddata[52] = ui.Rpump_ON->text()== "开机械泵"? 0xFF: 0x00;//(Rpump_on_D & 0xFF);
        QSenddata[53] = ui.Tpump_ON->text()=="开分子泵"? 0xFF: 0x00;//(Tpump_on_D & 0xFF);
        QSenddata[54] = ui.IG_ON->text()=="开IG"? 0xFF: 0x00;//(IG_on_D & 0xFF);
        QSenddata[55] = ui.Purge_Gas_ON->text()=="开进气"? 0xFF: 0x00;//(Purge_Gas_on_D & 0xFF);
        QSenddata[56] = ui.Heat_PW_ON->property("sw").toUInt();//(Heat_PW_on_D & 0xFF);
        QSenddata[57] = ui.Heat_ON->text()== "加热开"? 0xFF: 0x00;//(Heat_on_D & 0xFF);
        QSenddata[58] = ui.CID_Gas_ON->text()=="CIDON"? 0xFF: 0x00;//(CID_Gas_on_D & 0xFF);
        QSenddata[59] = ui.CID_Rel_ON->property("sw").toUInt();//(CID_Rel_on_D & 0xFF);
        QSenddata[60] = (0 & 0xFF);
        QSenddata[61] = (0 & 0xFF);
        QSenddata[62] = (0 & 0xFF);
        QSenddata[63] = (0 & 0xFF);
        QSenddata[64] = (0 & 0xFF);
        QSenddata[65] = (0 & 0xFF);
        QSenddata[66] = (0 & 0xFF);
        QSenddata[67] = (0 & 0xFF);

        QSenddata[68] = 0x0D;
        QSenddata[69] = 0x0A;
        return QSenddata;
    }
    static QByteArray stateReqCMD(){
        QByteArray reqCMD(70, 0);
        reqCMD[0] = 0x01;
        reqCMD[1] = 0xF3;
        reqCMD[2] = 0x01;
        reqCMD[3] = 0x04;
        reqCMD[68] = 0x0D;
        reqCMD[69] = 0x0A;
        return reqCMD;
    }

private slots:
    void serialPort1DataReceived(){
        QByteArray QRecivedata= mQRecivedata;
        if (QRecivedata.size()< 120)
            return;
        if (QRecivedata.at(3) != 0x04)
            return;

        uint ESIGetTempValueD = (uint)(QRecivedata[4] | (QRecivedata[5] << 8));
        uint APCIGetTempValueD = (uint)(QRecivedata[6] | (QRecivedata[7] << 8));
        uint CurtainGetTempValueD = (uint)(QRecivedata[8] | (QRecivedata[9] << 8));
        ushort ESITempPWMD = (ushort)(QRecivedata[10] | (QRecivedata[11] << 8));
        ushort APCITempPWMD = (ushort)(QRecivedata[12] | (QRecivedata[13] << 8));
        ushort CurtainTempPWMD = (ushort)(QRecivedata[14] | (QRecivedata[15] << 8));
        ushort Neb_Gas_Flow_D = (ushort)(QRecivedata[16] | (QRecivedata[17] << 8));
        ushort Heat_Gas_Flow_D = (ushort)(QRecivedata[18] | (QRecivedata[19] << 8));
        ushort Curtain_Gas_Flow_D = (ushort)(QRecivedata[20] | (QRecivedata[21] << 8));
        ushort CID_Gas_Flow_D = (ushort)(QRecivedata[22] | (QRecivedata[23] << 8));
        ushort Neb_Gas_Flow_PID_D = (ushort)(QRecivedata[24] | (QRecivedata[25] << 8));
        ushort Heat_Gas_Flow_PID_D = (ushort)(QRecivedata[26] | (QRecivedata[27] << 8));
        ushort Curtain_Gas_Flow_PID_D = (ushort)(QRecivedata[28] | (QRecivedata[29] << 8));
        ushort CID_Gas_Flow_PID_D = (ushort)(QRecivedata[30] | (QRecivedata[31] << 8));
        ushort IG0_Tuning_D = (ushort)(QRecivedata[32] | (QRecivedata[33] << 8));
        ushort IG12_Tuning_D = (ushort)(QRecivedata[34] | (QRecivedata[35] << 8));
        ushort CC_Tuning_D = (ushort)(QRecivedata[36] | (QRecivedata[37] << 8));
        ushort Q1_Tuning_D = (ushort)(QRecivedata[38] | (QRecivedata[39] << 8));
        ushort Q1_FB_Temp_D = (ushort)(QRecivedata[40] | (QRecivedata[41] << 8));
        ushort Q3_Tuning_D = (ushort)(QRecivedata[42] | (QRecivedata[43] << 8));
        ushort Q3_FB_Temp_D = (ushort)(QRecivedata[44] | (QRecivedata[45] << 8));
        ushort Lens_DC1_D = (ushort)(QRecivedata[46] | (QRecivedata[47] << 8));
        ushort Lens_DC2_D = (ushort)(QRecivedata[48] | (QRecivedata[49] << 8));
        ushort Lens_DC3_D = (ushort)(QRecivedata[50] | (QRecivedata[51] << 8));
        ushort Lens_DC4_D = (ushort)(QRecivedata[52] | (QRecivedata[53] << 8));
        ushort Lens_DC5_D = (ushort)(QRecivedata[54] | (QRecivedata[55] << 8));
        ushort Lens_DC6_D = (ushort)(QRecivedata[56] | (QRecivedata[57] << 8));
        ushort Lens_DC7_D = (ushort)(QRecivedata[58] | (QRecivedata[59] << 8));
        ushort Lens_DC8_D = (ushort)(QRecivedata[60] | (QRecivedata[61] << 8));
        ushort Lens_DC9_D = (ushort)(QRecivedata[62] | (QRecivedata[63] << 8));
        ushort Lens_DC10_D = (ushort)(QRecivedata[64] | (QRecivedata[65] << 8));
        ushort Lens_DC11_D = (ushort)(QRecivedata[66] | (QRecivedata[67] << 8));
        ushort Lens_DC12_D = (ushort)(QRecivedata[68] | (QRecivedata[69] << 8));
        ushort Lens_DC13_D = (ushort)(QRecivedata[70] | (QRecivedata[71] << 8));
        ushort Lens_DC14_D = (ushort)(QRecivedata[72] | (QRecivedata[73] << 8));
        ushort Lens_DC15_D = (ushort)(QRecivedata[74] | (QRecivedata[75] << 8));
        ushort Lens_DC16_D = (ushort)(QRecivedata[76] | (QRecivedata[77] << 8));
        ushort ESI_HV_VFB_D = (ushort)(QRecivedata[78] | (QRecivedata[79] << 8));
        ushort ESI_HV_IFB_D = (ushort)(QRecivedata[80] | (QRecivedata[81] << 8));
        ushort Curtain_HV_VFB_D = (ushort)(QRecivedata[82] | (QRecivedata[83] << 8));
        ushort HED_HV_VFB_D = (ushort)(QRecivedata[84] | (QRecivedata[85] << 8));
        ushort DET_HV_VFB_D = (ushort)(QRecivedata[86] | (QRecivedata[87] << 8));
        ushort Tpump_Frq_D = (ushort)(QRecivedata[88] | (QRecivedata[89] << 8));
        ushort Rpump_Frq_D = (ushort)(QRecivedata[90] | (QRecivedata[91] << 8));
        ushort IG_P_value_D = (ushort)(QRecivedata[92] | (QRecivedata[93] << 8));
        ushort PG_P_value_D = (ushort)(QRecivedata[94] | (QRecivedata[95] << 8));
        ushort IS_ID_value_D = (ushort)(QRecivedata[96] | (QRecivedata[97] << 8));
        quint8 IG0_RF_State_D = QRecivedata[98];
        quint8 IG12_RF_State_D = QRecivedata[99];
        quint8 CC_RF_State_D = QRecivedata[100];
        quint8 Q1_RF_State_D = QRecivedata[101];
        quint8 Q3_RF_State_D = QRecivedata[102];
        quint8 IG_Fan_State_D = QRecivedata[103];
        quint8 CC_Fan_State_D = QRecivedata[104];
        quint8 Q1_Fan_State_D = QRecivedata[105];
        quint8 Q3_Fan_State_D = QRecivedata[106];
        quint8 ESI_HV_State_D = QRecivedata[107];
        quint8 Curtain_HV_State_D = QRecivedata[108];
        quint8 HED_HV_State_D = QRecivedata[109];
        quint8 DET_HV_State_D = QRecivedata[110];
        quint8 Heat_Fan_State_D = QRecivedata[111];
        quint8 Tpump_Fan_State_D = QRecivedata[112];
        quint8 IS_Door_State_D = QRecivedata[113];

        double ESI_Get_Temp_Gain, ESI_Get_Temp_Offset;
        if(ui.HeaterID->currentText() == "小"){
            ESI_Get_Temp_Gain = 0.0345;
            ESI_Get_Temp_Offset = 125.09;
        }else if(ui.HeaterID->currentText() == "大"){
            ESI_Get_Temp_Gain = 0.0466;
            ESI_Get_Temp_Offset = 169.97;
        }

        double ESI_Get_Temp_value = (double)ESIGetTempValueD * ESI_Get_Temp_Gain - ESI_Get_Temp_Offset;
        double APCI_Get_Temp_value = (double)APCIGetTempValueD * 360 / 32768;
        double Curtain_Get_Temp_value = (double)CurtainGetTempValueD * 0.0536 - 211.75;

        ushort ESI_Temp_PWM_value = ESITempPWMD;
        ushort APCI_Temp_PWM_value = APCITempPWMD;
        ushort Curtain_Temp_PWM_value = CurtainTempPWMD;

        double Neb_Gas_Flow_value = (double)Neb_Gas_Flow_D * 5 / 4096;
        double Heat_Gas_Flow_value = (double)Heat_Gas_Flow_D * 5 / 4096;
        double Curtain_Gas_Flow_value = (double)Curtain_Gas_Flow_D * 5 / 4096;
        double CID_Gas_Flow_value = (double)CID_Gas_Flow_D * 5 / 4096;

        ushort Neb_Gas_PID_value = Neb_Gas_Flow_PID_D;
        ushort Heat_Gas_PID_value = Heat_Gas_Flow_PID_D;
        ushort Curtain_Gas_PID_value = Curtain_Gas_Flow_PID_D;
        ushort CID_Gas_PID_value = CID_Gas_Flow_PID_D;

        double IG0_Tuning_value = (double)IG0_Tuning_D * 5 / 4096;
        double IG12_Tuning_value = (double)IG12_Tuning_D * 5 / 4096;
        double CC_Tuning_value = (double)CC_Tuning_D * 5 / 4096;
        double Q1_Tuning_value = (double)Q1_Tuning_D * 5 / 4096;
        double Q1_FB_Temp_value = (double)Q1_FB_Temp_D * 5 / 4096;
        double Q3_Tuning_value = (double)Q3_Tuning_D * 5 / 4096;
        double Q3_FB_Temp_value = (double)Q3_FB_Temp_D * 5 / 4096;

        double Lens_DC1_value = (double)Lens_DC1_D * 5 / 4096;
        double Lens_DC2_value = (double)Lens_DC2_D * 5 / 4096;
        double Lens_DC3_value = (double)Lens_DC3_D * 5 / 4096;
        double Lens_DC4_value = (double)Lens_DC4_D * 5 / 4096;
        double Lens_DC5_value = (double)Lens_DC5_D * 5 / 4096;
        double Lens_DC6_value = (double)Lens_DC6_D * 5 / 4096;
        double Lens_DC7_value = (double)Lens_DC7_D * 5 / 4096;
        double Lens_DC8_value = (double)Lens_DC8_D * 5 / 4096;
        double Lens_DC9_value = (double)Lens_DC9_D * 5 / 4096;
        double Lens_DC10_value = (double)Lens_DC10_D * 5 / 4096;
        double Lens_DC11_value = (double)Lens_DC11_D * 5 / 4096;
        double Lens_DC12_value = (double)Lens_DC12_D * 5 / 4096;
        double Lens_DC13_value = (double)Lens_DC13_D * 5 / 4096;
        double Lens_DC14_value = (double)Lens_DC14_D * 5 / 4096;
        double Lens_DC15_value = (double)Lens_DC15_D * 5 / 4096;
        double Lens_DC16_value = (double)Lens_DC16_D * 5 / 4096;

        double ESI_HV_VFB_value = (double)ESI_HV_VFB_D * 5 / 4096;
        double ESI_HV_IFB_value = (double)ESI_HV_IFB_D * 5 / 4096;
        double Curtain_HV_VFB_value = (double)Curtain_HV_VFB_D * 5 / 4096;
        double HED_HV_VFB_value = (double)HED_HV_VFB_D * 5 / 4096;
        double DET_HV_VFB_value = (double)DET_HV_VFB_D * 5 / 4096;

        Tpump_Frq_value = (double)Tpump_Frq_D * 5 / 4096;
        double Rpump_Frq_value = (double)Rpump_Frq_D * 5 / 4096;

        double IG_P_value = (double)IG_P_value_D * 5 / 4096;
        double PG_P_value = (double)PG_P_value_D * 5 / 4096;

        double IS_ID_value = IS_ID_value_D * 360 / 32768;

        ui.UI_TW_TEMP_CTRL_CT->item(1,0)->setText(QString::number(ESI_Get_Temp_value));
        ui.UI_TW_TEMP_CTRL_CT->item(1,1)->setText(QString::number(APCI_Get_Temp_value));
        ui.UI_TW_TEMP_CTRL_CT->item(1,2)->setText(QString::number(Curtain_Get_Temp_value));

        ui.UI_TW_TEMP_CTRL_CT->item(2,0)->setText(QString::number(ESIGetTempValueD));
        ui.UI_TW_TEMP_CTRL_CT->item(2,1)->setText(QString::number(APCIGetTempValueD));
        ui.UI_TW_TEMP_CTRL_CT->item(2,2)->setText(QString::number(CurtainGetTempValueD));

        ui.UI_TW_TEMP_CTRL_CT->item(6,0)->setText(QString::number(ESI_Temp_PWM_value));
        ui.UI_TW_TEMP_CTRL_CT->item(6,1)->setText(QString::number(APCI_Temp_PWM_value));
        ui.UI_TW_TEMP_CTRL_CT->item(6,2)->setText(QString::number(Curtain_Temp_PWM_value));

        ui.UI_TW_GAS_CTRL_CT->item(1,0)->setText(QString::number(Neb_Gas_Flow_value));
        ui.UI_TW_GAS_CTRL_CT->item(1,1)->setText(QString::number(Heat_Gas_Flow_value));
        ui.UI_TW_GAS_CTRL_CT->item(1,2)->setText(QString::number(Curtain_Gas_Flow_value));
        ui.UI_TW_GAS_CTRL_CT->item(1,3)->setText(QString::number(CID_Gas_Flow_value));

        ui.UI_TW_GAS_CTRL_CT->item(2,0)->setText(QString::number(Neb_Gas_Flow_D));
        ui.UI_TW_GAS_CTRL_CT->item(2,0)->setText(QString::number(Heat_Gas_Flow_D));
        ui.UI_TW_GAS_CTRL_CT->item(2,0)->setText(QString::number(Curtain_Gas_Flow_D));
        ui.UI_TW_GAS_CTRL_CT->item(2,0)->setText(QString::number(CID_Gas_Flow_D));

        ui.UI_TW_GAS_CTRL_CT->item(6,0)->setText(QString::number(Neb_Gas_PID_value));
        ui.UI_TW_GAS_CTRL_CT->item(6,0)->setText(QString::number(Heat_Gas_PID_value));
        ui.UI_TW_GAS_CTRL_CT->item(6,0)->setText(QString::number(Curtain_Gas_PID_value));
        ui.UI_TW_GAS_CTRL_CT->item(6,0)->setText(QString::number(CID_Gas_PID_value));

        ui.IG0_RF_Tuning->setText(QString::number(IG0_Tuning_value));
        ui.IG12_RF_Tuning->setText(QString::number(IG12_Tuning_value));
        ui.CC_RF_Tuning->setText(QString::number(CC_Tuning_value));
        ui.Q1_RF_Tuning->setText(QString::number(Q1_Tuning_value));
        ui.Q1_FB_Temp->setText(QString::number(Q1_FB_Temp_value));
        ui.Q3_RF_Tuning->setText(QString::number(Q3_Tuning_value));
        ui.Q3_FB_Temp->setText(QString::number(Q3_FB_Temp_value));

        ui.Lens_DC1->setText(QString::number(Lens_DC1_value));
        ui.Lens_DC2->setText(QString::number(Lens_DC2_value));
        ui.Lens_DC3->setText(QString::number(Lens_DC3_value));
        ui.Lens_DC4->setText(QString::number(Lens_DC4_value));
        ui.Lens_DC5->setText(QString::number(Lens_DC5_value));
        ui.Lens_DC6->setText(QString::number(Lens_DC6_value));
        ui.Lens_DC7->setText(QString::number(Lens_DC7_value));
        ui.Lens_DC8->setText(QString::number(Lens_DC8_value));
        ui.Lens_DC9->setText(QString::number(Lens_DC9_value));
        ui.Lens_DC10->setText(QString::number(Lens_DC10_value));
        ui.Lens_DC11->setText(QString::number(Lens_DC11_value));
        ui.Lens_DC12->setText(QString::number(Lens_DC12_value));
        ui.Lens_DC13->setText(QString::number(Lens_DC13_value));
        ui.Lens_DC14->setText(QString::number(Lens_DC14_value));
        ui.Lens_DC15->setText(QString::number(Lens_DC15_value));
        ui.Lens_DC16->setText(QString::number(Lens_DC16_value));

        ui.ESI_HV_VFB->setText(QString::number(ESI_HV_VFB_value));
        ui.ESI_HV_IFB->setText(QString::number(ESI_HV_IFB_value));
        ui.Curtain_HV_VFB->setText(QString::number(Curtain_HV_VFB_value));
        ui.HED_HV_VFB->setText(QString::number(HED_HV_VFB_value));
        ui.DET_HV_VFB->setText(QString::number(DET_HV_VFB_value));

        ui.Tpump_Frq->setText(QString::number(Tpump_Frq_value));
        ui.Rpump_Frq->setText(QString::number(Rpump_Frq_value));

        ui.IG_P->setText(QString::number(IG_P_value));
        ui.PG_P->setText(QString::number(PG_P_value));

        ui.IS_ID->setText(QString::number(IS_ID_value));

        if (IS_Door_State_D){
            ui.IS_Door->setStyleSheet("QLineEdit { background-color: LightGreen; }");
        }else{
            ui.IS_Door->setStyleSheet("QLineEdit { background-color: LightCoral; }");
        }

        if (Heat_Fan_State_D){
            ui.Heat_Fan_State->setStyleSheet("QLineEdit { background-color: LightGreen; }");
        }else{
            ui.Heat_Fan_State->setStyleSheet("QLineEdit { background-color: LightCoral; }");
        }

        if(IG_Fan_State_D){
            ui.IG_Fan_State->setStyleSheet("QLineEdit { background-color: LightGreen; }");
        }else{
            ui.IG_Fan_State->setStyleSheet("QLineEdit { background-color: LightCoral; }");
        }

        if(CC_Fan_State_D){
            ui.CC_Fan_State->setStyleSheet("QLineEdit { background-color: LightGreen; }");
        }else{
            ui.CC_Fan_State->setStyleSheet("QLineEdit { background-color: LightCoral; }");
        }
    }
};

#endif // SCTRLTFG_H
