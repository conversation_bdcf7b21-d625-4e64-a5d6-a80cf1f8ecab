#include "sSingleAcquisition.h"
//#include <sMethod/cTQ_StructCMD_HZH.h>
#include <HWConnection.h>
#include <cFooting/cAcqFunction.h>
#include "sMethod/parmeter_structs.h"
#include "UMAStructure/UMA_HCS_Data.h"//<UMA_HCS_Data.h>
//#include <USBConnection/cParamCCS.h>


using namespace UMA_HCS;
using namespace LabSolutionsRW;

bool sSingleAcquisition::scanAnalyzeXIC(const double* pX,
                                        const double* pY,
                                        const _CONGIG_OMS::_STRUCT_DATA& pSTRUCT_DATA,
                                        uint32_t currentEvt,
                                        uint32_t sizeTIC)
{
    mChartTIC->lockXIC();
    QMutableMapIterator<QString, _GRAPH_XIC*> tempIter((*(mChartTIC->getXIC()))[currentEvt]);
    while (tempIter.hasNext()) {
        double tempY=0;
        double tempMass=tempIter.next().key().toDouble();
        _GRAPH_XIC* pGRAPH_XIC=tempIter.value();
        uint32_t sizeY=pGRAPH_XIC->yListXIC.size();
        double tempFirstM= tempMass- pGRAPH_XIC->massRange;
        double tempLastM= tempMass+ pGRAPH_XIC->massRange;
        uint32_t sizeF= pSTRUCT_DATA.uDelayPoint+ pSTRUCT_DATA. uPrePoint+pSTRUCT_DATA.uEventPoint;
        int countY=0;
        if((tempLastM< pX[pSTRUCT_DATA.uPrePoint])||
                (tempFirstM> pX[sizeF-1])){
            if(sizeY>0)
                vectorOperate::Clear(pGRAPH_XIC->yListXIC);
        }else{
            for(uint32_t i= pSTRUCT_DATA.uDelayPoint+ pSTRUCT_DATA.uPrePoint; i< sizeF; ++i){
                if(pX[i]>tempLastM)
                    break;
                if((pX[i]> tempFirstM)&&(pX[i]< tempLastM)){
                    ++countY;
                    tempY=tempY/(countY)*(countY-1)+ pY[i]/(countY);
                }
            }
            if(sizeY==0){
                pGRAPH_XIC->yListXIC.resize(sizeTIC,tempY);
            }else{
                pGRAPH_XIC->yListXIC.resize(sizeTIC);
                if(sizeY> sizeTIC)//maximun size
                    pGRAPH_XIC->yListXIC.shrink_to_fit();
            }
            pGRAPH_XIC->yListXIC[sizeTIC-1]= tempY* pGRAPH_XIC->Gain+ pGRAPH_XIC->Offset;
        }
    }
    mChartTIC->unLockXIC();
    return true;
}

bool sSingleAcquisition::simAnalyzeXIC(const std::vector<double>& pX,
                                       const std::vector<double>& pY,
                                       const _CONGIG_OMS::_STRUCT_DATA& pSTRUCT_DATA,
                                       uint32_t currentEvt,
                                       uint32_t sizeTIC)
{
    mChartTIC->lockXIC();
    QMutableMapIterator<QString, _GRAPH_XIC*> tempIter((*(mChartTIC->getXIC()))[currentEvt]);
    while (tempIter.hasNext()) {
        double tempY=0;
        double tempMass=tempIter.next().key().toDouble();
        _GRAPH_XIC* pGRAPH_XIC=tempIter.value();
        uint32_t sizeY=pGRAPH_XIC->yListXIC.size();
        double tempFirstM= tempMass- pGRAPH_XIC->massRange;
        double tempLastM= tempMass+ pGRAPH_XIC->massRange;
        uint32_t sizeF= pX.size();
        for(uint32_t i= 0; i< sizeF; ++i){
            if((pX[i]> tempFirstM)&&(pX[i]< tempLastM))
                tempY=pY[i];
        }
        if(sizeY==0){
            pGRAPH_XIC->yListXIC.resize(sizeTIC,tempY);
        }else{
            pGRAPH_XIC->yListXIC.resize(sizeTIC);
            if(sizeY> sizeTIC)//maximun size
                pGRAPH_XIC->yListXIC.shrink_to_fit();
        }
        pGRAPH_XIC->yListXIC[sizeTIC-1]= tempY* pGRAPH_XIC->Gain+ pGRAPH_XIC->Offset;
    }
    mChartTIC->unLockXIC();
    return true;
}

bool sSingleAcquisition::AnalyzeXIC(const ListSTRUCT_DATA& listSTRUCT_DATA,
                                    quint32 sizeTIC,
                                    const QList<std::vector<double>>* mThreadBuffY,
                                    const QList<std::vector<double>>* mThreadBuffX)
{
    for(int currentEvt=0; currentEvt< listSTRUCT_DATA.size(); ++currentEvt){
        if(cParamValue::Type_SIM_2048== listSTRUCT_DATA[currentEvt].first){
            const _CONGIG_OMS::_STRUCT_DATA* pSTRUCT_DATA= &(listSTRUCT_DATA[currentEvt].second);
            return simAnalyzeXIC((*mThreadBuffX)[currentEvt], (*mThreadBuffY)[currentEvt],*pSTRUCT_DATA, currentEvt, sizeTIC);//create xic
        }else{
            const _CONGIG_OMS::_STRUCT_DATA* pSTRUCT_DATA= &(listSTRUCT_DATA[currentEvt].second);
            return scanAnalyzeXIC((*mThreadBuffX)[currentEvt].data(), (*mThreadBuffY)[currentEvt].data(),*pSTRUCT_DATA, currentEvt, sizeTIC);//create xic
        }
    }
    return false;
}

bool sSingleAcquisition::dataDismantleTIC(QByteArray& pByteArray, QByteArray& pMarkBody, double& xTIC, double& yTIC)
{
    if(pByteArray.size() < (int)sizeof(_StreamBody))
        return false;
    _StreamBody* pStreamBody = (_StreamBody*)(pByteArray.data());

    if(pStreamBody->lengthParam>0){
        cParamValue::_StreamBodyParam* pStreamBodyParam= (cParamValue::_StreamBodyParam*)(pByteArray.data()+ sizeof(_StreamBody));
        if(pStreamBodyParam->type== cParamValue::Type_Child_Body_TIC){
            cParamValue::_StreamBodyTIC* pStreamBodyTIC= cParamValue::_StreamBodyTIC::p(pStreamBodyParam);
            xTIC= pStreamBodyTIC->currentTime;
            yTIC= pStreamBodyTIC->currentAcc;
        }
    }
    qint32 offset= sizeof(_StreamBody)+ pStreamBody->lengthParam;
    pMarkBody.resize(offset);
    memcpy(pMarkBody.data(), pByteArray.data(), offset);

    double vac = 0.13e-2;
    QByteArray StreamBodySystemStateArray;
    StreamBodySystemStateArray.resize(sizeof (uint32_t)+sizeof(double));
    cParamValue::_StreamBodySystemState* pSystemState= (cParamValue::_StreamBodySystemState*)(StreamBodySystemStateArray.data());
    pSystemState->length= sizeof(double);
    memcpy(pSystemState->data, &vac, sizeof(double));

    StreamBodySystemStateArray= cParamValue::_StreamBodyParam::toStreamArray(cParamValue::Type_Child_Body_System_State,
                                                                             StreamBodySystemStateArray);
    pMarkBody.append(StreamBodySystemStateArray);
    pStreamBody = reinterpret_cast<_StreamBody*>(pMarkBody.data());
    pStreamBody->lengthParam+= cParamValue::_StreamBodySystemState::size(sizeof(double));
    pStreamBody->length+= cParamValue::_StreamBodySystemState::size(sizeof(double));

    //    qint32 uAllPoint= (pByteArray.size()- offset)/ sizeof(double);
    //    double* pdbHead= (double*)(pByteArray.data());
    //    double* pdbOffset= (double*)(pByteArray.data()+ offset);
    //    for(qint32 i=0; i<uAllPoint; i++){
    //        *pdbHead= *pdbOffset;
    //        pdbHead++;
    //        pdbOffset++;
    //    }
    //    pByteArray.resize(uAllPoint*sizeof(double));
    QByteArray tempArray;
    tempArray.resize(pByteArray.size()- offset);
    memcpy(tempArray.data(), pByteArray.data()+ offset, tempArray.size());

    qint32 uAllPoint= tempArray.size()/ _StreamBody::size(_StreamBody::Type_FloatCompress);
    pByteArray.resize(uAllPoint* sizeof(double));
    _StreamBody::memcpy((double*)(pByteArray.data()), (double*)(tempArray.data()),
                        uAllPoint, _StreamBody::Type_FloatCompress);
    return true;
}

bool sSingleAcquisition::AnalyzeScan(double* pX, double* pY,
                                     _CONGIG_OMS::_STRUCT_DATA& pSTRUCT_DATA,
                                     int currentEvt, int countEvt,
                                     _STRUCT_PEAK& pSTRUCT_PEAK, /*double baseX, */
                                     quint32 uWidth)
{
#if 0

#endif
}

void sSingleAcquisition::updateScanParam()
{
    m_msSegments = m_pMsEvtSplitter->massSegments();
}

int sSingleAcquisition::analyzeThread(void *pParam, const bool &bRunning)
{

    sSingleAcquisition* pDataAcquisiton = (sSingleAcquisition*)pParam;
    UMA_HCS::HCSCommandUSB *pHCSCommUSB= HWConnection::getUSB();

    pDataAcquisiton-> updateScanParam();
    double refVol = 0;//    double refVol = QSettings().value(uma_project::getQqQRefVoltage(), 0).toDouble();///< 基准电压
    uint    muRefVolDigit = 0;//analog -> digit
    if(!pDataAcquisiton->m_STRUCT_ADC_TDC.Mode){
        muRefVolDigit = static_cast<uint>((refVol + 4.197)/(2*4.197)*0xffff);//analog -> digit
    }

    HCSDataFrame dataFrame;
    MsChData msChsData;


    const std::vector<MassEventSplitter::MassSegment>& tmpVector= pDataAcquisiton->m_pMsEvtSplitter->massSegments();

    while(bRunning){
        if (!pHCSCommUSB->getDataDAQ(&dataFrame)){
            QThread::usleep(10);
            continue;
        }
        auto chFrame = dataFrame.innerData();
        //        SDBG(QString("lUMAChannelId:%1, lUMAChannelNo:%2, lUMAEventNo:%3, lUMASegmentNo:%4, lDataLen:%5")
        //             .arg(chFrame.lUMAChannelId).arg(chFrame.lUMAChannelNo).arg(chFrame.lUMAEventNo).arg(chFrame.lUMASegmentNo)
        //             .arg(chFrame.lDataLen));

        const MassEventSplitter::MassSegment& tmpMassSegment= tmpVector.at(chFrame.lUMASegmentNo);
        if(tmpMassSegment.events.size()<= chFrame.lUMAEventNo)
            continue;
        const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt= tmpMassSegment.events.at(chFrame.lUMAEventNo);
        int msChSize = umaEvt.listChannel.size();

        if (msChSize<= chFrame.lUMAChannelNo){
            msChsData.clear();
            continue;
        }

        if(msChsData.size()!= chFrame.lUMAChannelNo){
            msChsData.clear();
            continue;
        }

        if(!cAcqFunction::dataSplit(chFrame,
                                    pDataAcquisiton->m_STRUCT_ADC_TDC,
                                    muRefVolDigit,
                                    msChsData))
            continue;

        double tmpTIC= 0;

        if ( msChsData.size()== msChSize){
            //            SDBG(QString("lUMAChannelNo:%1 msChsData.size:%2 msChSize:%3")
            //                 .arg(chFrame.lUMAChannelNo)
            //                 .arg(msChsData.size())
            //                 .arg(msChSize));
            bool isOK= false;
            pDataAcquisiton->mGraphBuffMutex.lock();
            switch (umaEvt.Type) {
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                      pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                      msChsData,
                                                      1,
                                                      tmpTIC,
                                                      (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                      pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                      msChsData,
                                                      3,
                                                      tmpTIC,
                                                      (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_NEG:{
                isOK= cAcqFunction::fillChartBuffSIM(umaEvt,
                                                     pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                     msChsData,
                                                     1,
                                                     tmpTIC,
                                                     (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_NEG:{
                isOK= cAcqFunction::fillChartBuffSIM(umaEvt,
                                                     pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                     msChsData,
                                                     3,
                                                     tmpTIC,
                                                     (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_NEG:{
                isOK= cAcqFunction::fillChartBuffMRM(umaEvt,
                                                     pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                     msChsData,
                                                     //3,
                                                     tmpTIC,
                                                     (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                      pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                      msChsData,
                                                      3,
                                                      tmpTIC,
                                                      (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG:{
                isOK= cAcqFunction::fillChartBuffSIM(umaEvt,
                                                     pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                     msChsData,
                                                     3,
                                                     tmpTIC,
                                                     (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                      pDataAcquisiton->m_STRUCT_ADC_TDC,
                                                      msChsData,
                                                      1,
                                                      tmpTIC,
                                                      (*pDataAcquisiton->mGraphBuffX)[0],
                        (*pDataAcquisiton->mGraphBuffY)[0]);
            }break;
            default:/*pDataAcquisiton->mGraphBuffMutex.unlock();*/break;
            }
            if(isOK)
                pDataAcquisiton->mIsNewMass= true;
            pDataAcquisiton->mGraphBuffMutex.unlock();
        }


        if (msChsData.size()== msChSize){
            msChsData.clear();

            pDataAcquisiton->mSTRUCT_TIC.append(chFrame.dTimeMs/60000, tmpTIC);
            pDataAcquisiton->mIsNewTIC=true;

            QByteArray streamBody, dataBuffer;
            cAcqFunction::createStreamBody(streamBody, dataBuffer,
                                           (*pDataAcquisiton->mGraphBuffY)[0].size(),
                    (*pDataAcquisiton->mGraphBuffY)[0]);
            pDataAcquisiton->mAcqFunction.write2File(pDataAcquisiton->mAcqFunction.mOffsetFile, dataBuffer,
                                                     chFrame.dTimeMs/60000, tmpTIC, 0, 0, 0, streamBody);
        }
        QThread::usleep(1);
    }
    return 0;
}
