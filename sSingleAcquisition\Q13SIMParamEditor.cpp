﻿#include "Q13SIMParamEditor.h"

Q13SIMParamEditor::Q13SIMParamEditor(QString Q,
                                     sMapSetMZ* pMapSetMZ,
                                     QWidget* parent)
    : uiQ13SIMParamEditor(Q, parent),
      mMapSetMZ(pMapSetMZ)
{

}

void Q13SIMParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool Q13SIMParamEditor::getParamEvent(QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM,
                   QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM_LDAC,
                   int& Polarity)
{
    //SCANSPEED=15 质量校正
    bool ok=false;
    QVector<float> mass;
    QVector<float> DwellTimeMs;
    if(!getTableParam(mass, DwellTimeMs))
        return false;
    for(int row= 0; row< mass.size(); row++){
        cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmp_CHANNEL_EVENT_PARAM;
        cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmp_CHANNEL_EVENT_PARAM_LDAC;
        tmp_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_End=
                tmp_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_End=
                tmp_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_End=
                tmp_CHANNEL_EVENT_PARAM.Q1_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM.Q2_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM.Q3_Mz_Start=
                tmp_CHANNEL_EVENT_PARAM.Q1_Mz_End=
                tmp_CHANNEL_EVENT_PARAM.Q2_Mz_End=
                tmp_CHANNEL_EVENT_PARAM.Q3_Mz_End= mass[row];
        tmp_CHANNEL_EVENT_PARAM_LDAC.ScanTimeMs=
                tmp_CHANNEL_EVENT_PARAM.ScanTimeMs= DwellTimeMs[row];

        tmp_CHANNEL_EVENT_PARAM_LDAC.TDC_DAC=
        tmp_CHANNEL_EVENT_PARAM.TDC_DAC= 65535* (getLE_gating_mv(&ok)+ 2500)/ 5000;
        IF_RETURN_FALSE(ok);

        tmp_CHANNEL_EVENT_PARAM_LDAC.PauseTimeMs=
                tmp_CHANNEL_EVENT_PARAM.PauseTimeMs= getPauseTime(&ok);
        IF_RETURN_FALSE(ok);

        tmp_CHANNEL_EVENT_PARAM_LDAC.WaitTimeMs=
                tmp_CHANNEL_EVENT_PARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
        IF_RETURN_FALSE(ok);

        p_CHANNEL_EVENT_PARAM<< tmp_CHANNEL_EVENT_PARAM;
        p_CHANNEL_EVENT_PARAM_LDAC<< tmp_CHANNEL_EVENT_PARAM_LDAC;
    }
    if(p_CHANNEL_EVENT_PARAM.isEmpty()||p_CHANNEL_EVENT_PARAM_LDAC.isEmpty())
        return false;
    p_CHANNEL_EVENT_PARAM_LDAC.first().PN_SwitchTimeMs=
            p_CHANNEL_EVENT_PARAM.first().PN_SwitchTimeMs= getPolarity_switch_time(&ok);
    IF_RETURN_FALSE(ok);
    p_CHANNEL_EVENT_PARAM_LDAC.last().WaitTimeMs=
            p_CHANNEL_EVENT_PARAM.last().WaitTimeMs= getWaitTime(&ok);
    IF_RETURN_FALSE(ok);

    Polarity= getPolarity();
    return true;
}

