/********************************************************************************
** Form generated from reading UI file 'uiState.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UISTATE_H
#define UI_UISTATE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiState
{
public:
    QVBoxLayout *verticalLayout;
    QTabWidget *tabWidget;
    QWidget *tab;
    QVBoxLayout *UI_LAYOUT_TFG_STATE;
    QWidget *tab_2;
    QVBoxLayout *UI_LAYOUT_MSG_STATE;

    void setupUi(QWidget *uiState)
    {
        if (uiState->objectName().isEmpty())
            uiState->setObjectName(QString::fromUtf8("uiState"));
        uiState->resize(603, 516);
        verticalLayout = new QVBoxLayout(uiState);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        tabWidget = new QTabWidget(uiState);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        UI_LAYOUT_TFG_STATE = new QVBoxLayout(tab);
        UI_LAYOUT_TFG_STATE->setObjectName(QString::fromUtf8("UI_LAYOUT_TFG_STATE"));
        tabWidget->addTab(tab, QString());
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        UI_LAYOUT_MSG_STATE = new QVBoxLayout(tab_2);
        UI_LAYOUT_MSG_STATE->setObjectName(QString::fromUtf8("UI_LAYOUT_MSG_STATE"));
        tabWidget->addTab(tab_2, QString());

        verticalLayout->addWidget(tabWidget);


        retranslateUi(uiState);

        tabWidget->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(uiState);
    } // setupUi

    void retranslateUi(QWidget *uiState)
    {
        uiState->setWindowTitle(QApplication::translate("uiState", "Form", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab), QApplication::translate("uiState", "TFG_Ctrl", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_2), QApplication::translate("uiState", "MSG", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiState: public Ui_uiState {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UISTATE_H
