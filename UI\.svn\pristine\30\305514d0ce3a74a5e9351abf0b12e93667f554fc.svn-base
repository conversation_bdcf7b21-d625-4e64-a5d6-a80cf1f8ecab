#pragma once

#include <QWidget>
#include <cConfigTQ_HZH.h>
#include "ui_uiCalibrationMassItem.h"

class uiCalibrationMassItem : public QWidget
{
    Q_OBJECT

public:
    QString startMass, endMass;
    double mR2= 0;
    explicit uiCalibrationMassItem(QWidget *parent = nullptr);
    ~uiCalibrationMassItem();
    bool setParam(QString& str);
    QString getParam(_CONGIG_OMS::_PARAM_FIT &pPARAM_FIT);
    void setName(QString& str){
        ui.UI_L_Exponent_CAL->setText(str);
    }
    QString getName(){
        return ui.UI_L_Exponent_CAL->text();
    }
    void setScanSpeed(QString speed){
        mSpeed= speed;
    }
    QString getScanSpeed(){
        return mSpeed;
    }
    void showThumbnail(bool Thumbnail){
        if(Thumbnail){
            ui.UI_TW_TABLE_CAL->hide();
            ui.UI_W_RIGHT_CAL->hide();
        }else{
            ui.UI_TW_TABLE_CAL->show();
            ui.UI_W_RIGHT_CAL->show();
        }
    }
    bool isThumbnail(){
        return ui.UI_TW_TABLE_CAL->isHidden();
    }
    void setFocus(bool focus){
        if(focus)
            ui.UI_W_TITLE_CAL->setStyleSheet("QWidget { background-color: #4792B9;}");
        else
            ui.UI_W_TITLE_CAL->setStyleSheet("");
    }
    std::vector<double> mCoefF;
    bool calculate(const double* pSrc, double* pDest, int length){
        if(!calculate())
            return false;
        _CONGIG_OMS::_PARAM_FIT::calibrarion(pSrc, pDest, length, mCoefF);
        return true;
    }

private slots:
    void onClicked(){
        emit focused(mSpeed);
        if(isThumbnail()){
            showThumbnail(false);
        }else{
            showThumbnail(true);
        }
    }
    void on_UI_PB_APPLY_CAL_clicked();
    void on_UI_PB_CANCEL_CAL_clicked();
    void onAddFront(){
        int column= ui.UI_TW_TABLE_CAL->currentColumn();
        ui.UI_TW_TABLE_CAL->insertColumn(column);
        ui.UI_TW_TABLE_CAL->setItem(0,column, new QTableWidgetItem());
        ui.UI_TW_TABLE_CAL->setItem(1, column, new QTableWidgetItem());
        ui.UI_TW_TABLE_CAL->setItem(2, column, new QTableWidgetItem());
    }
    void onAddBehind(){
        int column= ui.UI_TW_TABLE_CAL->currentColumn();
        ui.UI_TW_TABLE_CAL->insertColumn(++column);
        ui.UI_TW_TABLE_CAL->setItem(0,column, new QTableWidgetItem());
        ui.UI_TW_TABLE_CAL->setItem(1, column, new QTableWidgetItem());
        ui.UI_TW_TABLE_CAL->setItem(2, column, new QTableWidgetItem());
    }
    void onRemove(){
        int column= ui.UI_TW_TABLE_CAL->currentColumn();
        ui.UI_TW_TABLE_CAL->removeColumn(column);
    }
    void on_UI_TW_TABLE_CAL_customContextMenuRequested(const QPoint &pos);

    void on_UI_PB_Calculate_CAL_clicked();

    void on_UI_TW_TABLE_CAL_clicked(const QModelIndex &index);

private:
    Ui::uiCalibrationMassItem ui;
    QMenu* popMenu= nullptr;
    QString mSpeed;
    _CONGIG_OMS::_PARAM_FIT mPARAM_FIT;

    bool calculate();
    virtual bool calcFun(
                         const _CONGIG_OMS::_PARAM_FIT& tmpPARAM_FIT, std::vector<double>& predictedValues,
            std::vector<double>& correction, QString& Formula, double& R2){
        return false;
    }

signals:
    void focused(QString speed);
};

