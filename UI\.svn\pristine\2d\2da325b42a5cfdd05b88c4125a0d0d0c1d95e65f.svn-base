#pragma once

#include <QWidget>
#include "ui_uiCalibrationView.h"
#include <LibWidget/sChartWidget.h>
//#include <USBConnection/cParamCCS.h>
//#include <HWConnection.h>
//#include <USBConnection/HCSCommandUSB.h>
#include <uiSingleAcquisition/uiQ13ScanParamEditor.h>
#include "uiCalibrationMassItem.h"
//#include <MassEventSplitter.h>
//#include <cMatrix.h>
//#include <cPublicStructHZH.h>
//#include <sThread.h>
#include <cPublicStructHZH.h>
#include <uiMapSetMZ.h>
//#include "cDebugFunctions.h"
//#include "cFooting/cAcqFunction.h"
class uiCalibrationView : public qUiWidget
{
    Q_OBJECT

public:
    QString FormulaTitle;

    explicit uiCalibrationView(QString pro,
                              uiMapSetMZ* pMapSetMZ,
                              QWidget *parent = nullptr);
    ~uiCalibrationView();
    virtual void initClass(QString& filePath);
    void setEditDisabled(bool disable= true){
        ui.UI_W_METHOD_CAL->setDisabled(disable);
    }

    virtual uiCalibrationMassItem* createItem(QString& str){
        uiCalibrationMassItem* pCalibrationMassItem= new uiCalibrationMassItem(this);
        if(!pCalibrationMassItem->setParam(str)){
            delete pCalibrationMassItem;
            pCalibrationMassItem= nullptr;
            return nullptr;
        }
        return pCalibrationMassItem;
    }
    void setParam(QString& str){
        if(str.isEmpty())
            return;
        QStringList tmpList= str.split(";");
        foreach (auto var, tmpList) {
//            uiCalibrationMassItem* pCalibrationMassItem= new uiCalibrationMassItem(this);
//            if(!pCalibrationMassItem->setParam(var)){
//                delete pCalibrationMassItem;
//                pCalibrationMassItem= nullptr;
//                continue;
//            }
            uiCalibrationMassItem* pCalibrationMassItem= createItem(var);
            if(pCalibrationMassItem== nullptr)
                continue;
            connect(pCalibrationMassItem, SIGNAL(focused(QString)), this, SLOT(onFocused(QString)));
            mCalibrationMassItemMap.insert(pCalibrationMassItem->getScanSpeed().toDouble(), pCalibrationMassItem);
            ui.UI_LAYOUT_SPEEDMAP_CAL->addWidget(pCalibrationMassItem);
        }
        ui.UI_LAYOUT_SPEEDMAP_CAL->addStretch();
    }
    QString getParam(){
        QStringList tmpList;
        for(auto key: mCalibrationMassItemMap.keys()){
            uiCalibrationMassItem* pCalibrationMassItem= mCalibrationMassItemMap[key];
            _CONGIG_OMS::_PARAM_FIT tmpPARAM_FIT;
            QString Formula= pCalibrationMassItem->getParam(tmpPARAM_FIT);
            tmpList<< QString("%1  %2  %3  %4  %5")
                    .arg(key).arg(pCalibrationMassItem->startMass)
                    .arg(pCalibrationMassItem->endMass).arg(Formula).arg(pCalibrationMassItem->mR2);
        }
        return tmpList.join(";");
    }
    uiQ13ScanParamEditor* getScanParamEditor(){
        return mQ13ScanParamEditor;
    }
    bool getParamDAQ(_STRUCT_ADC_TDC& pSTRUCT_ADC_TDC){
        pSTRUCT_ADC_TDC.ACC= ui.UI_LE_AcqACC_CAL->text().toInt();//daqAccNum;
        pSTRUCT_ADC_TDC.Frq= ui.UI_LE_AcqFreq_CAL->text().toInt();//daqFreqHz;
        if(ui.UI_CB_ACQ_MODE_CAL->currentText()== "TDC"){
            pSTRUCT_ADC_TDC.Mode= 1;
        }else {
            pSTRUCT_ADC_TDC.Mode= 0;
        }
        return true;
    }
    QString mPro;
    uiMapSetMZ* mMapSetMZ;
    sChartWidget* mChart = nullptr;
    uiQ13ScanParamEditor* mQ13ScanParamEditor = nullptr;
    uiCalibrationMassItem* mCurrentFocus= nullptr;
    QMap<double, uiCalibrationMassItem*> mCalibrationMassItemMap;
    virtual bool acquisitionStart(){
        return false;
    }

    virtual void acquisitionStop(){

    }
//signals:
//    void sStartScan(uiCalibrationView*);
//    void sStopScan(uiCalibrationView*);

public slots:
    void onFocused(QString speed){
        double speedD= speed.toDouble();
        for(auto key: mCalibrationMassItemMap.keys()){
            if(speedD== key){
                mCurrentFocus= mCalibrationMassItemMap[key];
                mCurrentFocus->setFocus(true);
            }else
                mCalibrationMassItemMap[key]->setFocus(false);
        }
    }

private slots:
    void on_UI_PB_ADDMASS_CAL_clicked();
    void on_UI_PB_DELETEMASS_CAL_clicked();

//public:
//    _STRUCT_ADC_TDC m_STRUCT_ADC_TDC;

//    {
//        if(mTimerRefreshID!=-1)
//            killTimer(mTimerRefreshID);
//        mTimerRefreshID=-1;
//        mAnalyzeThread->stop();
//    }

//protected:
//    SThread* mAnalyzeThread= nullptr;
//    int mTimerRefreshID = -1;
//    static int analyzeThread(void *pParam, const bool &bRunning);
//    void timerEvent(QTimerEvent *evt);
//    std::vector<MassEventSplitter::MassSegment> m_msSegments;
//    int umaChMapMsChs(const UMA_HCS::HCSDataFrame::HCSData &chData);
//    void updateScanParam();
//    QByteArray mDataBuffer;
//    std::vector<double> mGraphBuffX;//std::vector<double>* mGraphBuff[2];         //size-allPoint
//    std::vector<double> mGraphBuffY;
//    volatile bool mIsNewMass= false;
//    QMutex mGraphBuffMutex;

protected:
    Ui::uiCalibrationView ui;

    bool initUI(QString& filePath);
    void createToolBar();

};

