/********************************************************************************
** Form generated from reading UI file 'uiQueueSys.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIQUEUESYS_H
#define UI_UIQUEUESYS_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiQueueSys
{
public:

    void setupUi(QWidget *uiQueueSys)
    {
        if (uiQueueSys->objectName().isEmpty())
            uiQueueSys->setObjectName(QString::fromUtf8("uiQueueSys"));
        uiQueueSys->resize(400, 300);

        retranslateUi(uiQueueSys);

        QMetaObject::connectSlotsByName(uiQueueSys);
    } // setupUi

    void retranslateUi(QWidget *uiQueueSys)
    {
        uiQueueSys->setWindowTitle(QApplication::translate("uiQueueSys", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiQueueSys: public Ui_uiQueueSys {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIQUEUESYS_H
