#include "sSingleAcquisition.h"
#include <QFileDialog>
#include <sMethod/cTQ_FunctionCMD_AD81416.h>
#include <sMethod/cTQ_FunctionCMD_HZH.h>

void calQ1Only(int Polarity,
               sCalibrationMass* pCalibrationMass,
               cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_PARAM,
               cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_LDAC_PARAM)
{
    double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ1();
    //double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ3();
    if(Polarity)
        pCalibrationMass->getCalibrationViewQ1POS()->
                calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q1_Mz_End);
    else
        pCalibrationMass->getCalibrationViewQ1NEG()->
                calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q1_Mz_End);

    tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start=
            tmpCHANNEL_EVENT_PARAM.Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start;
    tmpCHANNEL_EVENT_PARAM.Q3_Mz_End=
            tmpCHANNEL_EVENT_PARAM.Q2_Mz_End= tmpCHANNEL_EVENT_PARAM.Q1_Mz_End;

    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_Start=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_Start=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start;
    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_End=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_End=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_End= tmpCHANNEL_EVENT_PARAM.Q1_Mz_End;
}

void calQ3Only(int Polarity,
               sCalibrationMass* pCalibrationMass,
               cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_PARAM,
               cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_LDAC_PARAM)
{
    //double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ1();
    double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ3();
    if(Polarity)
        pCalibrationMass->getCalibrationViewQ3POS()->
                calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q3_Mz_End);
    else
        pCalibrationMass->getCalibrationViewQ3NEG()->
                calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q3_Mz_End);

    tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start=
            tmpCHANNEL_EVENT_PARAM.Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start;
    tmpCHANNEL_EVENT_PARAM.Q1_Mz_End=
            tmpCHANNEL_EVENT_PARAM.Q2_Mz_End= tmpCHANNEL_EVENT_PARAM.Q3_Mz_End;

    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_Start=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_Start=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start;
    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_End=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_End=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_End= tmpCHANNEL_EVENT_PARAM.Q3_Mz_End;
}

void calQ1Q3(int Polarity,
             sCalibrationMass* pCalibrationMass,
             cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_PARAM,
             cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& tmpCHANNEL_EVENT_LDAC_PARAM)
{
    double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ1();
    double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM.getScanSpeedQ3();
    if(Polarity){
        pCalibrationMass->getCalibrationViewQ1POS()->
                calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q1_Mz_End);
        pCalibrationMass->getCalibrationViewQ3POS()->
                calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q3_Mz_End);
    }else{
        pCalibrationMass->getCalibrationViewQ1NEG()->
                calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q1_Mz_End);
        pCalibrationMass->getCalibrationViewQ3NEG()->
                calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM.Q3_Mz_End);
    }

    tmpCHANNEL_EVENT_PARAM.Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start;
    tmpCHANNEL_EVENT_PARAM.Q2_Mz_End= tmpCHANNEL_EVENT_PARAM.Q1_Mz_End;

    tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_Start=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q1_Mz_Start;
    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_Start= tmpCHANNEL_EVENT_PARAM.Q3_Mz_Start;

    tmpCHANNEL_EVENT_LDAC_PARAM.Q2_Mz_End=
            tmpCHANNEL_EVENT_LDAC_PARAM.Q1_Mz_End= tmpCHANNEL_EVENT_PARAM.Q1_Mz_End;
    tmpCHANNEL_EVENT_LDAC_PARAM.Q3_Mz_End= tmpCHANNEL_EVENT_PARAM.Q3_Mz_End;
}

bool sSingleAcquisition::getQ1ScanParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                        cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                        cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{//q1scan
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_PARAM;
    cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_LDAC_PARAM;
    if(!((Q13ScanParamEditor*)mQ1ScanParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                 tmpCHANNEL_EVENT_LDAC_PARAM, Polarity))
        return false;
    pEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration())
        calQ1Only(Polarity, pCalibrationMass, tmpCHANNEL_EVENT_PARAM, tmpCHANNEL_EVENT_LDAC_PARAM);

    calEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannel(tmpCHANNEL_EVENT_LDAC_PARAM);
    if(Polarity){
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
    }else{
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
    }
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ1ScanParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ1ScanParamEditor->getEndTimeMinute();//tmpCHANNEL_EVENT_PARAM.eventTime_ms()/1000/60* 10;

    return true;
}

bool sSingleAcquisition::getQ3ScanParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                        cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                        cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_PARAM;
    cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_LDAC_PARAM;
    if(!((Q13ScanParamEditor*)mQ3ScanParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                 tmpCHANNEL_EVENT_LDAC_PARAM, Polarity))
        return false;
    pEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration())
        calQ3Only(Polarity, pCalibrationMass, tmpCHANNEL_EVENT_PARAM, tmpCHANNEL_EVENT_LDAC_PARAM);

    calEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannel(tmpCHANNEL_EVENT_LDAC_PARAM);
    if(Polarity)
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
    else
        pEVENT_PARAM.Type=calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ3ScanParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ3ScanParamEditor->getEndTimeMinute();//tmpCHANNEL_EVENT_PARAM.eventTime_ms()/1000/60* 10;
    return true;
}

bool sSingleAcquisition::getQ1SimParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                       cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                       cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM;
    QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM_LDAC;
    if(!((Q13SIMParamEditor*)mQ1SIMParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                               tmpCHANNEL_EVENT_PARAM_LDAC, Polarity))
        return false;
    pEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration()){
        int sizeCh= tmpCHANNEL_EVENT_PARAM.size();
        for(int i= 0; i< sizeCh; ++i){
            double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ1();
            //double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ3();
            if(Polarity)
                pCalibrationMass->getCalibrationViewQ1POS()->
                        calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End);
            else
                pCalibrationMass->getCalibrationViewQ1NEG()->
                        calibrate(scanSpeedQ1, tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start, tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End);
            tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start;
            tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End=
                    tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End;

            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start;
            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_End=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_End=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End;
        }
    }
    calEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannels(tmpCHANNEL_EVENT_PARAM_LDAC);
    if(Polarity){
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS;//???
    }else{
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_NEG;
    }
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ1SIMParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ1SIMParamEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::getQ3SimParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                       cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                       cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM;
    QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM_LDAC;
    if(!((Q13SIMParamEditor*)mQ3SIMParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                               tmpCHANNEL_EVENT_PARAM_LDAC, Polarity))
        return false;
    pEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration()){
        int sizeCh= tmpCHANNEL_EVENT_PARAM.size();
        for(int i= 0; i< sizeCh; ++i){
            //double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ1();
            double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ3();
            if(Polarity)
                pCalibrationMass->getCalibrationViewQ3POS()->
                        calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End);
            else
                pCalibrationMass->getCalibrationViewQ3NEG()->
                        calibrate(scanSpeedQ3, tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start, tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End);

            tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start;
            tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End=
                    tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End;

            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start;
            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_End=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_End=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End;
        }
    }
    calEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannels(tmpCHANNEL_EVENT_PARAM_LDAC);
    if(Polarity){
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS;//???
    }else{
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_NEG;
    }
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ3SIMParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ3SIMParamEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::getMrmParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                     cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                     cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM;
    QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM> tmpCHANNEL_EVENT_PARAM_LDAC;
    if(!((MRMParameterEditor*)mMRMParameterEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                  tmpCHANNEL_EVENT_PARAM_LDAC, Polarity))
        return false;
    pEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration()){
        int sizeCh= tmpCHANNEL_EVENT_PARAM.size();
        for(int i= 0; i< sizeCh; ++i){
            double scanSpeedQ1= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ1();
            double scanSpeedQ3= tmpCHANNEL_EVENT_PARAM[i].getScanSpeedQ3();
            if(Polarity){
                pCalibrationMass->getCalibrationViewQ1POS()->calibrate(scanSpeedQ1,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End);
                pCalibrationMass->getCalibrationViewQ3POS()->calibrate(scanSpeedQ3,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End);
            }else{
                pCalibrationMass->getCalibrationViewQ1NEG()->calibrate(scanSpeedQ1,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End);
                pCalibrationMass->getCalibrationViewQ3NEG()->calibrate(scanSpeedQ3,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start,
                                                                       tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End);
            }
            tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start;
            tmpCHANNEL_EVENT_PARAM[i].Q2_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End;

            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_Start=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_Start;
            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q2_Mz_End=
                    tmpCHANNEL_EVENT_PARAM_LDAC[i].Q1_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q1_Mz_End;
            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_Start= tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_Start;
            tmpCHANNEL_EVENT_PARAM_LDAC[i].Q3_Mz_End= tmpCHANNEL_EVENT_PARAM[i].Q3_Mz_End;
        }
    }
    calEVENT_PARAM.addChannels(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannels(tmpCHANNEL_EVENT_PARAM_LDAC);
    if(Polarity){
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_POS;//???
    }else{
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_NEG;//???
    }
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mMRMParameterEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mMRMParameterEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::getProductParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                         cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                         cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_PARAM;
    cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_LDAC_PARAM;
    if(!((IonScanParamEditor*)mProIonScanParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                     tmpCHANNEL_EVENT_LDAC_PARAM, Polarity))
        return false;
    pEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration())
        calQ1Q3(Polarity, pCalibrationMass, tmpCHANNEL_EVENT_PARAM, tmpCHANNEL_EVENT_LDAC_PARAM);

    calEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannel(tmpCHANNEL_EVENT_LDAC_PARAM);
    if(Polarity)
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
    else
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ3ScanParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ3ScanParamEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::getPrecursorParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                           cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                           cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_PARAM;
    cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_LDAC_PARAM;
    if(!((IonScanParamEditor*)mPreIonScanParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                     tmpCHANNEL_EVENT_LDAC_PARAM, Polarity))
        return false;
    pEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration())
        calQ1Q3(Polarity, pCalibrationMass, tmpCHANNEL_EVENT_PARAM, tmpCHANNEL_EVENT_LDAC_PARAM);

    calEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannel(tmpCHANNEL_EVENT_LDAC_PARAM);
    if(Polarity)
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
    else
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ3ScanParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ3ScanParamEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::getNeutralLossParam(cTQ_StructCMD_HZH::_EVENT_PARAM& calEVENT_PARAM,
                                             cTQ_StructCMD_AD81416::_EVENT_PARAM& calEVENT_PARAM_LDAC,
                                             cTQ_StructCMD_HZH::_EVENT_PARAM& pEVENT_PARAM)
{
    sTune* pTune= (sTune*)mTune;
    int Polarity;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_PARAM;
    cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM tmpCHANNEL_EVENT_LDAC_PARAM;
    if(!((IonScanParamEditor*)mNeuLosScanParamEditor)->getParamEvent(tmpCHANNEL_EVENT_PARAM,
                                                                     tmpCHANNEL_EVENT_LDAC_PARAM, Polarity))
        return false;
    pEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    sCalibrationMass* pCalibrationMass= pTune->getCalibrationMass();
    if(!pCalibrationMass)
        return false;
    if(pCalibrationMass->usingCalibration())
        calQ1Q3(Polarity, pCalibrationMass, tmpCHANNEL_EVENT_PARAM, tmpCHANNEL_EVENT_LDAC_PARAM);

    calEVENT_PARAM.addChannel(tmpCHANNEL_EVENT_PARAM);
    calEVENT_PARAM_LDAC.addChannel(tmpCHANNEL_EVENT_LDAC_PARAM);
    if(Polarity)
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
    else
        pEVENT_PARAM.Type= calEVENT_PARAM_LDAC.Type=
                calEVENT_PARAM.Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
    pEVENT_PARAM.TimeStartMin= calEVENT_PARAM_LDAC.TimeStartMin=
            calEVENT_PARAM.TimeStartMin= mQ3ScanParamEditor->getStartTimeMinute();
    pEVENT_PARAM.TimeEndMin= calEVENT_PARAM_LDAC.TimeEndMin=
            calEVENT_PARAM.TimeEndMin= mQ3ScanParamEditor->getEndTimeMinute();
    return true;
}

bool sSingleAcquisition::onManualScan()
{
    sTune* pTune= (sTune*)mTune;
    //    ui.UI_W_MENU_SINGLEACQ->setDisabled(true);
    //    if(!mTune)
    //        return ui.UI_W_MENU_SINGLEACQ->setDisabled(false);

#ifdef DMSIT
    if(!pTune->updateDMSIT())
        return false;
#endif
    QByteArray tmpBufferW,
            tmpBufferR(512, 0);
    if(!pTune->setStop())
        return false;
    if(!pTune->updateMZ())
        return false;
    /***************************************************************************/
    m_EVENT_PARAM_SET.clearEvent();
    cTQ_StructCMD_HZH::_EVENT_PARAM_SET tmpEVENT_PARAM_SET;
    cTQ_StructCMD_AD81416::_EVENT_PARAM_SET tmpEVENT_PARAM_LDAC_SET;//m_EVENT_PARAM_LDAC_SET.clearEvent();
    cTQ_StructCMD_HZH::_EVENT_PARAM tmpEVENT_PARAM;
    cTQ_StructCMD_HZH::_EVENT_PARAM calEVENT_PARAM;
    cTQ_StructCMD_AD81416::_EVENT_PARAM calEVENT_PARAM_LDAC;

    switch(ui.UI_TABWIDGET_PARAM_SINGLEACQ->currentIndex()){
    case 0:{//q1scan
        if(!getQ1ScanParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 1:{//q3scan
        if(!getQ3ScanParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 2:{//q1sim
        if(!getQ1SimParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 3:{//q3sim
        if(!getQ3SimParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 4:{//mrm
        if(!getMrmParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 5:{//pre
        if(!getPrecursorParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 6:{//pro
        if(!getProductParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    case 7:{//neu
        if(!getNeutralLossParam(calEVENT_PARAM, calEVENT_PARAM_LDAC, tmpEVENT_PARAM))
            return false;
        break;}
    default:break;
    }
    //cTQ_StructCMD_HZH::linearInterpolation(calEVENT_PARAM, mList_MZ_PARAM_SET);
    tmpEVENT_PARAM_SET.addEvent(calEVENT_PARAM);
    m_EVENT_PARAM_SET.addEvent(tmpEVENT_PARAM);

    if((cTQ_FunctionCMD_HZH::cTQ_CMD_MS_Event_Param_Set(tmpBufferW, tmpEVENT_PARAM_SET)!=0)||tmpBufferW.isEmpty())
        return false;
    //printDebugBuff(tmpBufferW);
    if(mHCSCommUSB->sendCMD(tmpBufferW, tmpBufferR)!=0){
        qWarning()<<"E_sSingleAcquisition_cTQ_CMD_MS_Event_Param_Set_FAILUER";
        return false;
    }
    SINFO("MS_Event_Param_Set");

    tmpEVENT_PARAM_LDAC_SET.addEvent(calEVENT_PARAM_LDAC);
    if((cTQ_FunctionCMD_AD81416::cTQ_CMD_MS_Event_Param_Set(tmpBufferW, tmpEVENT_PARAM_LDAC_SET)!=0)||tmpBufferW.isEmpty())
        return false;
    if(mHCSCommUSB->sendCMD(tmpBufferW, tmpBufferR)!=0){
        qWarning()<<"E_sSingleAcquisition_cTQ_CMD_MS_Event_Param_Set_FAILUER";
        return false;
    }
    SINFO("MS_Event_Param_Set");

    m_pMsEvtSplitter->setMassEventVector(m_EVENT_PARAM_SET);
    mHCSCommUSB->getEvSegSplitterPtr()->setUMAEvents(m_EVENT_PARAM_SET);
    initTIC();

    QByteArray pSegment;
    if(!cAcqFunction::fillSegmentStruct(m_EVENT_PARAM_SET.listEvent, pSegment))
        return false;

    QString tmpStr;
    QString strNumber= "No.&3&SegNo.&EventNo.&ExpNo.";
    QByteArray StreamHead= cAcqFunction::createStreamHead(pSegment,
                                                          tmpStr,
                                                          tmpStr,//& pXIC,
                                                          tmpStr,
                                                          getTuningFile,
                                                          strNumber);
    QString strPath= QCoreApplication::applicationDirPath()+ "/data";
    if(StreamHead.isEmpty()|| !mAcqFunction.openFile(StreamHead, strPath))
        return false;

    getParamDAQ(m_STRUCT_ADC_TDC);
    ParamCCS::_DAQ_CONFIG tmpDAQ_CONFIG;//= mSystem->getConfigDAQ();
    cFunctionsTune::fillingParamDAQ(m_STRUCT_ADC_TDC, tmpDAQ_CONFIG);
    if(mHCSCommUSB->startDAQ(tmpDAQ_CONFIG, _StreamBody::Type_Double)!= 0)//saveData=false;
        return false;
    //mAnalyzeThread->start();

    acquisitionStart();

    if(!pTune->setScan())
        return false;
    return true;
}

void sSingleAcquisition::onManualStop()
{
    //ui.UI_W_MENU_SINGLEACQ->setDisabled(false);
    if(mTimerRefreshID!=-1)
        killTimer(mTimerRefreshID);
    mTimerRefreshID=-1;
    mAnalyzeThread->stop();
    mHCSCommUSB->stopDAQ();
    ((sTune*)mTune)->setStop();

    QString dateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strPath= QCoreApplication::applicationDirPath()+ "/data";//;
    QString filename= QFileDialog::getSaveFileName(this, tr("Save Data"),
                                                   strPath+ "/MASS_DATA_" + dateTime,
                                                   tr("*"));
    mAcqFunction.saveFile(filename, strPath);
}
