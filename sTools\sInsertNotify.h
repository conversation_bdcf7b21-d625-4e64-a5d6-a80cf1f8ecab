﻿#ifndef C_INSERTNOTIFY_H
#define C_INSERTNOTIFY_H

#include <QObject>
#include "cSafedata.h"

#define TMP_MSG(x) sInsertNotify::getInstance()->insertTemporary(x, 3)
#define ERR_MSG(x) sInsertNotify::getInstance()->insertNotify(x, sInsertNotify::Error)
#define WARN_MSG(x) sInsertNotify::getInstance()->insertNotify(x, sInsertNotify::Warning)

class sInsertNotify : public QObject
{
    Q_OBJECT
public:
    static sInsertNotify* getInstance(){
        static sInsertNotify notify;
        return &notify;
    }

    enum Degree{
        Info,
        Warning,
        Error
    };
    Q_ENUM(Degree)

    void insertNotify(const QString& msg, Degree d = Info);
    void insertTemporary(const QString& msg, int sec = 5);

    static void msgHandler(QtMsgType type, const QMessageLogContext &content, const QString &msg);
    static QStringList logs(int &nBeginID);

signals:
    void sig_insertNotify(const QString& msg, Degree d);
    void sig_insertTemporary(const QString& msg, int sec);

public slots:

private:
    explicit sInsertNotify(QObject *parent = nullptr);
    static cSafeData<QStringList> m_logs;
    static int m_logBeginID; ///< 日志起始ID， 会根据日志擦除情况增加
};

#include <QMetaType>
Q_DECLARE_METATYPE(sInsertNotify::Degree)

#endif // C_INSERTNOTIFY_H
