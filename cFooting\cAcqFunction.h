#pragma once

#include <uiMethod/cTQ_StructCMD_HZH.h>
#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif
#include <QTime>
#include <cDataFileWrite.h>
#include <cPublicStructHZH.h>
#include <UMA_HCS_Data.h>

typedef QPair<cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE, cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM>
TYPEDEF_EVENT;
typedef QMap<uint32_t, QMap<QString, _PARAM_XIC*>>
Event_Mass_PARAM_XIC;

class cAcqFunction
{
public:
    cAcqFunction();
    static bool dataSplit( /*QVector<double>& sampleData,*/
                          const UMA_HCS::HCSDataFrame::HCSData& chFrame,
                          _STRUCT_ADC_TDC pSTRUCT_ADC_TDC,//uint ACC,
                          uint    muRefVolDigit,
                           //const QList<TYPEDEF_EVENT>& msChsMaped,
                          MsChData& msChsData);
//    static bool dataSplitTune( /*QVector<double>& sampleData,*/
//                          const UMA_HCS::HCSDataFrame::HCSData& chFrame,
//                          uint ACC,
//                          uint    muRefVolDigit,
//                          MsChData& msChsData);

    static bool fillChartBuffScan(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                  const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                  const MsChData& pMsChData, //const DataSeg& pDataSeg,
                                  const quint8 q/*q=1||q=3*/,
                                  double& tic,
                                  std::vector<double>& pGraphBuffX,
                                  std::vector<double>& pGraphBuffY);
    static bool fillChartBuffSIM(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                 const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                 const MsChData& pMsChData,
                                 const quint8 q/*q=1||q=3*/,
                                 double& tic,
                                 std::vector<double>& pGraphBuffX,
                                 std::vector<double>& pGraphBuffY);
    static bool fillChartBuffMRM(const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt,
                                 const _STRUCT_ADC_TDC& p_STRUCT_ADC_TDC,
                                 const MsChData& pMsChData,
                                 //const quint8 q/*q=1||q=3*/,
                                 double& tic,
                                 std::vector<double>& pGraphBuffX,
                                 std::vector<double>& pGraphBuffY);

    cDataFileWrite mFileDAQ;
    qint64 mOffsetFile=0;
    QByteArray mStreamHead;
    static bool fillSegmentStruct(const QList<cTQ_StructCMD_HZH::_EVENT_PARAM>& p_EVENT_PARAM_list,
                           QByteArray& graphParam);
private:
    static bool splitAndAverage(const double* data,
                                const int size,
                                std::vector<double>& averages,
                                double ScanStep,
                                double MzStart, double MzEnd);
    static bool fillFileStructScan(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                   const quint8 q/*q=1||q=3*/,
                                          QByteArray& baEvtParam);
    static bool fillFileStructSIM(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                          const quint8 q/*q=1||q=3*/,
                                          QByteArray& baEvtParam);
    static bool fillFileStructMRM(const cTQ_StructCMD_HZH::_EVENT_PARAM& p_EVENT_PARAM,
                                          QByteArray& baEvtParam);
public:
    //mFileDAQ.createDataFile(streamHead, property("DeviceDataDirPath").toString());
    static QByteArray createStreamHead(QByteArray& pSegment,
                                       QString& fileHead,
                                       QString& pXIC,
                                       //QString& pSelectXIC,
                                       QString& pPropertyStr,
                                       QByteArray& tuningFile,
                                       QString& otherLines
                                       ){
        QByteArray streamHead;
        uint32_t sizeSegment= (uint32_t)(pSegment.size());
        if(sizeSegment< sizeof(cParamValue::_Segment))
            return streamHead;
        streamHead.resize(sizeof(_StreamHead));
        streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_Segment_Param, pSegment);
        if(!fileHead.isEmpty()){
            QByteArray tmpArray= fileHead.toUtf8();
            streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_Method_Param, tmpArray);
        }
        if(!pXIC.isEmpty()){
            QByteArray tmpArray= pXIC.toUtf8();
            streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_XIC_Param, tmpArray);
        }
        if(!tuningFile.isEmpty())
            streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_Tuning_Param, tuningFile);

//        QByteArray tmpArray= pSelectXIC.toUtf8();
//        streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_XIC_Select, tmpArray);

        if(!pPropertyStr.isEmpty()){
            QByteArray tmpArray= pPropertyStr.toUtf8();
            streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_Property_Str, tmpArray);
        }
        if(otherLines.size()>0){
            QByteArray tmpArray= otherLines.toUtf8();
            streamHead+= cParamValue::_StreamHeadParam::toStreamArray(cParamValue::Type_Line_Param, tmpArray);
        }

        _StreamHead* p_StreamHead =(_StreamHead*)(streamHead.data());
        p_StreamHead->dateTime= QDateTime::currentDateTime().toTime_t();
        p_StreamHead->length= streamHead.size();
        p_StreamHead->typeParam = _StreamHead::Type_ACQ_TQ;//20250615
        return streamHead;
    }

    static bool dataCutHead(QByteArray& pByteArray,
                            QByteArray& pMarkBody,
                            double& xTIC,
                            double& yTIC,
                            _StreamBody::Type_Data currentType){
        if(pByteArray.size() < (int)sizeof(_StreamBody))
            return false;
        _StreamBody* pStreamBody = (_StreamBody*)(pByteArray.data());

        if(pStreamBody->lengthParam>0){
            cParamValue::_StreamBodyParam* pStreamBodyParam= (cParamValue::_StreamBodyParam*)(pByteArray.data()+ sizeof(_StreamBody));
            if(pStreamBodyParam->type== cParamValue::Type_Child_Body_TIC){
                cParamValue::_StreamBodyTIC* pStreamBodyTIC= cParamValue::_StreamBodyTIC::p(pStreamBodyParam);
                xTIC= pStreamBodyTIC->currentTime;
                yTIC= pStreamBodyTIC->currentAcc;
            }
        }
        qint32 offset= sizeof(_StreamBody)+ pStreamBody->lengthParam;
        pMarkBody.resize(offset);
        memcpy(pMarkBody.data(), pByteArray.data(), offset);

        QByteArray tempArray;
        tempArray.resize(pByteArray.size()- offset);
        memcpy(tempArray.data(), pByteArray.data()+ offset, tempArray.size());

        qint32 uAllPoint= tempArray.size()/ _StreamBody::size(currentType);
        pByteArray.resize(uAllPoint* sizeof(double));
        _StreamBody::memcpy((double*)(pByteArray.data()), (double*)(tempArray.data()),
                            uAllPoint, currentType);
        return true;
    }
    static bool createStreamBody(QByteArray& streamBody,
                                 QByteArray& dataBuffer,//默认double
                                 int sizeData,
                                 std::vector<double>& srcBuffY
                                 ){
        if(srcBuffY.size()<sizeData)
            return false;
        streamBody.resize(sizeof(_StreamBody)/*+ cParamValue::_StreamBodyTIC::size()*/);
        QDateTime tempDateTime=QDateTime::currentDateTime();
        _StreamBody* p_StreamBody= reinterpret_cast<decltype(p_StreamBody)>(streamBody.data());
        p_StreamBody->typeParam = _StreamBody::Type_Float;
        p_StreamBody->length = streamBody.size()+ sizeData* _StreamBody::size(_StreamBody::Type_Float);
        p_StreamBody->dateTime= tempDateTime.toTime_t()*1000+ tempDateTime.time().msec();
        p_StreamBody->lengthParam= 0;/*cParamValue::_StreamBodyTIC::size();*/
        dataBuffer.resize(sizeData* sizeof (double));
        double* pDst= (double*)(dataBuffer.data()/*+ offset*/);
        for(int uIndex = 0; uIndex < sizeData; ++uIndex){
            pDst[uIndex] = srcBuffY[uIndex];//pDst[uIndex] = mGraphBuffY[uIndex];
        }
        return true;
    }
    bool openFile(const QByteArray& streamHead,//in<-TIC参数头
                  QString filePath= QString()){
        mStreamHead= streamHead;
        return mFileDAQ.createDataFile(streamHead, filePath);
    }
    void write2File(qint64& offsetFile,
                    const QByteArray& pSrcData,
                    double xTIC,
                    double yTIC,
                    //QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                    double NoSeg,
                    double NoEvt,
                    double NoExp,
                    QByteArray& tempMarkBody/*,
                                                          _StreamBody::Type_Data typeData*/);//in analyzeThread
    void saveFile(QString fileName, QString filePath/*= QString()*/){
        if(mFileDAQ.isOpened())
            mFileDAQ.closeFile();
        if(fileName.indexOf(":")==-1){
            mFileDAQ.renameFile(fileName);
        }else{
            cDataFileWrite::renameFile(fileName, filePath);//mFileDAQ.currentPath()
        }
    }
};

