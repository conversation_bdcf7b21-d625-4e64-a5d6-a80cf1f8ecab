#pragma once
#include <qglobal.h>
#include <vector>
#include <cstring>
#include <math.h>

using namespace std;

namespace MathPublic{
class cMathLCTR
{
public:
    cMathLCTR(void){}
    static double Prctile(vector<double> data, double prc);
    static double LinIntXToY(double dX1, double dX2, double dY1, double dY2, double dX);
    static double LinIntYToX(double dX1, double dX2, double dY1, double dY2, double dY);
    static void LinLeastSq(vector<double> x,vector<double> y, int n, double& gradient, double& intersect);
    static void Hist(vector<double> data, int iNumBins,vector<int>& iHist, vector<double>& dBins);
    static vector<int> Hist(vector<double> data, vector<double> bins);
    static vector<double> Unique(vector<double> data);
    static void Unique(vector<double> data, vector<double>& dOutput, vector<int>& iNumRept);
    static double STD(vector<double> Val);
    static double PopSTD(vector<double> Val);
    static double STD(vector<double> Val, int N);
    static double STD(vector<double> Val, double& M);
    static double STD(vector<double> Val, int N, double& M);
    static double RMS(vector<double> Val);
    static vector<double> APlusB(vector<double> A, vector<double> B);
    static vector<double> AMinusB(vector<double> A, vector<double> B);
    static vector<double> AMinusBAllPlus(vector<double> A, vector<double> B);
    static void FactorArray(vector<double> A, double F, bool bMulitply);
    static bool CopyArrays(vector<double>& From, vector<double>& To, int iStart, int iEnd);
    static bool CopyArrays(vector<int> From, vector<int>& To, int iStart, int iEnd);

    static double average(int n,double *x);
    static double spfh(int n,double *x);
    static double shpf(int n,double *x);
    static double dcj(int n,double *x,double *y);
    static double djc(int n,double *x,double *y);
    static double he(int n,double *x);
    static double xsa(int n,double *x,double *y);
    static double xsb(int n,double *x,double *y,double a);

    static int DivRem(int a, int b, int& result);
    static double Round(double val, int places);

    template <class T>
    static void swap(T *a, T *b);
    template <class T>
    static int partition(vector<T>& Arrays, int nChoose);
    template <class T>
    static void QuickSort(vector<T>& Arrays, int nChoose);

    static double arrayMax(const double* py, long length);
    static bool getGravityPoint(const double* px, const double* py, long length, double& gx, double& area);
};
}
