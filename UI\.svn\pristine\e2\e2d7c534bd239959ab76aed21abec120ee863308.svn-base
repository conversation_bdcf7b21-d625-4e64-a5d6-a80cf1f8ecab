/********************************************************************************
** Form generated from reading UI file 'uiBaseParamEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIBASEPARAMEDITOR_H
#define UI_UIBASEPARAMEDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiBaseParamEditor
{
public:
    QVBoxLayout *uiBaseParamEditorLayout;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_8;
    QLineEdit *UI_LE_gating_mv;
    QGroupBox *groupBox;
    QGridLayout *gridLayout_2;
    QLineEdit *lineEdit_Polarity_switch_time_rOnly;
    QLineEdit *lineEdit_Polarity_switch_time;
    QLabel *Polarity_switch_time;
    QPushButton *UI_PB_Polarity;
    QSpacerItem *horizontalSpacer;
    QGridLayout *gridLayout;
    QLabel *label_6;
    QLineEdit *lineEdit_eventTime;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_5;
    QLineEdit *lineEdit_pauseTime_rOnly;
    QLineEdit *lineEdit_pauseTime;
    QSpacerItem *horizontalSpacer_4;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_7;
    QLineEdit *lineEdit_waitTime_rOnly;
    QSpacerItem *horizontalSpacer_5;
    QHBoxLayout *horizontalLayout;
    QLabel *label_3;
    QLineEdit *lineEdit_startTime;
    QLabel *label;
    QLineEdit *lineEdit_endTime;
    QLabel *label_2;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;

    void setupUi(QWidget *uiBaseParamEditor)
    {
        if (uiBaseParamEditor->objectName().isEmpty())
            uiBaseParamEditor->setObjectName(QString::fromUtf8("uiBaseParamEditor"));
        uiBaseParamEditor->resize(273, 423);
        uiBaseParamEditorLayout = new QVBoxLayout(uiBaseParamEditor);
        uiBaseParamEditorLayout->setObjectName(QString::fromUtf8("uiBaseParamEditorLayout"));
        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label_8 = new QLabel(uiBaseParamEditor);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setLayoutDirection(Qt::RightToLeft);
        label_8->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        horizontalLayout_4->addWidget(label_8);

        UI_LE_gating_mv = new QLineEdit(uiBaseParamEditor);
        UI_LE_gating_mv->setObjectName(QString::fromUtf8("UI_LE_gating_mv"));

        horizontalLayout_4->addWidget(UI_LE_gating_mv);


        uiBaseParamEditorLayout->addLayout(horizontalLayout_4);

        groupBox = new QGroupBox(uiBaseParamEditor);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        gridLayout_2 = new QGridLayout(groupBox);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        lineEdit_Polarity_switch_time_rOnly = new QLineEdit(groupBox);
        lineEdit_Polarity_switch_time_rOnly->setObjectName(QString::fromUtf8("lineEdit_Polarity_switch_time_rOnly"));
        lineEdit_Polarity_switch_time_rOnly->setMaximumSize(QSize(65, 16777215));
        lineEdit_Polarity_switch_time_rOnly->setFrame(false);
        lineEdit_Polarity_switch_time_rOnly->setReadOnly(true);

        gridLayout_2->addWidget(lineEdit_Polarity_switch_time_rOnly, 1, 2, 1, 1);

        lineEdit_Polarity_switch_time = new QLineEdit(groupBox);
        lineEdit_Polarity_switch_time->setObjectName(QString::fromUtf8("lineEdit_Polarity_switch_time"));
        lineEdit_Polarity_switch_time->setMaximumSize(QSize(65, 16777215));

        gridLayout_2->addWidget(lineEdit_Polarity_switch_time, 1, 3, 1, 1);

        Polarity_switch_time = new QLabel(groupBox);
        Polarity_switch_time->setObjectName(QString::fromUtf8("Polarity_switch_time"));
        Polarity_switch_time->setLayoutDirection(Qt::LeftToRight);
        Polarity_switch_time->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout_2->addWidget(Polarity_switch_time, 1, 1, 1, 1);

        UI_PB_Polarity = new QPushButton(groupBox);
        UI_PB_Polarity->setObjectName(QString::fromUtf8("UI_PB_Polarity"));
        UI_PB_Polarity->setMaximumSize(QSize(24, 16777215));

        gridLayout_2->addWidget(UI_PB_Polarity, 1, 0, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_2->addItem(horizontalSpacer, 1, 4, 1, 1);


        uiBaseParamEditorLayout->addWidget(groupBox);

        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        label_6 = new QLabel(uiBaseParamEditor);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout->addWidget(label_6, 0, 0, 1, 1);

        lineEdit_eventTime = new QLineEdit(uiBaseParamEditor);
        lineEdit_eventTime->setObjectName(QString::fromUtf8("lineEdit_eventTime"));
        lineEdit_eventTime->setMaximumSize(QSize(65, 16777215));
        lineEdit_eventTime->setFrame(true);
        lineEdit_eventTime->setReadOnly(false);

        gridLayout->addWidget(lineEdit_eventTime, 0, 1, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_3, 0, 2, 1, 1);


        uiBaseParamEditorLayout->addLayout(gridLayout);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, -1, -1, -1);
        label_5 = new QLabel(uiBaseParamEditor);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        horizontalLayout_3->addWidget(label_5);

        lineEdit_pauseTime_rOnly = new QLineEdit(uiBaseParamEditor);
        lineEdit_pauseTime_rOnly->setObjectName(QString::fromUtf8("lineEdit_pauseTime_rOnly"));
        lineEdit_pauseTime_rOnly->setMaximumSize(QSize(65, 16777215));
        lineEdit_pauseTime_rOnly->setFrame(false);
        lineEdit_pauseTime_rOnly->setReadOnly(true);

        horizontalLayout_3->addWidget(lineEdit_pauseTime_rOnly);

        lineEdit_pauseTime = new QLineEdit(uiBaseParamEditor);
        lineEdit_pauseTime->setObjectName(QString::fromUtf8("lineEdit_pauseTime"));
        lineEdit_pauseTime->setMaximumSize(QSize(65, 16777215));

        horizontalLayout_3->addWidget(lineEdit_pauseTime);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_4);


        uiBaseParamEditorLayout->addLayout(horizontalLayout_3);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label_7 = new QLabel(uiBaseParamEditor);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        horizontalLayout_2->addWidget(label_7);

        lineEdit_waitTime_rOnly = new QLineEdit(uiBaseParamEditor);
        lineEdit_waitTime_rOnly->setObjectName(QString::fromUtf8("lineEdit_waitTime_rOnly"));
        lineEdit_waitTime_rOnly->setFrame(false);
        lineEdit_waitTime_rOnly->setReadOnly(true);

        horizontalLayout_2->addWidget(lineEdit_waitTime_rOnly);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_5);


        uiBaseParamEditorLayout->addLayout(horizontalLayout_2);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setSizeConstraint(QLayout::SetDefaultConstraint);
        horizontalLayout->setContentsMargins(0, -1, -1, -1);
        label_3 = new QLabel(uiBaseParamEditor);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout->addWidget(label_3);

        lineEdit_startTime = new QLineEdit(uiBaseParamEditor);
        lineEdit_startTime->setObjectName(QString::fromUtf8("lineEdit_startTime"));

        horizontalLayout->addWidget(lineEdit_startTime);

        label = new QLabel(uiBaseParamEditor);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout->addWidget(label);

        lineEdit_endTime = new QLineEdit(uiBaseParamEditor);
        lineEdit_endTime->setObjectName(QString::fromUtf8("lineEdit_endTime"));

        horizontalLayout->addWidget(lineEdit_endTime);

        label_2 = new QLabel(uiBaseParamEditor);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout->addWidget(label_2);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);

        horizontalLayout->setStretch(0, 1);
        horizontalLayout->setStretch(1, 1);
        horizontalLayout->setStretch(3, 1);
        horizontalLayout->setStretch(4, 1);
        horizontalLayout->setStretch(5, 2);

        uiBaseParamEditorLayout->addLayout(horizontalLayout);

        verticalSpacer = new QSpacerItem(20, 183, QSizePolicy::Minimum, QSizePolicy::Expanding);

        uiBaseParamEditorLayout->addItem(verticalSpacer);


        retranslateUi(uiBaseParamEditor);

        QMetaObject::connectSlotsByName(uiBaseParamEditor);
    } // setupUi

    void retranslateUi(QWidget *uiBaseParamEditor)
    {
        uiBaseParamEditor->setWindowTitle(QApplication::translate("uiBaseParamEditor", "Form", nullptr));
        label_8->setText(QApplication::translate("uiBaseParamEditor", "Gating Voltage(mV):", nullptr));
        UI_LE_gating_mv->setText(QApplication::translate("uiBaseParamEditor", "75", nullptr));
        groupBox->setTitle(QApplication::translate("uiBaseParamEditor", "Polarity", nullptr));
        lineEdit_Polarity_switch_time_rOnly->setText(QApplication::translate("uiBaseParamEditor", "1", nullptr));
        lineEdit_Polarity_switch_time->setText(QApplication::translate("uiBaseParamEditor", "1", nullptr));
        Polarity_switch_time->setText(QApplication::translate("uiBaseParamEditor", "Switch Time(ms)", nullptr));
        UI_PB_Polarity->setText(QApplication::translate("uiBaseParamEditor", "+", nullptr));
        label_6->setText(QApplication::translate("uiBaseParamEditor", "Event Time(ms):", nullptr));
        lineEdit_eventTime->setText(QApplication::translate("uiBaseParamEditor", "0", nullptr));
        label_5->setText(QApplication::translate("uiBaseParamEditor", "Pause Time(ms):", nullptr));
        lineEdit_pauseTime_rOnly->setText(QApplication::translate("uiBaseParamEditor", "1", nullptr));
        lineEdit_pauseTime->setText(QApplication::translate("uiBaseParamEditor", "1", nullptr));
        label_7->setText(QApplication::translate("uiBaseParamEditor", "Wait Time(ms):", nullptr));
        lineEdit_waitTime_rOnly->setText(QApplication::translate("uiBaseParamEditor", "1", nullptr));
        label_3->setText(QApplication::translate("uiBaseParamEditor", "Acq.Time", nullptr));
        lineEdit_startTime->setText(QApplication::translate("uiBaseParamEditor", "0", nullptr));
        label->setText(QApplication::translate("uiBaseParamEditor", "-", nullptr));
        lineEdit_endTime->setText(QApplication::translate("uiBaseParamEditor", "100", nullptr));
        label_2->setText(QApplication::translate("uiBaseParamEditor", "min", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiBaseParamEditor: public Ui_uiBaseParamEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIBASEPARAMEDITOR_H
