#include "uiBatch.h"

#include <QFileDialog>
#include <QTableView>
#include <xlsxdocument.h>
#include <xlsxworksheet.h>
#include <xlsxcellrange.h>
#include <QDir>

#include "uiBatch/uiXlsxSheetModel.h"


uiBatch::uiBatch(QScriptEngine* pScriptEngine, QWidget *parent) :
    qUiWidget(parent)
{
}

uiBatch::~uiBatch()
{
    //delete ui;
}

void uiBatch::initClass(QString& filePath)
{


    initUI(filePath);
}

bool uiBatch::initUI(QString& filePath)
{
    ui.setupUi(this);
    mPlateLayout= new uiPlateLayout();
    mPlateLayout->hide();

    createToolBar();
    ui.UI_L_DATE_BATCH->setText(QDate::currentDate().toString("yyyyMMdd"));
//    pMainWindow->ui.UI_MPB_LIST_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
//    pMainWindow->ui.UI_MPB_LIST_MAINW->setPicture(QPixmap(":/Button/sMainWindow/picture/Correct.png"),
//                                     QPixmap(":/Button/sMainWindow/picture/Correct.png"),
//                                     QPixmap(":/Button/sMainWindow/picture/Correct.png"),
//                                     QPixmap(":/Button/sMainWindow/picture/Correct.png"),
//                                     tr("在途队列"));
//    //mSystemButton->setFontSize(9);
//    pMainWindow->ui.UI_MPB_LIST_MAINW->setTextSize(0,0,50);
//    //connect(mSystemButton,SIGNAL(ButtonClicked()),this,SLOT(on_UI_PB_SNAPSHOT_clicked()));
    ui.UI_TABWIDGET_LIST_BATCH->setTabPosition(QTabWidget::South);
    ui.UI_TW_BATCH->setColumnCount(9);
    QStringList headerText;
    headerText<<QObject::tr("Sample Name")
          <<QObject::tr("MS Method")
         <<QObject::tr("LC Method")
        <<QObject::tr("Rack Type")
       <<QObject::tr("Rack Position")
      <<QObject::tr("Plate Type")
     <<QObject::tr("Plate Position")
    <<QObject::tr("Vial Position")
    <<QObject::tr("Injection Time");
    ui.UI_TW_BATCH->setHorizontalHeaderLabels(headerText);
    ui.UI_TW_BATCH->setAlternatingRowColors(true);




//    QString filePath= QCoreApplication::applicationDirPath()+ "/寄存器地址_ZX-220428.xlsx";
//    QXlsx::Document xlsx(filePath);
//    foreach (QString sheetName, xlsx.sheetNames()) {
//        QXlsx::Worksheet *sheet = dynamic_cast<QXlsx::Worksheet *>(xlsx.sheet(sheetName));
//        if (sheet) {
//            QTableView *view = new QTableView(ui.UI_TABWIDGET_LIST_BATCH);
//            view->setModel(new QXlsx::SheetModel(sheet, view));
//            foreach (QXlsx::CellRange range, sheet->mergedCells())
//                view->setSpan(range.firstRow() - 1, range.firstColumn() - 1, range.rowCount(),
//                              range.columnCount());
//            ui.UI_TABWIDGET_LIST_BATCH-> addTab(view, sheetName);
//        }
//    }
    return true;
}

void uiBatch::createToolBar()
{
    ui.UI_MB_NEW_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_NEW_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("New"));
    ui.UI_MB_NEW_BATCH->setTextSize(64,64,40);
    connect(ui.UI_MB_NEW_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_NEW_BATCH()));

    ui.UI_MB_OPEN_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_OPEN_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Open"));
    ui.UI_MB_OPEN_BATCH->setTextSize(64,64,40);
    connect(ui.UI_MB_OPEN_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_OPEN_BATCH()));

    ui.UI_MB_SAVE_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_SAVE_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Save"));
    ui.UI_MB_SAVE_BATCH->setTextSize(64,64,40);
    connect(ui.UI_MB_SAVE_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_SAVE_BATCH()));

    ui.UI_MB_SAVEAS_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_SAVEAS_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Save As"));
    ui.UI_MB_SAVEAS_BATCH->setTextSize(64,64,40);
    connect(ui.UI_MB_SAVEAS_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_SAVEAS_BATCH()));

    ui.UI_MB_PLATE_LAYOUT_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_PLATE_LAYOUT_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Plate Layout"));
    ui.UI_MB_PLATE_LAYOUT_BATCH->setTextSize(88,64,40);
    connect(ui.UI_MB_PLATE_LAYOUT_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_PLATE_LAYOUT_BATCH()));

    ui.UI_MB_SUBMIT_BATCH->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_SUBMIT_BATCH->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Submit"));
    ui.UI_MB_SUBMIT_BATCH->setTextSize(64,64,40);
    connect(ui.UI_MB_SUBMIT_BATCH,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_SUBMIT_BATCH()));
}

bool uiBatch::getBatch(QList<QStringList>& pList)
{
    int rowCount= ui.UI_TW_BATCH->rowCount();
    int columnCount= ui.UI_TW_BATCH->columnCount();
    for(int indexRow= 0; indexRow< rowCount; ++indexRow){
        QStringList tmpList;
        for(int indexColumn= 0; indexColumn< columnCount; ++indexColumn){
            tmpList<<ui.UI_TW_BATCH->item(indexRow, indexColumn)->text();
        }
        pList<< tmpList;
    }
    return true;
}

QList<QMap<QString, QString>>& uiBatch::getBatch()
{
    QStringList headerText;
    headerText<<QObject::tr("Sample Name")
          <<QObject::tr("MS Method")
         <<QObject::tr("LC Method")
        <<QObject::tr("Rack Type")
       <<QObject::tr("Rack Position")
      <<QObject::tr("Plate Type")
     <<QObject::tr("Plate Position")
    <<QObject::tr("Vial Position")
    <<QObject::tr("Injection Time");
    mParamBatch.clear();
    int rowCount= ui.UI_TW_BATCH->rowCount();
    int columnCount= ui.UI_TW_BATCH->columnCount();
    if(columnCount< headerText.size())
        return mParamBatch;
    columnCount= headerText.size();
    for(int indexRow= 0; indexRow< rowCount; ++indexRow){
        //QStringList tmpList;
        QMap<QString, QString> tmpMap;
        for(int indexColumn= 0; indexColumn< columnCount; ++indexColumn){
            //tmpList<<ui.UI_TW_BATCH->item(indexRow, indexColumn)->text();
            tmpMap[headerText[indexColumn]]= ui.UI_TW_BATCH->item(indexRow, indexColumn)->text();
        }
        mParamBatch<< tmpMap;
    }
    return mParamBatch;
}

void uiBatch::onUI_MB_NEW_BATCH()
{
    QString filePath = QCoreApplication::applicationDirPath()+ "/templateBatch.dll";
    ui.UI_TW_BATCH->clear();

    ui.UI_TABWIDGET_LIST_BATCH->setTabText(0, "");
    QXlsx::Document xlsx(filePath);
    QXlsx::SheetModel* pModel= nullptr;
    foreach (QString sheetName, xlsx.sheetNames()) {
        QXlsx::Worksheet *sheet = dynamic_cast<QXlsx::Worksheet *>(xlsx.sheet(sheetName));
        if (sheet) {
            pModel= new QXlsx::SheetModel(sheet/*, view*/);
            int rowCounts= pModel->rowCount();
            if(rowCounts< 100)
                ui.UI_TW_BATCH->setRowCount(100);
            else
                ui.UI_TW_BATCH->setRowCount(pModel->rowCount());
            ui.UI_TW_BATCH->setColumnCount(pModel->columnCount());
            QStringList headerText;
            headerText<<QObject::tr("Sample Name")
                     <<QObject::tr("MS Method")
                    <<QObject::tr("LC Method")
                   <<QObject::tr("Rack Type")
                  <<QObject::tr("Rack Position")
                 <<QObject::tr("Plate Type")
                <<QObject::tr("Plate Position")
               <<QObject::tr("Vial Position")
              <<QObject::tr("Injection Time");
            ui.UI_TW_BATCH->setHorizontalHeaderLabels(headerText);
            for(int indexRow= 0; indexRow< pModel->rowCount(); ++indexRow){
                for(int indexColumn= 0; indexColumn< pModel->columnCount(); ++indexColumn){
                    ui.UI_TW_BATCH->setItem(indexRow, indexColumn,
                                            new QTableWidgetItem(pModel->data(pModel->index(indexRow,indexColumn)).toString()));
                }
            }
            int indexRow= pModel->rowCount();
            if(indexRow<100)
            for(; indexRow< 100; ++indexRow){
                for(int indexColumn= 0; indexColumn< pModel->columnCount(); ++indexColumn){
                    ui.UI_TW_BATCH->setItem(indexRow, indexColumn,
                                            new QTableWidgetItem(""));
                }
            }
            return;
        }
    }
}

void uiBatch::onUI_MB_OPEN_BATCH()
{
    QString filePath = QFileDialog::getOpenFileName(0, "Open xlsx file", QString(), "*.xlsx");
    if (filePath.isEmpty())
        return;
    ui.UI_TW_BATCH->clear();
    ui.UI_TABWIDGET_LIST_BATCH->setTabText(0, filePath);
    QXlsx::Document xlsx(filePath);
    QXlsx::SheetModel* pModel= nullptr;
    foreach (QString sheetName, xlsx.sheetNames()) {
        QXlsx::Worksheet *sheet = dynamic_cast<QXlsx::Worksheet *>(xlsx.sheet(sheetName));
        if (sheet) {
            pModel= new QXlsx::SheetModel(sheet/*, view*/);
            ui.UI_TW_BATCH->setRowCount(pModel->rowCount());
            ui.UI_TW_BATCH->setColumnCount(pModel->columnCount());
            for(int indexRow= 0; indexRow< pModel->rowCount(); ++indexRow){
                for(int indexColumn= 0; indexColumn< pModel->columnCount(); ++indexColumn){
                    ui.UI_TW_BATCH->setItem(indexRow, indexColumn,
                                            new QTableWidgetItem(pModel->data(pModel->index(indexRow,indexColumn)).toString()));
                }
            }
            return;
        }
    }
}

void uiBatch::onUI_MB_SAVE_BATCH()
{
    QString filePath= ui.UI_TABWIDGET_LIST_BATCH->tabText(0);
    if(filePath.isEmpty()){
        QString tmpFile = QFileDialog::getSaveFileName(0, "Open xlsx file", QString(), "*.xlsx");
        if (tmpFile.isEmpty())
            return;
        filePath= tmpFile;
        ui.UI_TABWIDGET_LIST_BATCH->setTabText(0, filePath);
    }

    filePath= QDir::fromNativeSeparators(filePath);
    QXlsx::Document xlsx;
    int rowCount= ui.UI_TW_BATCH->rowCount();
    int columnCount= ui.UI_TW_BATCH->columnCount();
    for(int indexRow= 0; indexRow< rowCount; ++indexRow){
        for(int indexColumn= 0; indexColumn< columnCount; ++indexColumn){
            xlsx.write(indexRow+1, indexColumn+1, ui.UI_TW_BATCH->item(indexRow, indexColumn)->text());
        }
    }
        xlsx.saveAs(filePath);
}

void uiBatch::onUI_MB_SAVEAS_BATCH()
{
    QString filePath = QFileDialog::getOpenFileName(0, "Open xlsx file", QString(), "*.xlsx");
    if (filePath.isEmpty())
        return;
    ui.UI_TABWIDGET_LIST_BATCH->setTabText(0, filePath);
    //QString filePath= ui.UI_TABWIDGET_LIST_BATCH->tabText(0);
    filePath= QDir::fromNativeSeparators(filePath);
    QXlsx::Document xlsx;
    int rowCount= ui.UI_TW_BATCH->rowCount();
    int columnCount= ui.UI_TW_BATCH->columnCount();
    for(int indexRow= 0; indexRow< rowCount; ++indexRow){
        for(int indexColumn= 0; indexColumn< columnCount; ++indexColumn){
            xlsx.write(indexRow+1, indexColumn+1, ui.UI_TW_BATCH->item(indexRow, indexColumn)->text());
        }
    }
        xlsx.saveAs(filePath);
}

void uiBatch::onUI_MB_PLATE_LAYOUT_BATCH()
{
    if(!mPlateLayout)
        return;
    mPlateLayout->show();
}

void uiBatch::onUI_MB_SUBMIT_BATCH()
{
    emit sSubmit();
}
