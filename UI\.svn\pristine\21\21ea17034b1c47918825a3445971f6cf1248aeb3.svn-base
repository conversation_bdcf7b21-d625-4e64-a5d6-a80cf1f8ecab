#ifndef COMMSERIAL_H
#define COMMSERIAL_H
#include <QSerialPort>
#include <qdebug.h>
#include <QMutex>
#include <QElapsedTimer>
#include <QCoreApplication>
//#include <QWidget>

//#define addLog(msg) qDebug()<<(msg)
//#define SUART_CMD_WRITE_BAUDRATE    1

class cCommSerial : public QSerialPort
{
    Q_OBJECT
public:
    struct _STRUCT_SERIAL{
        quint16 PID_SERIAL= 29987;
        quint16 VID_SERIAL= 6790;
        qint32 DelayTimeMs_SERIAL= 1000;
        qint32 BaudRate_SERIAL= QSerialPort::Baud115200;
        QSerialPort::Parity Parity_SERIAL= QSerialPort::NoParity;
        QSerialPort::DataBits DataBits_SERIAL= QSerialPort::Data8;
        QSerialPort::StopBits StopBits_SERIAL= QSerialPort::OneStop;
        QSerialPort::OpenModeFlag OpenModeFlag_SERIAL= QIODevice::ReadWrite;
        _STRUCT_SERIAL(quint16 PID= 29987, quint16 VID= 6790,
                       qint32 DelayTimeMs= 1000,
                       qint32 baudRate= QSerialPort::Baud115200,
                       QSerialPort::Parity parity= QSerialPort::NoParity,
                       QSerialPort::DataBits dataBits= QSerialPort::Data8,
                       QSerialPort::StopBits stopBits= QSerialPort::OneStop,
                       QSerialPort::OpenModeFlag openModeFlag= QIODevice::ReadWrite):
            PID_SERIAL(PID),
            VID_SERIAL(VID),
            DelayTimeMs_SERIAL(DelayTimeMs),
            BaudRate_SERIAL(baudRate),
            Parity_SERIAL(parity),
            DataBits_SERIAL(dataBits),
            StopBits_SERIAL(stopBits),
            OpenModeFlag_SERIAL(openModeFlag){
        }
        _STRUCT_SERIAL & operator=(const _STRUCT_SERIAL &obj){
            if(this !=&obj){
                PID_SERIAL= obj.PID_SERIAL;
                VID_SERIAL= obj.VID_SERIAL;
                DelayTimeMs_SERIAL= obj.DelayTimeMs_SERIAL;
                BaudRate_SERIAL= obj.BaudRate_SERIAL;
                Parity_SERIAL= obj.Parity_SERIAL;
                DataBits_SERIAL= obj.DataBits_SERIAL;
                StopBits_SERIAL= obj.StopBits_SERIAL;
                OpenModeFlag_SERIAL= obj.OpenModeFlag_SERIAL;
            }
            return *this;
        }
    };
    //typedef bool (SerialHandle)(QByteArray& byteArray, void* pThis);
    //static void msleep(unsigned long);
    explicit cCommSerial(QObject *parent = 0);
    ~cCommSerial(){
        if(isOpen())
            close();
    }
    bool openDevice(quint16 PID= 29987, quint16 VID= 6790, qint32 baudRate= QSerialPort::Baud115200, qint32 delayMS=3000);
    bool openWithCMD(QByteArray& cmd, const quint16 nRead, quint16 PID= 29987, quint16 VID= 6790,
                     qint32 baudRate= QSerialPort::Baud115200, Parity typeParity=QSerialPort::NoParity, qint32 delayMS=300);
    bool openWithCMD_Q(QByteArray& cmd, const quint16 req, quint16 PID= 29987, quint16 VID= 6790,
                     qint32 baudRate= QSerialPort::Baud115200, qint32 delayMS=3000);
    //QByteArray writeForBack(QByteArray& pWriteBuff, int lengthRead=0, qint32 delayMS=100);
    QByteArray writeForBack(QByteArray& pWriteBuff, quint16 nRead, qint32 delayMS=100);
    QByteArray openWrite(QByteArray& cmd, quint16 nRead, quint16 PID= 29987, quint16 VID= 6790,
                     qint32 baudRate= QSerialPort::Baud115200, qint32 delayMS=3000);


    QString openComm(const _STRUCT_SERIAL& pSTRUCT_SERIAL, /*const */QStringList& excludedList,
                  const QByteArray& writeArray, QByteArray& readArray, quint16 nRead, qint32 delayMS);
    QByteArray writeComm(const QByteArray& writeArray, quint16 nRead, qint32 delayMS);
//    qint64 write(QSerialPort& pSerialPort,
//            const QByteArray& lpBuffer,
//            qint32 nTimeOutMs= 500);
    qint64 write(const QByteArray& lpBuffer,
                 qint32 nTimeOutMs= 500);
//    QByteArray read(QSerialPort& pSerialPort,
//            qint64 lengthR,
//            qint32 nTimeOutMs= 500);
    QByteArray read(qint64 lengthR,
                    qint32 nTimeOutMs= 500);
//    QByteArray  readSpecific(QSerialPort& pSerialPort,
//            qint64 lengthR,
//            qint32 nTimeOutMs= 500);
    bool ioControl(
            int dwIoControlCode,
            void* lpInBuffer = NULL,
            unsigned int nInBufferSize = 0,
            void* lpOutBuffer = NULL,
            unsigned int nOutBufferSize = 0,
            unsigned int* lpBytesReturned = NULL,
            unsigned int nTimeOutMs = ~0
            );
    static void sleepMS(int time){
        QElapsedTimer t;
        t.start();
        while(t.elapsed()<time){
            QCoreApplication::processEvents();
            //qDebug()<<t.elapsed()<<","<<time;
        }
        //qDebug()<<10000;
    }
    bool trylock(int timeout= 0){
        return mMutex.tryLock(timeout);
    }

    void lock(){
        return mMutex.lock();
    }
    void unlock(){
        return mMutex.unlock();
    }


private:
    QMutex mMutex;
    quint16 mPID= 29987,
        mVID= 6790;
    qint32 mBaudRate= QSerialPort::Baud115200,
        mDelayMS=3000;
    Parity mTypeParity=QSerialPort::NoParity;
    QByteArray mOpenCMD;
    quint16 mOpenREQ=0;

    QString findDevice(quint16 PID= 29987, quint16 VID= 6790);
    QList<QString> findDevices(quint16 PID= 29987, quint16 VID= 6790);
    //bool open(QSerialPort& pSerialPort, QString portName, qint32 baudRate= QSerialPort::Baud9600, Parity typeParity=QSerialPort::NoParity);
    bool open(QString portName, qint32 baudRate= QSerialPort::Baud9600, Parity typeParity=QSerialPort::NoParity);
    //bool open(QSerialPort& pSerialPort, QString portName, _STRUCT_SERIAL& pSTRUCT_SERIAL);
    bool open(const QString& portName, const _STRUCT_SERIAL& pSTRUCT_SERIAL);

public slots:
    void onReadyRead();
    void handleBytesWritten(qint64 nBytes);
    void handleError(QSerialPort::SerialPortError err);
};

#endif // COMMSERIAL_H
