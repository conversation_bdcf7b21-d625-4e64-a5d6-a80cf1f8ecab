#include "sCalibrationMassItem.h"
#include <QMenu>
#include "cMathHZH.h"

sCalibrationMassItem::sCalibrationMassItem(QWidget *parent) :
    uiCalibrationMassItem(parent)
{

}

sCalibrationMassItem::~sCalibrationMassItem()
{

}

bool sCalibrationMassItem::calcFun(
             const _CONGIG_OMS::_PARAM_FIT& tmpPARAM_FIT,
             std::vector<double>& predictedValues,
             std::vector<double>& correction,
             QString& Formula, double& R2)
{
    int nPloy= tmpPARAM_FIT.ployD;
    int sizeData= tmpPARAM_FIT.predictedValues.size();
    double tempStr1, tempStr2;
    std::vector<double> actualValues;
    bool ok=false;
    for(int i=0; i<sizeData; i++){
        tempStr1= tmpPARAM_FIT.predictedValues[i].toDouble(&ok);
        if(!ok)
            continue;
        tempStr2= tmpPARAM_FIT.actualValues[i].toDouble(&ok);
        if(!ok)
            continue;
        predictedValues.push_back(tempStr1);
        actualValues.push_back(tempStr2);
    }
    sizeData= actualValues.size();
    if(sizeData< 1)
        return false;
    if((sizeData> nPloy)&& (sizeData== predictedValues.size())){

        double out_chisq=0;
        if(cMatrix::polyfit(actualValues.data(), predictedValues.data(), sizeData, nPloy, mCoefF, out_chisq)!= 0)
            return false;
        correction.resize(sizeData);
        Formula= _CONGIG_OMS::_PARAM_FIT::getFormula(mCoefF);
        _CONGIG_OMS::_PARAM_FIT::calibrarion(actualValues.data(), correction.data(), sizeData, mCoefF);
        if(Formula.isEmpty())
            return false;
        R2 = cMathHZH::calculateR2(correction, predictedValues);
        return true;
    }
    return false;
}

