#include "sSingleAcquisition.h"
#include <QSettings>

void sSingleAcquisition::timerEvent(QTimerEvent *evt)
{
    if(evt->timerId() == mTimerRefreshID){
        if(mGraphBuffMutex.tryLock()){
            if(mIsNewMass){
                int CountEvt= m_EVENT_PARAM_SET.listEvent.size();
                for(int i= 0; i< CountEvt; i++){
                    if((m_EVENT_PARAM_SET.listEvent[i].Type== cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS)||
                            (m_EVENT_PARAM_SET.listEvent[i].Type== cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG)||
                            (m_EVENT_PARAM_SET.listEvent[i].Type> cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG)){
                        mMassChart[i]->setStyleM(TVPlot::Sticks);
                        mMassChart[i]->setSamples((*mGraphBuffX)[i], (*mGraphBuffY)[i]/*(*mAbsc)[i],(*mOrd)[i], mMinLine*/);
                    }else {
                        mMassChart[i]->setStyleM(TVPlot::Lines);
                        mMassChart[i]->setSamples((*mGraphBuffX)[i], (*mGraphBuffY)[i]);
                    }
                }
            }
            mIsNewMass= false;
            mGraphBuffMutex.unlock();
        }
        if(mSTRUCT_TIC.tryLock()){
            if(mIsNewTIC){
                quint32 sizeTIC= mSTRUCT_TIC.size();
                if(mChartTIC)
                    mChartTIC->setSamples(mSTRUCT_TIC.xTIC.data(), mSTRUCT_TIC.yTIC.data(), sizeTIC/*,false*/);
                //mChartTIC->drawXIC(mSTRUCT_TIC.xTIC, sizeTIC);
            }
            mIsNewTIC= false;
            mSTRUCT_TIC.unlock();
        }
    }
}

bool sSingleAcquisition::loadConfigFile(_CONGIG_OMS::_CONGIG_ACQUISITION_LIT* pCONGIG_ACQUISITION,
                                        bool ifUpdateUI,
                                        QString qPath)
{
    Q_UNUSED(ifUpdateUI)
    QString path;
    if(qPath.isEmpty()){
        path = QCoreApplication::applicationDirPath()+"/system.ini";
    }else
        path = qPath;
    QSettings configIniRead(path, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");

    if(!pCONGIG_ACQUISITION)
        return false;
    pCONGIG_ACQUISITION->Period= configIniRead.value("/Acquisition/Period",1000).toDouble();
    pCONGIG_ACQUISITION->maxiHeighMassChart= configIniRead.value("/Acquisition/maxiHeighMassChart",16777215).toInt();
    pCONGIG_ACQUISITION->modelChart = static_cast<_CONGIG_OMS::STYLE_CHART>(configIniRead.value("/Acquisition/ModelChart","1").toString().toUInt());
    pCONGIG_ACQUISITION->fileSaveParam = configIniRead.value("/Acquisition/FileSaveCnt","1").toString().toUInt();
    pCONGIG_ACQUISITION->xLeft = configIniRead.value("/Acquisition/xLeft",  0).toDouble();
    pCONGIG_ACQUISITION->xRight= configIniRead.value("/Acquisition/xRight", 0).toDouble();
    pCONGIG_ACQUISITION->backGround= configIniRead.value("/Acquisition/backGround", 0).toDouble();
    pCONGIG_ACQUISITION->a= configIniRead.value("/Acquisition/a", 0).toDouble();
    pCONGIG_ACQUISITION->b= configIniRead.value("/Acquisition/b", 0).toDouble();
    pCONGIG_ACQUISITION->c= configIniRead.value("/Acquisition/c", 0).toDouble();

    if(!mCALIBRATE.splitCalibrat(configIniRead.value("/FIT/PolyFit","").toString()))
        return false;
    return true;
}

bool sSingleAcquisition::loadTuningFile(bool ifUpdateUI)
{
    Q_UNUSED(ifUpdateUI)
    QString path= _FUNTION_OMS::getFilePath();//    QString path= pModeSelect->getFilePath();
    if(path.isEmpty())
        return false;

    QFile file(path);
    if(file.open(QIODevice::ReadOnly | QIODevice::Text)){
        getTuningFile=file.readAll();
        file.close();
    }else
        getTuningFile.clear();

    QSettings configIniRead(path, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");

    //    mParamXIC= configIniRead.value("/XIC/str").toString();
    //    mChartTIC->updateXIC(mParamXIC);

    if(!mCALIBRATE.splitCalibrat(configIniRead.value("/FIT/PolyFit","").toString()))
        return false;
    return true;
}

quint32 sSingleAcquisition::dataDismantleFirst(QByteArray& pByteArray,
                                               cParamValue::_Segment* pSegment,
                                               QList<std::vector<double>>* pListX,
                                               QList<std::vector<double>>* pListY,
                                               ListSTRUCT_DATA& pSTRUCT_DATA,
                                               bool restart)
{
    if(pByteArray.isEmpty()||(pSegment==nullptr)||(pListY==nullptr))
        return 0;
    quint32 uAllPoint = pByteArray.size() / sizeof(double);
    double dbEvtTimeSum= cParamValue::_Segment::getSegTimeMs(pSegment);
    if(dbEvtTimeSum< 0.0000001)
        return 0;
    if((quint32)(pListY->size())!= pSegment->countsEvent){
        pListY->clear();
        pListX->clear();
        for(quint32 i=0;i<pSegment->countsEvent;i++){
            pListY->append(std::vector<double>(0));
            pListX->append(std::vector<double>(0));
        }
    }
    if(restart){
        pSTRUCT_DATA.clear();
        mPointTimeSIM.clear();
        for(quint32 currentEvt=0;currentEvt<pSegment->countsEvent;currentEvt++){
            pSTRUCT_DATA.append(QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>(cParamValue::Type_Event_Null, _CONGIG_OMS::_STRUCT_DATA()));
            mPointTimeSIM.append(std::vector<quint32>());
        }
    }

    int offsetP= 0;
    double* pdbOffset = (double*)(pByteArray.data());
    for(quint32 currentEvt=0; currentEvt< pSegment->countsEvent; ++currentEvt){
        cParamValue::_Event* pEvent= (cParamValue::_Event*)((char*)&(pSegment->fisrtEvent)+ offsetP);
        if(pEvent-> holdTimeMs< 0.0000001)
            continue;
        if(cParamValue::Type_SIM_2048== pEvent->type){
            pSTRUCT_DATA[currentEvt].first= pEvent->type;
            offsetP+= sizeof(cParamValue::_EventSIM2048);
            cParamValue::_EventSIM2048* pEventSIM2048= (cParamValue::_EventSIM2048*)pEvent;
            _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt].second);
            if(restart){
                tempSTRUCT_DATA->uEvtValidPoint = (quint32)(pEventSIM2048->holdTimeMs * uAllPoint / dbEvtTimeSum);
                if((!vectorOperate::Resize((*pListY)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint))
                        ||(!vectorOperate::Resize((*pListX)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint))){

                    return 0;
                }
                //                _FUNTION_OMS::calibrationF(mCALIBRATE, pdbOffset, pEventSIM2048,
                //                                           (*pListX)[currentEvt], (*pListY)[currentEvt], mPointTimeSIM[currentEvt], tempSTRUCT_DATA);
                //
            }
            const double* pFirst= pdbOffset+ tempSTRUCT_DATA->uDelayPoint;
            for(int indexM= 0; indexM< mPointTimeSIM[currentEvt].size(); ++indexM){
                pFirst+= tempSTRUCT_DATA->uPrePoint;
                double tempI= *pFirst;
                for(int i=0; i< mPointTimeSIM[currentEvt][indexM]; ++i){
                    tempI= tempI*i/(i+1)+ *pFirst/(i+1);
                    ++pFirst;
                }
                (*pListY)[currentEvt][indexM]= tempI;
            }
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }else{//Type_LIT, Type_Profile, Type_Scan
            pSTRUCT_DATA[currentEvt].first= pEvent->type;
            offsetP+= sizeof(cParamValue::_EventLIT);
            cParamValue::_EventLIT* pEventLIT= (cParamValue::_EventLIT*)pEvent;
            _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt].second);//_CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt]);
            if(restart){
                tempSTRUCT_DATA->uEvtValidPoint = (quint32)(pEventLIT->holdTimeMs * uAllPoint / dbEvtTimeSum);
                if((!vectorOperate::Resize((*pListY)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint))
                        ||(!vectorOperate::Resize((*pListX)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint))){

                    return 0;
                }
                _FUNTION_OMS::calibrationF(mCALIBRATE, (*pListX)[currentEvt], pEventLIT, tempSTRUCT_DATA);//_CONGIG_OMS::calibrationF(mCALIBRATE, pFirst, pLast, pEventLIT, tempSTRUCT_DATA);
                //tempSTRUCT_DATA->uEventPoint= tempSTRUCT_DATA->uEvtValidPoint- tempSTRUCT_DATA->uPrePoint- tempSTRUCT_DATA->uPostPoint;
            }
            memcpy((*pListY)[currentEvt].data(), pdbOffset, tempSTRUCT_DATA->uEvtValidPoint* sizeof(double));
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }
    }
    return uAllPoint;
}

bool sSingleAcquisition::dataDismantleSecond(const QByteArray& pByteArray,QList<std::vector<double>>* pListY,
                                             const ListSTRUCT_DATA& pSTRUCT_DATA)
{
    if(pByteArray.isEmpty()||(pListY==nullptr)|| pSTRUCT_DATA.isEmpty())
        return false;

    double* pdbOffset = (double*)(pByteArray.data());
    for(int currentEvt=0; currentEvt< pSTRUCT_DATA.size(); ++currentEvt){
        if(cParamValue::Type_SIM_2048== pSTRUCT_DATA[currentEvt].first){
            const _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt].second);
            const double* pFirst= pdbOffset+ tempSTRUCT_DATA->uDelayPoint;
            for(int indexM= 0; indexM< mPointTimeSIM[currentEvt].size(); ++indexM){
                pFirst+= tempSTRUCT_DATA->uPrePoint;
                double tempI= *pFirst;
                for(int i=0; i< mPointTimeSIM[currentEvt][indexM]; ++i){
                    tempI= tempI*i/(i+1)+ *pFirst/(i+1);
                    ++pFirst;
                }
                (*pListY)[currentEvt][indexM]= tempI;
            }
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }else{//Type_LIT, Type_Profile, Type_Scan
            const _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt].second);//_CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt]);
            memcpy((*pListY)[currentEvt].data(), pdbOffset, tempSTRUCT_DATA->uEvtValidPoint* sizeof(double));
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }
    }
    return true;
}

