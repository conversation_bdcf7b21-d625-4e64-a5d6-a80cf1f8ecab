﻿#include "IonScanParamEditor.h"
#include "ui_IonScanParamEditor.h"
#include "sMethod/xml_attr_tag.h"
#include <sMethod/cTQ_FunctionCMD_HZH.h>
#include <uiMethod/cTQ_StructCMD_AD81416.h>

/**
 * @brief
 * 该类支持ProductIonScan、PrecursorIonScan、NeutralLosScan 三种Event类型的参数显示与编辑
 * @param element
 * @param parent
 */
IonScanParamEditor::IonScanParamEditor(const QString& type,
                                       sMapSetMZ* pMapSetMZ,
                                       QWidget* parent)
    : uiIonScanParamEditor(type, parent),
      mMapSetMZ(pMapSetMZ)
{

}

void IonScanParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool IonScanParamEditor::calcUseMapMZ(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE& Type,
                                      cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& tmpPARAM)
{
    if(!mMapSetMZ)
        return false;
    cTQ_FunctionCMD_HZH::linearInterpolation(Type, tmpPARAM, mMapSetMZ->getMzVoltagesHDAC());
    return true;
}

bool IonScanParamEditor::getParamEvent(cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM,
                                       cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM_LDAC,
                                       int& Polarity){
    bool ok=false;
    if (m_strEventType == "Product Ion Scan"){
        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_Start= getIonMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_Start= getStartMZ(&ok);
        IF_RETURN_FALSE(ok);

        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_End= getIonMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_End= getEndMZ(&ok);
        IF_RETURN_FALSE(ok);
    }else if (m_strEventType == "Precursor Ion Scan"){
        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_Start= getStartMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_Start= getIonMZ(&ok);
        IF_RETURN_FALSE(ok);

        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_End=getEndMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_End= getIonMZ(&ok);
        IF_RETURN_FALSE(ok);
    }else if (m_strEventType == "Neutral Los Scan"){
        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_Start= getStartMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_Start=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_Start= p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_Start- getIonMZ(&ok);
        IF_RETURN_FALSE(ok);

        p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM_LDAC.Q2_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q1_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q2_Mz_End=getEndMZ(&ok);
        IF_RETURN_FALSE(ok);
        p_CHANNEL_EVENT_PARAM_LDAC.Q3_Mz_End=
                p_CHANNEL_EVENT_PARAM.Q3_Mz_End= p_CHANNEL_EVENT_PARAM_LDAC.Q1_Mz_End- getIonMZ(&ok);
        IF_RETURN_FALSE(ok);
    }else
        return false;

    p_CHANNEL_EVENT_PARAM_LDAC.TDC_DAC=
            p_CHANNEL_EVENT_PARAM.TDC_DAC= 65535* (getLE_gating_mv(&ok)+ 2500)/ 5000;
    IF_RETURN_FALSE(ok);

    p_CHANNEL_EVENT_PARAM_LDAC.PN_SwitchTimeMs=
            p_CHANNEL_EVENT_PARAM.PN_SwitchTimeMs= getPolarity_switch_time(&ok);
    IF_RETURN_FALSE(ok);

    p_CHANNEL_EVENT_PARAM_LDAC.PauseTimeMs=
            p_CHANNEL_EVENT_PARAM.PauseTimeMs= getPauseTime(&ok);
    IF_RETURN_FALSE(ok);

    p_CHANNEL_EVENT_PARAM_LDAC.WaitTimeMs=
            p_CHANNEL_EVENT_PARAM.WaitTimeMs= getWaitTime(&ok);
    IF_RETURN_FALSE(ok);
    p_CHANNEL_EVENT_PARAM_LDAC.ScanTimeMs=
            p_CHANNEL_EVENT_PARAM.ScanTimeMs= p_CHANNEL_EVENT_PARAM.getScanTimeMs(
                getEventTime(&ok));
    IF_RETURN_FALSE(ok);
    Polarity= getPolarity();
    return true;
}
