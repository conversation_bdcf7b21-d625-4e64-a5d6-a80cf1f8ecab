#pragma once

#include <QtCore/qglobal.h>
#include "Processing/CSignalArea.h"
#include "Processing/Centroid.h"
#include "Processing/CFWHM.h"

#if defined(PEAKALGORITHM_LIBRARY)
#  define PEAKALGORITHM_EXPORT Q_DECL_EXPORT
#else
#  define PEAKALGORITHM_EXPORT Q_DECL_IMPORT
#endif

class PEAKALGORITHM_EXPORT cPeakAlgorithm
{
public:
    cPeakAlgorithm(){}

private:
    libPeakAlgorithm::CCentroid mCentroidServices;
public:
    double CentroidData(std::vector<double>const& arrAbsc, std::vector<double>const& arrOrd,
                      std::vector<double>& dAbsc,std::vector<double>& dOrd, std::vector<int>& iStart, std::vector<int>& iEnd,std::vector<double>& cArea, int width){
        if (*max_element(arrAbsc.begin(),arrAbsc.end()) > 0)
            return mCentroidServices.CentroidData(arrAbsc, arrOrd, dAbsc, dOrd, iStart, iEnd, cArea, width);
        return -1;
    }

    void CentroidData(double dStartAbsc, double dEndAbsc, double dIncAbsc,std::vector<double> dOrd,std::vector<double>& cAbsc,
                      std::vector<double>& cOrd, std::vector<int>& iStart, std::vector<int>& iEnd,std::vector<double>& cArea, int width){
        if (dStartAbsc < dEndAbsc)
            mCentroidServices.CentroidData(dStartAbsc, dEndAbsc, dIncAbsc, dOrd, cAbsc, cOrd, iStart, iEnd, cArea, width);}

    /*void CentroidDataThreshold(std::vector<double> dAbsc, std::vector<double> dOrd, std::vector<double> darrBaseline, std::vector<double>& cAbsc,
        std::vector<double>& cOrd, std::vector<int>& iarrStart, std::vector<int>& iarrEnd){
        if (*max_element(dAbsc.begin(),dAbsc.end())> 0)
            mCentroidServices.CentroidDataThreshold(dAbsc, dOrd, darrBaseline, cAbsc, cOrd, iarrStart, iarrEnd);}*/

    /*void CentroidData(std::vector<double> dAbsc, std::vector<double> dOrd, std::vector<double> darrBaseline, std::vector<double>& cAbsc,
        std::vector<double>& cOrd, std::vector<int>& iarrStart, std::vector<int>& iarrEnd){
        if (*max_element(dAbsc.begin(),dAbsc.end()) > 0)
            mCentroidServices.CentroidData(dAbsc, dOrd, darrBaseline, cAbsc, cOrd, iarrStart, iarrEnd);}*/

    double CentroidDataA(std::vector<double> arrAbsc, std::vector<double> arrOrd,
                         std::vector<double>& dAbsc, std::vector<double>& dOrd, std::vector<int>& iStart, std::vector<int>& iEnd,std::vector<double>& cArea, int width){
        double dOut =0;
        if (*max_element(arrAbsc.begin(),arrAbsc.end()) > 0)
            dOut = mCentroidServices.CentroidDataA(arrAbsc, arrOrd, dAbsc, dOrd, iStart, iEnd,cArea, width);
        return dOut;}

private:
    libPeakAlgorithm::CFWHM mFWHMServices;
public:
    std::vector<double> GetHHPW(std::vector<double>& dAbsc, std::vector<double>& dOrd, std::vector<int>& iStart, std::vector<int>& iEnd){
        return mFWHMServices.GetFWHM(dAbsc, dOrd, iStart, iEnd);}
    //	std::vector<double> GetMassResolution(std::vector<double> dAbsc,std::vector<double> dOrd,std::vector<double> dXC,std::vector<int> iStart,std::vector<int> iEnd){
    //		return mFWHMServices.GetMassResolution(dAbsc, dOrd, dXC, iStart, iEnd);}

private:
    libPeakAlgorithm::CSignalArea mSignalAreaServices;
public:
    double SignalArea(std::vector<double>& dAbsc,std::vector<double>& dOrd){
        return mSignalAreaServices.SignalArea(dAbsc, dOrd);}
    std::vector<double> SignalArea(std::vector<double>& dAbsc,std::vector<double>& dOrd,std::vector<int>& Start,std::vector<int>& End){
        return mSignalAreaServices.SignalArea(dAbsc, dOrd, Start, End);}
    double SignalArea(std::vector<double>& dAbsc,std::vector<double>& dOrd, int istart, int iend){
        return mSignalAreaServices.SignalArea(dAbsc, dOrd, istart, iend);}
    double SignalArea(std::vector<double>& dAbsc,std::vector<double>& dOrd, double dstart, double dend){
        return mSignalAreaServices.SignalArea(dAbsc, dOrd, dstart, dend);}
    double SignalContourArea(std::vector<double>& dAbsc,std::vector<double>& dOrd){
        return mSignalAreaServices.SignalContourArea(dAbsc, dOrd);}
};
