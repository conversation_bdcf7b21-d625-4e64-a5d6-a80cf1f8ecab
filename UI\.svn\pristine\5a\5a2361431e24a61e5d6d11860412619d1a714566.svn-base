/****************************************************************************
** Meta object code from reading C++ file 'uiQ13SIMParamEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiSingleAcquisition/uiQ13SIMParamEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiQ13SIMParamEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiQ13SIMParamEditor_t {
    QByteArrayData data[9];
    char stringdata0[177];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiQ13SIMParamEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiQ13SIMParamEditor_t qt_meta_stringdata_uiQ13SIMParamEditor = {
    {
QT_MOC_LITERAL(0, 0, 19), // "uiQ13SIMParamEditor"
QT_MOC_LITERAL(1, 20, 17), // "on_chanel_Changed"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 17), // "QTableWidgetItem*"
QT_MOC_LITERAL(4, 57, 4), // "item"
QT_MOC_LITERAL(5, 62, 43), // "on_lineEdit_Polarity_switch_t..."
QT_MOC_LITERAL(6, 106, 4), // "arg1"
QT_MOC_LITERAL(7, 111, 32), // "on_lineEdit_pauseTime_textEdited"
QT_MOC_LITERAL(8, 144, 32) // "on_lineEdit_eventTime_textEdited"

    },
    "uiQ13SIMParamEditor\0on_chanel_Changed\0"
    "\0QTableWidgetItem*\0item\0"
    "on_lineEdit_Polarity_switch_time_textEdited\0"
    "arg1\0on_lineEdit_pauseTime_textEdited\0"
    "on_lineEdit_eventTime_textEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiQ13SIMParamEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x08 /* Private */,
       5,    1,   37,    2, 0x08 /* Private */,
       7,    1,   40,    2, 0x08 /* Private */,
       8,    1,   43,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,

       0        // eod
};

void uiQ13SIMParamEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiQ13SIMParamEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_chanel_Changed((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1]))); break;
        case 1: _t->on_lineEdit_Polarity_switch_time_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->on_lineEdit_pauseTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->on_lineEdit_eventTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiQ13SIMParamEditor::staticMetaObject = { {
    &uiBaseParamEditor::staticMetaObject,
    qt_meta_stringdata_uiQ13SIMParamEditor.data,
    qt_meta_data_uiQ13SIMParamEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiQ13SIMParamEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiQ13SIMParamEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiQ13SIMParamEditor.stringdata0))
        return static_cast<void*>(this);
    return uiBaseParamEditor::qt_metacast(_clname);
}

int uiQ13SIMParamEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = uiBaseParamEditor::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
