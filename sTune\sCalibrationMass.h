#pragma once

#include "sCalibrationMass/sCalibrationView.h"
#include "sMapSetMZ.h"

#include <QWidget>
#include <uiCalibrationMass.h>

class sCalibrationMass : public uiCalibrationMass
{
    Q_OBJECT

public:
    explicit sCalibrationMass(sMapSetMZ* pMapSetMZ,
                              QWidget *parent = nullptr);
    ~sCalibrationMass();
    virtual void initClass(QString& filePath);
    sCalibrationView* getCalibrationViewQ1POS(){
        return (sCalibrationView*)mCalibrationViewQ1POS;
    }
    sCalibrationView* getCalibrationViewQ1NEG(){
        return (sCalibrationView*)mCalibrationViewQ1NEG;
    }
    sCalibrationView* getCalibrationViewQ3POS(){
        return (sCalibrationView*)mCalibrationViewQ3POS;
    }
    sCalibrationView* getCalibrationViewQ3NEG(){
        return (sCalibrationView*)mCalibrationViewQ3NEG;
    }
};

