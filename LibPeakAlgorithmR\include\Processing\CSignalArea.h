#pragma once
#include<vector>

namespace libPeakAlgorithm{
class CSignalArea
{
public:
	CSignalArea(void){};
	~CSignalArea(){};
    double SignalArea(std::vector<double>& dAbsc,std::vector<double>& dOrd);
    double SignalContourArea(std::vector<double>& dAbsc,std::vector<double>& dOrd);
    double SignalArea(double dAbscInc, std::vector<double>& dOrd);
    double SignalArea(std::vector<double>& dAbsc, std::vector<double>& dOrd, int start, int end);
    std::vector<double> SignalArea(std::vector<double>& dAbsc, std::vector<double>& dOrd, std::vector<int>& Start, std::vector<int>& End);
    double SignalArea(std::vector<double>& dAbsc, std::vector<double>& dOrd, double start, double end);
    double MaxSignal(std::vector<double>& dOrd);
    double AverageSignal(std::vector<double>& dOrd);
    double TotalSignal(std::vector<double>& dOrd);
};
}
