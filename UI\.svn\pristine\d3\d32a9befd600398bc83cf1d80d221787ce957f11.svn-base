#pragma once

#include <QScriptEngine>
#include <QWidget>
#include <qUiWidget.h>
#include "ui_uiQueue.h"

class uiQueue : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiQueue(QScriptEngine* pScriptEngine,
                    QWidget *parent = nullptr);
    ~uiQueue();
    virtual void initClass(QString& filePath);

protected:
    Ui::uiQueue ui;
    bool initUI(QString& filePath);
    virtual void createToolBar();

public slots:
    void onSubmit(QList<QMap<QString, QString>>& pList, QString date);

private slots:
    void onUI_MB_START_QUEUE();
    void onUI_MB_STOP_QUEUE();
    void onUI_MB_PRINT_QUEUEH();
};

