/********************************************************************************
** Form generated from reading UI file 'uiSingleAcquisition.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UISINGLEACQUISITION_H
#define UI_UISINGLEACQUISITION_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "LibWidget/sMyButton.h"

QT_BEGIN_NAMESPACE

class Ui_uiSingleAcquisition
{
public:
    QVBoxLayout *verticalLayout_3;
    QWidget *UI_W_TOOLBAR_SINGLEACQ;
    QHBoxLayout *horizontalLayout;
    MyWidget::sMyButton *UI_MB_LOAD_SINGLEACQ;
    MyWidget::sMyButton *UI_MB_SAVE_SINGLEACQ;
    QSpacerItem *horizontalSpacer;
    MyWidget::sMyButton *UI_MB_RUN_SINGLEACQ;
    MyWidget::sMyButton *UI_MB_STOP_SINGLEACQ;
    QHBoxLayout *horizontalLayout_2;
    QWidget *UI_W_MENU_SINGLEACQ;
    QVBoxLayout *verticalLayout_2;
    QTabWidget *UI_TABWIDGET_PARAM_SINGLEACQ;
    QWidget *UI_W_Q1SCAN_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_Q1SCAN_SINGLEACQ;
    QWidget *UI_W_Q3SCAN_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_Q3SCAN_SINGLEACQ;
    QWidget *UI_W_Q1SIM_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_Q1SIM_SINGLEACQ;
    QWidget *UI_W_Q3SIM_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_Q3SIM_SINGLEACQ;
    QWidget *UI_W_MRM_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_MRM_SINGLEACQ;
    QWidget *UI_W_IONSCAN_SINGLEACQ;
    QHBoxLayout *UI_LAYOUT_IONSCAN_SINGLEACQ;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label;
    QComboBox *UI_CB_ACQ_MODE_SINGLEACQ;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *UI_PB_ADVANCE_SINGLEACQ;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_2;
    QLineEdit *UI_LE_AcqFreq_SINGLEACQ;
    QLabel *label_3;
    QLineEdit *UI_LE_AcqACC_SINGLEACQ;
    QVBoxLayout *UI_LAYOUT_CHART_TIC_SINGLEACQ;
    QScrollArea *UI_SCROLLAREA_CHART_SINGLEACQ;
    QWidget *scrollAreaWidgetContents;
    QHBoxLayout *UI_LAYOUT_PROCESS;
    QWidget *widget_3;

    void setupUi(QWidget *uiSingleAcquisition)
    {
        if (uiSingleAcquisition->objectName().isEmpty())
            uiSingleAcquisition->setObjectName(QString::fromUtf8("uiSingleAcquisition"));
        uiSingleAcquisition->resize(819, 527);
        verticalLayout_3 = new QVBoxLayout(uiSingleAcquisition);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        UI_W_TOOLBAR_SINGLEACQ = new QWidget(uiSingleAcquisition);
        UI_W_TOOLBAR_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_TOOLBAR_SINGLEACQ"));
        horizontalLayout = new QHBoxLayout(UI_W_TOOLBAR_SINGLEACQ);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        UI_MB_LOAD_SINGLEACQ = new MyWidget::sMyButton(UI_W_TOOLBAR_SINGLEACQ);
        UI_MB_LOAD_SINGLEACQ->setObjectName(QString::fromUtf8("UI_MB_LOAD_SINGLEACQ"));

        horizontalLayout->addWidget(UI_MB_LOAD_SINGLEACQ);

        UI_MB_SAVE_SINGLEACQ = new MyWidget::sMyButton(UI_W_TOOLBAR_SINGLEACQ);
        UI_MB_SAVE_SINGLEACQ->setObjectName(QString::fromUtf8("UI_MB_SAVE_SINGLEACQ"));

        horizontalLayout->addWidget(UI_MB_SAVE_SINGLEACQ);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        UI_MB_RUN_SINGLEACQ = new MyWidget::sMyButton(UI_W_TOOLBAR_SINGLEACQ);
        UI_MB_RUN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_MB_RUN_SINGLEACQ"));

        horizontalLayout->addWidget(UI_MB_RUN_SINGLEACQ);

        UI_MB_STOP_SINGLEACQ = new MyWidget::sMyButton(UI_W_TOOLBAR_SINGLEACQ);
        UI_MB_STOP_SINGLEACQ->setObjectName(QString::fromUtf8("UI_MB_STOP_SINGLEACQ"));

        horizontalLayout->addWidget(UI_MB_STOP_SINGLEACQ);


        verticalLayout_3->addWidget(UI_W_TOOLBAR_SINGLEACQ);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, -1, -1, -1);
        UI_W_MENU_SINGLEACQ = new QWidget(uiSingleAcquisition);
        UI_W_MENU_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_MENU_SINGLEACQ"));
        UI_W_MENU_SINGLEACQ->setMinimumSize(QSize(0, 0));
        UI_W_MENU_SINGLEACQ->setMaximumSize(QSize(320, 16777215));
        verticalLayout_2 = new QVBoxLayout(UI_W_MENU_SINGLEACQ);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 9, 0, 9);
        UI_TABWIDGET_PARAM_SINGLEACQ = new QTabWidget(UI_W_MENU_SINGLEACQ);
        UI_TABWIDGET_PARAM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_TABWIDGET_PARAM_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->setMaximumSize(QSize(320, 16777215));
        UI_W_Q1SCAN_SINGLEACQ = new QWidget();
        UI_W_Q1SCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_Q1SCAN_SINGLEACQ"));
        UI_LAYOUT_Q1SCAN_SINGLEACQ = new QHBoxLayout(UI_W_Q1SCAN_SINGLEACQ);
        UI_LAYOUT_Q1SCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q1SCAN_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q1SCAN_SINGLEACQ, QString());
        UI_W_Q3SCAN_SINGLEACQ = new QWidget();
        UI_W_Q3SCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_Q3SCAN_SINGLEACQ"));
        UI_LAYOUT_Q3SCAN_SINGLEACQ = new QHBoxLayout(UI_W_Q3SCAN_SINGLEACQ);
        UI_LAYOUT_Q3SCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q3SCAN_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q3SCAN_SINGLEACQ, QString());
        UI_W_Q1SIM_SINGLEACQ = new QWidget();
        UI_W_Q1SIM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_Q1SIM_SINGLEACQ"));
        UI_LAYOUT_Q1SIM_SINGLEACQ = new QHBoxLayout(UI_W_Q1SIM_SINGLEACQ);
        UI_LAYOUT_Q1SIM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q1SIM_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q1SIM_SINGLEACQ, QString());
        UI_W_Q3SIM_SINGLEACQ = new QWidget();
        UI_W_Q3SIM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_Q3SIM_SINGLEACQ"));
        UI_LAYOUT_Q3SIM_SINGLEACQ = new QHBoxLayout(UI_W_Q3SIM_SINGLEACQ);
        UI_LAYOUT_Q3SIM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q3SIM_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q3SIM_SINGLEACQ, QString());
        UI_W_MRM_SINGLEACQ = new QWidget();
        UI_W_MRM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_MRM_SINGLEACQ"));
        UI_LAYOUT_MRM_SINGLEACQ = new QHBoxLayout(UI_W_MRM_SINGLEACQ);
        UI_LAYOUT_MRM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_MRM_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_MRM_SINGLEACQ, QString());
        UI_W_IONSCAN_SINGLEACQ = new QWidget();
        UI_W_IONSCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_W_IONSCAN_SINGLEACQ"));
        UI_LAYOUT_IONSCAN_SINGLEACQ = new QHBoxLayout(UI_W_IONSCAN_SINGLEACQ);
        UI_LAYOUT_IONSCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_IONSCAN_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_IONSCAN_SINGLEACQ, QString());

        verticalLayout_2->addWidget(UI_TABWIDGET_PARAM_SINGLEACQ);

        widget_2 = new QWidget(UI_W_MENU_SINGLEACQ);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMaximumSize(QSize(320, 16777215));
        verticalLayout = new QVBoxLayout(widget_2);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label = new QLabel(widget_2);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout_3->addWidget(label);

        UI_CB_ACQ_MODE_SINGLEACQ = new QComboBox(widget_2);
        UI_CB_ACQ_MODE_SINGLEACQ->addItem(QString());
        UI_CB_ACQ_MODE_SINGLEACQ->addItem(QString());
        UI_CB_ACQ_MODE_SINGLEACQ->setObjectName(QString::fromUtf8("UI_CB_ACQ_MODE_SINGLEACQ"));

        horizontalLayout_3->addWidget(UI_CB_ACQ_MODE_SINGLEACQ);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_2);

        UI_PB_ADVANCE_SINGLEACQ = new QPushButton(widget_2);
        UI_PB_ADVANCE_SINGLEACQ->setObjectName(QString::fromUtf8("UI_PB_ADVANCE_SINGLEACQ"));

        horizontalLayout_3->addWidget(UI_PB_ADVANCE_SINGLEACQ);


        verticalLayout->addLayout(horizontalLayout_3);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label_2 = new QLabel(widget_2);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout_4->addWidget(label_2);

        UI_LE_AcqFreq_SINGLEACQ = new QLineEdit(widget_2);
        UI_LE_AcqFreq_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LE_AcqFreq_SINGLEACQ"));

        horizontalLayout_4->addWidget(UI_LE_AcqFreq_SINGLEACQ);

        label_3 = new QLabel(widget_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout_4->addWidget(label_3);

        UI_LE_AcqACC_SINGLEACQ = new QLineEdit(widget_2);
        UI_LE_AcqACC_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LE_AcqACC_SINGLEACQ"));

        horizontalLayout_4->addWidget(UI_LE_AcqACC_SINGLEACQ);


        verticalLayout->addLayout(horizontalLayout_4);


        verticalLayout_2->addWidget(widget_2);


        horizontalLayout_2->addWidget(UI_W_MENU_SINGLEACQ);

        UI_LAYOUT_CHART_TIC_SINGLEACQ = new QVBoxLayout();
        UI_LAYOUT_CHART_TIC_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_CHART_TIC_SINGLEACQ"));
        UI_SCROLLAREA_CHART_SINGLEACQ = new QScrollArea(uiSingleAcquisition);
        UI_SCROLLAREA_CHART_SINGLEACQ->setObjectName(QString::fromUtf8("UI_SCROLLAREA_CHART_SINGLEACQ"));
        UI_SCROLLAREA_CHART_SINGLEACQ->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 464, 433));
        UI_LAYOUT_PROCESS = new QHBoxLayout(scrollAreaWidgetContents);
        UI_LAYOUT_PROCESS->setObjectName(QString::fromUtf8("UI_LAYOUT_PROCESS"));
        UI_SCROLLAREA_CHART_SINGLEACQ->setWidget(scrollAreaWidgetContents);

        UI_LAYOUT_CHART_TIC_SINGLEACQ->addWidget(UI_SCROLLAREA_CHART_SINGLEACQ);


        horizontalLayout_2->addLayout(UI_LAYOUT_CHART_TIC_SINGLEACQ);


        verticalLayout_3->addLayout(horizontalLayout_2);

        widget_3 = new QWidget(uiSingleAcquisition);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));

        verticalLayout_3->addWidget(widget_3);


        retranslateUi(uiSingleAcquisition);

        UI_TABWIDGET_PARAM_SINGLEACQ->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(uiSingleAcquisition);
    } // setupUi

    void retranslateUi(QWidget *uiSingleAcquisition)
    {
        uiSingleAcquisition->setWindowTitle(QApplication::translate("uiSingleAcquisition", "Form", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q1SCAN_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "Q1 Scan", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q3SCAN_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "Q3 Scan", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q1SIM_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "Q1 SIM", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q3SIM_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "Q3 SIM", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_MRM_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "MRM", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_IONSCAN_SINGLEACQ), QApplication::translate("uiSingleAcquisition", "IonScan", nullptr));
        label->setText(QApplication::translate("uiSingleAcquisition", "AcquisitionMode:", nullptr));
        UI_CB_ACQ_MODE_SINGLEACQ->setItemText(0, QApplication::translate("uiSingleAcquisition", "ADC", nullptr));
        UI_CB_ACQ_MODE_SINGLEACQ->setItemText(1, QApplication::translate("uiSingleAcquisition", "TDC", nullptr));

        UI_PB_ADVANCE_SINGLEACQ->setText(QApplication::translate("uiSingleAcquisition", "Advance", nullptr));
        label_2->setText(QApplication::translate("uiSingleAcquisition", "AcqFreq(Hz):", nullptr));
        UI_LE_AcqFreq_SINGLEACQ->setText(QApplication::translate("uiSingleAcquisition", "1000000", nullptr));
        label_3->setText(QApplication::translate("uiSingleAcquisition", "AcqACC:", nullptr));
        UI_LE_AcqACC_SINGLEACQ->setText(QApplication::translate("uiSingleAcquisition", "32", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiSingleAcquisition: public Ui_uiSingleAcquisition {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UISINGLEACQUISITION_H
