﻿#pragma once
//#include <sMethod/cTQ_StructCMD_HZH.h>
#include <QTableWidgetItem>
#include <uiQ13SIMParamEditor.h>
#include <sTune/sMapSetMZ.h>

class Q13SIMParamEditor : public uiQ13SIMParamEditor//AbstractParamEditor
{
    Q_OBJECT
public:
    explicit Q13SIMParamEditor(QString Q,
                               sMapSetMZ* pMapSetMZ,
                               QWidget* parent = nullptr);
    ~Q13SIMParamEditor(){}
    void initClass(QString& filePath);
    bool getParamEvent(QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM,
                       QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM_LDAC,
                       int& Polarity);

private:
    sMapSetMZ* mMapSetMZ= nullptr;
};

