/****************************************************************************
** Meta object code from reading C++ file 'uiCalibrationView.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiTune/uiCalibrationMass/uiCalibrationView.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiCalibrationView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiCalibrationView_t {
    QByteArrayData data[6];
    char stringdata0[96];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiCalibrationView_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiCalibrationView_t qt_meta_stringdata_uiCalibrationView = {
    {
QT_MOC_LITERAL(0, 0, 17), // "uiCalibrationView"
QT_MOC_LITERAL(1, 18, 9), // "onFocused"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 5), // "speed"
QT_MOC_LITERAL(4, 35, 28), // "on_UI_PB_ADDMASS_CAL_clicked"
QT_MOC_LITERAL(5, 64, 31) // "on_UI_PB_DELETEMASS_CAL_clicked"

    },
    "uiCalibrationView\0onFocused\0\0speed\0"
    "on_UI_PB_ADDMASS_CAL_clicked\0"
    "on_UI_PB_DELETEMASS_CAL_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiCalibrationView[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x0a /* Public */,
       4,    0,   32,    2, 0x08 /* Private */,
       5,    0,   33,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiCalibrationView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiCalibrationView *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onFocused((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->on_UI_PB_ADDMASS_CAL_clicked(); break;
        case 2: _t->on_UI_PB_DELETEMASS_CAL_clicked(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiCalibrationView::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiCalibrationView.data,
    qt_meta_data_uiCalibrationView,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiCalibrationView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiCalibrationView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiCalibrationView.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiCalibrationView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
