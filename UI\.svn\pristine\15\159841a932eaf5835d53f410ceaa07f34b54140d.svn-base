#pragma once

#include <QWidget>

namespace Ui {
class uiEditMRM;
}

class uiEditMRM : public QWidget
{
    Q_OBJECT

public:
    explicit uiEditMRM(QWidget *parent = nullptr);
    ~uiEditMRM();
    void setParam(QString& str){

    }
    QString getParam(){
        QString str;
        return str;
    }
private slots:
    void on_comboBox_2_activated(int index);

    void on_UI_CB_SMRM_EMRM_stateChanged(int arg1);

private:
    Ui::uiEditMRM *ui;
};

