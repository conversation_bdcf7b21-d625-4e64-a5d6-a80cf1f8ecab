/****************************************************************************
** Meta object code from reading C++ file 'uiMapSetMZ.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiTune/uiMapSetMZ.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiMapSetMZ.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiMapSetMZ_t {
    QByteArrayData data[7];
    char stringdata0[123];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiMapSetMZ_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiMapSetMZ_t qt_meta_stringdata_uiMapSetMZ = {
    {
QT_MOC_LITERAL(0, 0, 10), // "uiMapSetMZ"
QT_MOC_LITERAL(1, 11, 19), // "mzVoltageMapChanged"
QT_MOC_LITERAL(2, 31, 0), // ""
QT_MOC_LITERAL(3, 32, 21), // "HZH::paramMZ_Voltages"
QT_MOC_LITERAL(4, 54, 10), // "mzVoltages"
QT_MOC_LITERAL(5, 65, 27), // "on_UI_PB_SAVE_SETMZ_clicked"
QT_MOC_LITERAL(6, 93, 29) // "on_UI_PB_UPDATE_SETMZ_clicked"

    },
    "uiMapSetMZ\0mzVoltageMapChanged\0\0"
    "HZH::paramMZ_Voltages\0mzVoltages\0"
    "on_UI_PB_SAVE_SETMZ_clicked\0"
    "on_UI_PB_UPDATE_SETMZ_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiMapSetMZ[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   32,    2, 0x08 /* Private */,
       6,    0,   33,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiMapSetMZ::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiMapSetMZ *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->mzVoltageMapChanged((*reinterpret_cast< const HZH::paramMZ_Voltages(*)>(_a[1]))); break;
        case 1: _t->on_UI_PB_SAVE_SETMZ_clicked(); break;
        case 2: _t->on_UI_PB_UPDATE_SETMZ_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiMapSetMZ::*)(const HZH::paramMZ_Voltages & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiMapSetMZ::mzVoltageMapChanged)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiMapSetMZ::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiMapSetMZ.data,
    qt_meta_data_uiMapSetMZ,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiMapSetMZ::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiMapSetMZ::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiMapSetMZ.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiMapSetMZ::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void uiMapSetMZ::mzVoltageMapChanged(const HZH::paramMZ_Voltages & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
