#pragma once
#include <qglobal.h>
#include <vector>
/**
 * @brief
 *
 */
namespace libDataFilter{
class SCList
{
public:
    struct DataDouble{
        std::vector<double> data; /**< TODO */
        DataDouble* next; /**< TODO */
        DataDouble* front; /**< TODO */
        DataDouble(){
           next = nullptr;
           front = nullptr;
        }
    };
    SCList(uint maxSize = 1):m_maxSize(maxSize){}
    ~SCList(){
        release();
    }
    DataDouble* pushNext(const std::vector<double>& data);
    DataDouble* pushFront(const std::vector<double>& data);
    DataDouble* toNext();
    uint size(){
        return m_size;
    }
    uint maxSize() const{
        return m_maxSize;
    }
    void setMaxSize(const uint &maxSize);
    DataDouble* currentData(){
        return m_current;
    }
    void release();

private:
    DataDouble* m_current = nullptr; /**< TODO */
    uint m_size = 0; /**< TODO */
    uint m_maxSize; /**< TODO */
};
}
