/*!
    \page building
    \title Qt Xlsx Build

    \note QZipWriter and QZipReader which live in gui-private is used in
    this library. For linux user, if your Qt is installed through package
    manager tools such "apt-get", make sure that you have installed the Qt5
    develop package *qtbase5-private-dev* ;
    if you Qt is built from source by yourself,
    or download from qt-project.org directly, nothing need to do.

    \section1 Usage(1): Use Xlsx as Qt5's addon module

    1. Download the source code from \l {https://github.com/dbzhang800/QtXlsxWriter/archive/master.zip} {github.com}.

    2. Put the source code in any directory you like. At the toplevel directory run

    \note Perl is needed in this step.

    \code
    qmake
    make
    make install
    \endcode

    The library, the header files, and others will be installed to your system.

    3. Add following line to your qmake's project file:

    \code
    QT += xlsx
    \endcode

    4. Then, using Qt Xlsx in your code

    \code
    #include "xlsxdocument.h"
    int main()
    {
        QXlsx::Document xlsx;
        xlsx.write("A1", "Hello Qt!");
        xlsx.saveAs("Test.xlsx");
        return 0;
    }
    \endcode

    \section1 Usage(2): Use source code directly

    The package contains a qtxlsx.pri file that allows you to integrate
    the component into applications that use qmake for the build step.

    1. Download the source code from \l {https://github.com/dbzhang800/QtXlsxWriter/archive/master.zip} {github.com}

    2. Put the source code in any directory you like. For example, 3rdparty:

    \code
    |-- project.pro
    |-- ....
    |-- 3rdparty\
    |     |-- qtxlsx\
    |     |
    \endcode

    3. Add following line to your qmake project file:

    \code
    include(3rdparty/qtxlsx/src/xlsx/qtxlsx.pri)
    \endcode

    \note If you like, you can copy all files from *src/xlsx* to your application's source path. Then add following line to your project file:

    \code
    include(qtxlsx.pri)
    \endcode

    \note If you do not use qmake, you need to define the following macro manually

    \code
    XLSX_NO_LIB
    \endcode

    4. Then, using Qt Xlsx in your code
*/
