/****************************************************************************
** Meta object code from reading C++ file 'uiCtrlTFG.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiState/uiCtrlTFG.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiCtrlTFG.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiCtrlTFG_t {
    QByteArrayData data[30];
    char stringdata0[618];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiCtrlTFG_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiCtrlTFG_t qt_meta_stringdata_uiCtrlTFG = {
    {
QT_MOC_LITERAL(0, 0, 9), // "uiCtrlTFG"
QT_MOC_LITERAL(1, 10, 21), // "on_Monitor_ON_clicked"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 23), // "on_Purge_Gas_ON_clicked"
QT_MOC_LITERAL(4, 57, 16), // "on_IG_ON_clicked"
QT_MOC_LITERAL(5, 74, 24), // "on_Tpump_Frq_textChanged"
QT_MOC_LITERAL(6, 99, 4), // "arg1"
QT_MOC_LITERAL(7, 104, 19), // "on_Tpump_ON_clicked"
QT_MOC_LITERAL(8, 124, 19), // "on_Rpump_ON_clicked"
QT_MOC_LITERAL(9, 144, 24), // "on_Auto_ShutDown_clicked"
QT_MOC_LITERAL(10, 169, 23), // "on_Auto_StartUp_clicked"
QT_MOC_LITERAL(11, 193, 21), // "on_IG12_RF_ON_clicked"
QT_MOC_LITERAL(12, 215, 19), // "on_CC_RF_ON_clicked"
QT_MOC_LITERAL(13, 235, 19), // "on_Q1_RF_ON_clicked"
QT_MOC_LITERAL(14, 255, 20), // "on_Q1_ABMode_clicked"
QT_MOC_LITERAL(15, 276, 24), // "on_Q3_RF_on_ctrl_clicked"
QT_MOC_LITERAL(16, 301, 25), // "on_Q3_ABmode_ctrl_clicked"
QT_MOC_LITERAL(17, 327, 21), // "on_Heat_PW_ON_clicked"
QT_MOC_LITERAL(18, 349, 18), // "on_Heat_ON_clicked"
QT_MOC_LITERAL(19, 368, 21), // "on_CID_Gas_ON_clicked"
QT_MOC_LITERAL(20, 390, 21), // "on_CID_Rel_ON_clicked"
QT_MOC_LITERAL(21, 412, 20), // "on_ESI_HV_ON_clicked"
QT_MOC_LITERAL(22, 433, 20), // "on_CUR_HV_ON_clicked"
QT_MOC_LITERAL(23, 454, 20), // "on_HED_HV_ON_clicked"
QT_MOC_LITERAL(24, 475, 20), // "on_DET_HV_ON_clicked"
QT_MOC_LITERAL(25, 496, 20), // "on_IG0_RF_ON_clicked"
QT_MOC_LITERAL(26, 517, 28), // "on_update_SerialPort_clicked"
QT_MOC_LITERAL(27, 546, 26), // "on_Open_SerialPort_clicked"
QT_MOC_LITERAL(28, 573, 20), // "on_Scan_Ctrl_clicked"
QT_MOC_LITERAL(29, 594, 23) // "serialPort1DataReceived"

    },
    "uiCtrlTFG\0on_Monitor_ON_clicked\0\0"
    "on_Purge_Gas_ON_clicked\0on_IG_ON_clicked\0"
    "on_Tpump_Frq_textChanged\0arg1\0"
    "on_Tpump_ON_clicked\0on_Rpump_ON_clicked\0"
    "on_Auto_ShutDown_clicked\0"
    "on_Auto_StartUp_clicked\0on_IG12_RF_ON_clicked\0"
    "on_CC_RF_ON_clicked\0on_Q1_RF_ON_clicked\0"
    "on_Q1_ABMode_clicked\0on_Q3_RF_on_ctrl_clicked\0"
    "on_Q3_ABmode_ctrl_clicked\0"
    "on_Heat_PW_ON_clicked\0on_Heat_ON_clicked\0"
    "on_CID_Gas_ON_clicked\0on_CID_Rel_ON_clicked\0"
    "on_ESI_HV_ON_clicked\0on_CUR_HV_ON_clicked\0"
    "on_HED_HV_ON_clicked\0on_DET_HV_ON_clicked\0"
    "on_IG0_RF_ON_clicked\0on_update_SerialPort_clicked\0"
    "on_Open_SerialPort_clicked\0"
    "on_Scan_Ctrl_clicked\0serialPort1DataReceived"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiCtrlTFG[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      27,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  149,    2, 0x08 /* Private */,
       3,    0,  150,    2, 0x08 /* Private */,
       4,    0,  151,    2, 0x08 /* Private */,
       5,    1,  152,    2, 0x08 /* Private */,
       7,    0,  155,    2, 0x08 /* Private */,
       8,    0,  156,    2, 0x08 /* Private */,
       9,    0,  157,    2, 0x08 /* Private */,
      10,    0,  158,    2, 0x08 /* Private */,
      11,    0,  159,    2, 0x08 /* Private */,
      12,    0,  160,    2, 0x08 /* Private */,
      13,    0,  161,    2, 0x08 /* Private */,
      14,    0,  162,    2, 0x08 /* Private */,
      15,    0,  163,    2, 0x08 /* Private */,
      16,    0,  164,    2, 0x08 /* Private */,
      17,    0,  165,    2, 0x08 /* Private */,
      18,    0,  166,    2, 0x08 /* Private */,
      19,    0,  167,    2, 0x08 /* Private */,
      20,    0,  168,    2, 0x08 /* Private */,
      21,    0,  169,    2, 0x08 /* Private */,
      22,    0,  170,    2, 0x08 /* Private */,
      23,    0,  171,    2, 0x08 /* Private */,
      24,    0,  172,    2, 0x08 /* Private */,
      25,    0,  173,    2, 0x08 /* Private */,
      26,    0,  174,    2, 0x08 /* Private */,
      27,    0,  175,    2, 0x08 /* Private */,
      28,    0,  176,    2, 0x08 /* Private */,
      29,    0,  177,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiCtrlTFG::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiCtrlTFG *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_Monitor_ON_clicked(); break;
        case 1: _t->on_Purge_Gas_ON_clicked(); break;
        case 2: _t->on_IG_ON_clicked(); break;
        case 3: _t->on_Tpump_Frq_textChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->on_Tpump_ON_clicked(); break;
        case 5: _t->on_Rpump_ON_clicked(); break;
        case 6: _t->on_Auto_ShutDown_clicked(); break;
        case 7: _t->on_Auto_StartUp_clicked(); break;
        case 8: _t->on_IG12_RF_ON_clicked(); break;
        case 9: _t->on_CC_RF_ON_clicked(); break;
        case 10: _t->on_Q1_RF_ON_clicked(); break;
        case 11: _t->on_Q1_ABMode_clicked(); break;
        case 12: _t->on_Q3_RF_on_ctrl_clicked(); break;
        case 13: _t->on_Q3_ABmode_ctrl_clicked(); break;
        case 14: _t->on_Heat_PW_ON_clicked(); break;
        case 15: _t->on_Heat_ON_clicked(); break;
        case 16: _t->on_CID_Gas_ON_clicked(); break;
        case 17: _t->on_CID_Rel_ON_clicked(); break;
        case 18: _t->on_ESI_HV_ON_clicked(); break;
        case 19: _t->on_CUR_HV_ON_clicked(); break;
        case 20: _t->on_HED_HV_ON_clicked(); break;
        case 21: _t->on_DET_HV_ON_clicked(); break;
        case 22: _t->on_IG0_RF_ON_clicked(); break;
        case 23: _t->on_update_SerialPort_clicked(); break;
        case 24: _t->on_Open_SerialPort_clicked(); break;
        case 25: _t->on_Scan_Ctrl_clicked(); break;
        case 26: _t->serialPort1DataReceived(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiCtrlTFG::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_uiCtrlTFG.data,
    qt_meta_data_uiCtrlTFG,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiCtrlTFG::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiCtrlTFG::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiCtrlTFG.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int uiCtrlTFG::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 27)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 27;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
