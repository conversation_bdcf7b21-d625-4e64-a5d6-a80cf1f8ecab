#include "uiState.h"


uiState::uiState(QWidget *parent) :
    qUiWidget(parent)
{

}

uiState::~uiState()
{
    if(mListWidgetForLog)
        delete mListWidgetForLog;
    mListWidgetForLog=nullptr;
    if(mCtrlTFG)
        delete mCtrlTFG;
    mCtrlTFG=nullptr;
    //delete ui;
}

bool uiState::initUI(QString& filePath)
{
    ui.setupUi(this);
    Qt::WindowFlags m_flags = windowFlags();
    setWindowFlags(m_flags | Qt::WindowStaysOnTopHint);
    ui.UI_LAYOUT_MSG_STATE->addWidget(mListWidgetForLog);
    //mListWidgetForLog->hide();
    ui.UI_LAYOUT_TFG_STATE->addWidget(mCtrlTFG);

    createToolBar();
    return true;
}

void uiState::initClass(QString& filePath)
{
    mListWidgetForLog= new sListWidgetForLog(this);
    mListWidgetForLog->initClass(filePath);
    mCtrlTFG= new uiCtrlTFG(this);
    mCtrlTFG->initClass(filePath);
    initUI(filePath);
}

void uiState::createToolBar()
{

}
