﻿#include "HCSCommandUSB.h"
#include <QCoreApplication>
#include <QTime>
#include <QSettings>
#include <QDateTime>
#include <QCryptographicHash>

#include "USBConnection/HCSCommunicationCMD.h"
using namespace UMA_HCS;

/**
 * @brief ConvertMD5BytesToULongLong  Convert the MD5 string to 128Bit number
 * @param bytes Byte array
 * @param lower Lower 64Bit
 * @param upper Upper 64Bit
 * @return
 */
static bool ConvertMD5BytesToULongLong(const QByteArray& bytes, uint64_t* lower, uint64_t* upper)
{
    if (bytes.length() != 32){
        SWARNING("The MD5 string is not matched to the correct length.");
        return false;
    }

    QByteArray upperBytes(&bytes.data()[0], 16);
    QByteArray lowerBytes(&bytes.data()[16], 16);
    bool bUpper = false;
    bool bLower = false;
    *upper = upperBytes.toULongLong(&bUpper, 16);
    *lower = lowerBytes.toULongLong(&b<PERSON><PERSON><PERSON>, 16);
    return bUpper && bLower;
}

HCSCommandUSB::HCSCommandUSB(QObject *parent) : CommandUSB(parent)
{
    createCommunicationHandle();
    mChannelGet = UINT64_MAX;
    mHCSSampledData.channel = UINT64_MAX;
    //mDataIOPtr = UMA::UMADataIOv2::GetSingletonPtr();
    mEvSegSplitterPtr = std::make_shared<UMAEventSegmentSplitter>();
    mProcessThread.setUserParam(this);
    mProcessThread.setUserFunction(processHCSDataThread);
    mHCSSampledData.frame.setEvSplitter(mEvSegSplitterPtr);
}

//void HCSCommandUSB::setEventConfig(const QList<UMA_Event> &umaEvents, const QString& methodPath)
//{
//    if (mUMAEventConfig == umaEvents)
//        return;
////    mUMAEventConfig = umaEvents;
////    HCSDataFileMeta::HCSFileMetaData _metaData;
////    _metaData.detectorType = 0x00;  // unused, set zero as default
////    _metaData.date = QDateTime::currentSecsSinceEpoch();    // get the epoch time stamp seconds
////    _metaData.massMethodMD5Lower = 0x00;
////    _metaData.massMethodMD5Upper = 0x00;
////    if (!methodPath.isEmpty())
////    {
////        QFile file(methodPath);
////        if (file.open(QFile::ReadOnly))
////        {
////            QCryptographicHash md(QCryptographicHash::Md5);
////            md.addData(file.readAll());
////            auto md5 = md.result().toHex();
////            ConvertMD5BytesToULongLong(md5, &_metaData.massMethodMD5Lower, &_metaData.massMethodMD5Upper);
////        }
////    }
////    _metaData.events.clear();
////    for(auto ev : umaEvents)
////    {
////        _metaData.events.push_back(ev);
////    }
////    mDataFileMeta.setData(_metaData);
////    mEvSegSplitterPtr->setUMAEvents(_metaData.events);
//}

//const QList<UMA_Event> &HCSCommandUSB::eventConfig() const
//{
//    return mUMAEventConfig;
//}

bool HCSCommandUSB::getDataDAQ(UMA_HCS::HCSDataFrame *frame)
{
    bool bRet = false;
    mHCSSampledData.qLock.lock();
    if (mChannelGet != mHCSSampledData.channel){
        frame->copy(&mHCSSampledData.frame);
        mChannelGet = mHCSSampledData.channel;
        bRet = true;
    }else{
        bRet = false;
    }
    mHCSSampledData.qLock.unlock();
    return bRet;
}

int HCSCommandUSB::startDAQ(libControlCCS::ParamCCS::_DAQ_CONFIG &p_DAQ_CONFIG, _StreamBody::Type_Data typeData)
{
    if (mProcessThread.isRunning())
        return -1;
    mChannelGet = UINT64_MAX;
    mHCSSampledData.channel = UINT64_MAX;
//    if(!mCommunicationHandle->clearBuffUSB())
//        return -2;
    return CommandUSB::startDAQ(p_DAQ_CONFIG, typeData);
}

int HCSCommandUSB::processHCSDataThread(void *pParam, const bool &bRunning)
{
    SINFO("N_CommandUSB_processDataThread_start");
    HCSCommandUSB* pCmd = reinterpret_cast<HCSCommandUSB*>(pParam);
    if(!pCmd)
        return -1;

    bool isNewData=false;
    uint tmpVersion=1;
    //uint64_t msTriggerIndex = 0;
    //uint64_t msTriggerFirstID = 0;
    std::vector<uint32_t> umaChData;
    //std::vector<uint64_t> msChIndex;
    QDateTime startTime = QDateTime::currentDateTime();
    while(bRunning&& pCmd->mCommunicationCMD->isThreadRun()){
        isNewData=false;
        HCSCommunicationCMD::HCSDaqPacket packet;
        if(!pCmd->mCommunicationHandle->getDataDAQ(&packet)){//获得一帧数据
            QThread::msleep(1);
            continue;
        }

        umaChData.resize(packet.data.size());
        auto dstIter = umaChData.begin();
        auto srcIter = packet.data.begin();
        //msChIndex.clear();
        QDateTime currentTime = QDateTime::currentDateTime();
        uint64_t timeMs = startTime.msecsTo(currentTime);

        uint64_t TIC = 0;
        for(;dstIter != umaChData.end(); ++dstIter, ++srcIter){
            *dstIter = (*srcIter & 0x03ffffffu); //[25-0] : valid data;
            TIC += *dstIter;
        }
        //msTriggerFirstID = msTriggerIndex;//20240714
        //msChIndex.push_back(0);//20240714
        //msChIndex.push_back(std::abs(std::distance(umaChData.begin(), umaChData.end())));//20240714
        //++ msTriggerIndex;//20240714

        tmpVersion = packet.frameId /*- 1*/;
        isNewData = true;
        pCmd->mHCSSampledData.qLock.lock();
        pCmd->mHCSSampledData.channel = tmpVersion;
        //pCmd->mHCSSampledData.dTIC = (double)TIC;
        pCmd->mHCSSampledData.frame.setData(tmpVersion, (double)timeMs, (double)TIC,
                                            /*msTriggerFirstID, */umaChData/*, msChIndex*/);

        pCmd->mHCSSampledData.qLock.unlock();
//        SDBG("UMA Channel No:" << tmpVersion);
//                 << "UMA  Data Size:" << umaChData.size()
//                 << "MS Channel Cnt:" << msChIndex.size()
//                 << "MS Trigger Idx:" << msTriggerFirstID);

        uint64_t lTimeMs = 10;
        pCmd->mEvSegSplitterPtr->getUMAChannelPeroid(tmpVersion/* + 1*/, &lTimeMs);
        QThread::msleep(lTimeMs / 2);
    }

//    if(bSaveFile){
//        pCmd->closeFileTIC();
//        pCmd->closeFileMass();
//    }
    SINFO("N_CommandUSB_processDataThread_Over");
    return 0;
}

std::shared_ptr<UMAEventSegmentSplitter> HCSCommandUSB::getEvSegSplitterPtr() const
{
    return mEvSegSplitterPtr;
}

void HCSCommandUSB::createCommunicationHandle()
{
    mCommunicationHandle = new HCSCommunicationCMD(this);
    mCommunicationCMD = mCommunicationHandle;
}


