/********************************************************************************
** Form generated from reading UI file 'uiCtrlTFG.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UICTRLTFG_H
#define UI_UICTRLTFG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiCtrlTFG
{
public:
    QHBoxLayout *horizontalLayout_7;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout_2;
    QGroupBox *groupBox;
    QHBoxLayout *horizontalLayout_3;
    QTableWidget *UI_TW_TEMP_CTRL_CT;
    QVBoxLayout *verticalLayout_3;
    QPushButton *Heat_PW_ON;
    QSpacerItem *verticalSpacer_6;
    QLabel *label_16;
    QLabel *Heat_Fan_State;
    QSpacerItem *verticalSpacer_7;
    QPushButton *Heat_ON;
    QSpacerItem *verticalSpacer_8;
    QLabel *label_18;
    QComboBox *HeaterID;
    QGroupBox *groupBox_2;
    QHBoxLayout *horizontalLayout_4;
    QTableWidget *UI_TW_GAS_CTRL_CT;
    QVBoxLayout *verticalLayout_4;
    QPushButton *CID_Gas_ON;
    QPushButton *CID_Rel_ON;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_5;
    QGroupBox *groupBox_6;
    QGridLayout *gridLayout_4;
    QPushButton *IG0_RF_ON;
    QLineEdit *IG12_RF_Tuning;
    QLabel *IG0_RF_State;
    QLineEdit *IG0_RF_Tuning;
    QLabel *IG12_RF_State;
    QPushButton *IG12_RF_ON;
    QLabel *IG_Fan_State;
    QLabel *label_19;
    QGroupBox *groupBox_7;
    QGridLayout *gridLayout_5;
    QPushButton *CC_RF_ON;
    QLineEdit *CC_RF_Tuning;
    QLabel *label_25;
    QLabel *CC_Fan_State;
    QGroupBox *groupBox_9;
    QGridLayout *gridLayout_6;
    QPushButton *Q1_ABMode;
    QPushButton *Q1_RF_ON;
    QLineEdit *Q1_FB_Temp;
    QLineEdit *Q1_RF_Tuning;
    QLabel *Q1_Fan_State;
    QLabel *label_28;
    QLabel *label_29;
    QLabel *Q1_RF_State;
    QGroupBox *groupBox_10;
    QGridLayout *gridLayout_8;
    QPushButton *Q3_ABmode_ctrl;
    QLabel *label_31;
    QLineEdit *Q3_FB_Temp;
    QLabel *label_32;
    QPushButton *Q3_RF_on_ctrl;
    QLineEdit *Q3_RF_Tuning;
    QLabel *Q3_Fan_State;
    QLabel *Q3_RF_State;
    QGroupBox *groupBox_8;
    QGridLayout *gridLayout_7;
    QLineEdit *IS_ID;
    QLabel *label_21;
    QLabel *IS_Door;
    QSpacerItem *verticalSpacer_2;
    QHBoxLayout *horizontalLayout_6;
    QGroupBox *groupBox_11;
    QGridLayout *gridLayout_9;
    QLineEdit *Lens_DC3;
    QLabel *label_40;
    QLineEdit *Lens_DC1;
    QLabel *label_45;
    QLabel *label_38;
    QLabel *label_48;
    QLabel *label_50;
    QLabel *label_41;
    QLabel *label_39;
    QLabel *label_35;
    QLineEdit *Lens_DC5;
    QLineEdit *Lens_DC4;
    QLabel *label_44;
    QLineEdit *Lens_DC8;
    QLabel *label_49;
    QLineEdit *Lens_DC6;
    QLabel *label_47;
    QLabel *label_43;
    QLineEdit *Lens_DC7;
    QLineEdit *Lens_DC2;
    QLabel *label_36;
    QLabel *label_42;
    QLabel *label_46;
    QLabel *label_37;
    QLineEdit *Lens_DC9;
    QLineEdit *Lens_DC10;
    QLineEdit *Lens_DC11;
    QLineEdit *Lens_DC12;
    QLineEdit *Lens_DC13;
    QLineEdit *Lens_DC14;
    QLineEdit *Lens_DC15;
    QLineEdit *Lens_DC16;
    QVBoxLayout *verticalLayout_5;
    QPushButton *Monitor_ON;
    QSpacerItem *verticalSpacer_4;
    QPushButton *Scan_Ctrl;
    QSpacerItem *verticalSpacer_5;
    QVBoxLayout *verticalLayout_6;
    QLineEdit *COMLen;
    QSpacerItem *verticalSpacer_3;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox_3;
    QGridLayout *gridLayout_3;
    QPushButton *Auto_StartUp;
    QPushButton *Rpump_ON;
    QLabel *label_13;
    QPushButton *IG_ON;
    QPushButton *Auto_ShutDown;
    QLineEdit *PG_P;
    QPushButton *Tpump_ON;
    QLineEdit *Tpump_Frq;
    QLabel *Tpump_Fan_State;
    QLabel *label_12;
    QLineEdit *IG_P;
    QPushButton *Purge_Gas_ON;
    QLabel *label_14;
    QLineEdit *Rpump_Frq;
    QGroupBox *groupBox_4;
    QGridLayout *gridLayout_2;
    QLabel *label_5;
    QLabel *label_6;
    QLabel *DET_HV_State;
    QLabel *label_7;
    QLabel *label_3;
    QLineEdit *ESI_HV_IFB;
    QLabel *CUR_HV_State;
    QLabel *label_4;
    QLineEdit *ESI_HV_VFB;
    QLineEdit *Curtain_HV_VFB;
    QLineEdit *HED_HV_VFB;
    QLabel *HED_HV_State;
    QLineEdit *DET_HV_VFB;
    QPushButton *CUR_HV_ON;
    QPushButton *HED_HV_ON;
    QPushButton *DET_HV_ON;
    QPushButton *ESI_HV_ON;
    QLabel *ESI_HV_State;
    QGroupBox *groupBox_5;
    QGridLayout *gridLayout;
    QLabel *label_2;
    QComboBox *comboBox2;
    QComboBox *comboBox1;
    QLabel *label;
    QPushButton *Open_SerialPort;
    QPushButton *update_SerialPort;

    void setupUi(QWidget *uiCtrlTFG)
    {
        if (uiCtrlTFG->objectName().isEmpty())
            uiCtrlTFG->setObjectName(QString::fromUtf8("uiCtrlTFG"));
        uiCtrlTFG->resize(1567, 691);
        horizontalLayout_7 = new QHBoxLayout(uiCtrlTFG);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(-1, 0, -1, -1);
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        groupBox = new QGroupBox(uiCtrlTFG);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        horizontalLayout_3 = new QHBoxLayout(groupBox);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        UI_TW_TEMP_CTRL_CT = new QTableWidget(groupBox);
        if (UI_TW_TEMP_CTRL_CT->columnCount() < 3)
            UI_TW_TEMP_CTRL_CT->setColumnCount(3);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        if (UI_TW_TEMP_CTRL_CT->rowCount() < 7)
            UI_TW_TEMP_CTRL_CT->setRowCount(7);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(0, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(1, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(2, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(3, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(4, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(5, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setVerticalHeaderItem(6, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(0, 0, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(0, 1, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(0, 2, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(1, 0, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(1, 1, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(1, 2, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(2, 0, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(2, 1, __qtablewidgetitem17);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(2, 2, __qtablewidgetitem18);
        QTableWidgetItem *__qtablewidgetitem19 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(3, 0, __qtablewidgetitem19);
        QTableWidgetItem *__qtablewidgetitem20 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(3, 1, __qtablewidgetitem20);
        QTableWidgetItem *__qtablewidgetitem21 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(3, 2, __qtablewidgetitem21);
        QTableWidgetItem *__qtablewidgetitem22 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(4, 0, __qtablewidgetitem22);
        QTableWidgetItem *__qtablewidgetitem23 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(4, 1, __qtablewidgetitem23);
        QTableWidgetItem *__qtablewidgetitem24 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(4, 2, __qtablewidgetitem24);
        QTableWidgetItem *__qtablewidgetitem25 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(5, 0, __qtablewidgetitem25);
        QTableWidgetItem *__qtablewidgetitem26 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(5, 1, __qtablewidgetitem26);
        QTableWidgetItem *__qtablewidgetitem27 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(5, 2, __qtablewidgetitem27);
        QTableWidgetItem *__qtablewidgetitem28 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(6, 0, __qtablewidgetitem28);
        QTableWidgetItem *__qtablewidgetitem29 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(6, 1, __qtablewidgetitem29);
        QTableWidgetItem *__qtablewidgetitem30 = new QTableWidgetItem();
        UI_TW_TEMP_CTRL_CT->setItem(6, 2, __qtablewidgetitem30);
        UI_TW_TEMP_CTRL_CT->setObjectName(QString::fromUtf8("UI_TW_TEMP_CTRL_CT"));
        UI_TW_TEMP_CTRL_CT->setMinimumSize(QSize(480, 300));
        UI_TW_TEMP_CTRL_CT->setMaximumSize(QSize(480, 300));

        horizontalLayout_3->addWidget(UI_TW_TEMP_CTRL_CT);

        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        Heat_PW_ON = new QPushButton(groupBox);
        Heat_PW_ON->setObjectName(QString::fromUtf8("Heat_PW_ON"));
        Heat_PW_ON->setMaximumSize(QSize(56, 56));

        verticalLayout_3->addWidget(Heat_PW_ON);

        verticalSpacer_6 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_6);

        label_16 = new QLabel(groupBox);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setAlignment(Qt::AlignCenter);

        verticalLayout_3->addWidget(label_16);

        Heat_Fan_State = new QLabel(groupBox);
        Heat_Fan_State->setObjectName(QString::fromUtf8("Heat_Fan_State"));
        Heat_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));
        Heat_Fan_State->setAlignment(Qt::AlignCenter);

        verticalLayout_3->addWidget(Heat_Fan_State);

        verticalSpacer_7 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_7);

        Heat_ON = new QPushButton(groupBox);
        Heat_ON->setObjectName(QString::fromUtf8("Heat_ON"));
        Heat_ON->setMaximumSize(QSize(56, 56));

        verticalLayout_3->addWidget(Heat_ON);

        verticalSpacer_8 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_8);

        label_18 = new QLabel(groupBox);
        label_18->setObjectName(QString::fromUtf8("label_18"));

        verticalLayout_3->addWidget(label_18);

        HeaterID = new QComboBox(groupBox);
        HeaterID->addItem(QString());
        HeaterID->addItem(QString());
        HeaterID->setObjectName(QString::fromUtf8("HeaterID"));
        HeaterID->setMaximumSize(QSize(56, 16777215));

        verticalLayout_3->addWidget(HeaterID);


        horizontalLayout_3->addLayout(verticalLayout_3);


        horizontalLayout_2->addWidget(groupBox);

        groupBox_2 = new QGroupBox(uiCtrlTFG);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        horizontalLayout_4 = new QHBoxLayout(groupBox_2);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        UI_TW_GAS_CTRL_CT = new QTableWidget(groupBox_2);
        if (UI_TW_GAS_CTRL_CT->columnCount() < 4)
            UI_TW_GAS_CTRL_CT->setColumnCount(4);
        QTableWidgetItem *__qtablewidgetitem31 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setHorizontalHeaderItem(0, __qtablewidgetitem31);
        QTableWidgetItem *__qtablewidgetitem32 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setHorizontalHeaderItem(1, __qtablewidgetitem32);
        QTableWidgetItem *__qtablewidgetitem33 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setHorizontalHeaderItem(2, __qtablewidgetitem33);
        QTableWidgetItem *__qtablewidgetitem34 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setHorizontalHeaderItem(3, __qtablewidgetitem34);
        if (UI_TW_GAS_CTRL_CT->rowCount() < 7)
            UI_TW_GAS_CTRL_CT->setRowCount(7);
        QTableWidgetItem *__qtablewidgetitem35 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(0, __qtablewidgetitem35);
        QTableWidgetItem *__qtablewidgetitem36 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(1, __qtablewidgetitem36);
        QTableWidgetItem *__qtablewidgetitem37 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(2, __qtablewidgetitem37);
        QTableWidgetItem *__qtablewidgetitem38 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(3, __qtablewidgetitem38);
        QTableWidgetItem *__qtablewidgetitem39 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(4, __qtablewidgetitem39);
        QTableWidgetItem *__qtablewidgetitem40 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(5, __qtablewidgetitem40);
        QTableWidgetItem *__qtablewidgetitem41 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setVerticalHeaderItem(6, __qtablewidgetitem41);
        QTableWidgetItem *__qtablewidgetitem42 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(0, 0, __qtablewidgetitem42);
        QTableWidgetItem *__qtablewidgetitem43 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(0, 1, __qtablewidgetitem43);
        QTableWidgetItem *__qtablewidgetitem44 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(0, 2, __qtablewidgetitem44);
        QTableWidgetItem *__qtablewidgetitem45 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(0, 3, __qtablewidgetitem45);
        QTableWidgetItem *__qtablewidgetitem46 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(1, 0, __qtablewidgetitem46);
        QTableWidgetItem *__qtablewidgetitem47 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(1, 1, __qtablewidgetitem47);
        QTableWidgetItem *__qtablewidgetitem48 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(1, 2, __qtablewidgetitem48);
        QTableWidgetItem *__qtablewidgetitem49 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(1, 3, __qtablewidgetitem49);
        QTableWidgetItem *__qtablewidgetitem50 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(2, 0, __qtablewidgetitem50);
        QTableWidgetItem *__qtablewidgetitem51 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(2, 1, __qtablewidgetitem51);
        QTableWidgetItem *__qtablewidgetitem52 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(2, 2, __qtablewidgetitem52);
        QTableWidgetItem *__qtablewidgetitem53 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(2, 3, __qtablewidgetitem53);
        QTableWidgetItem *__qtablewidgetitem54 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(3, 0, __qtablewidgetitem54);
        QTableWidgetItem *__qtablewidgetitem55 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(3, 1, __qtablewidgetitem55);
        QTableWidgetItem *__qtablewidgetitem56 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(3, 2, __qtablewidgetitem56);
        QTableWidgetItem *__qtablewidgetitem57 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(3, 3, __qtablewidgetitem57);
        QTableWidgetItem *__qtablewidgetitem58 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(4, 0, __qtablewidgetitem58);
        QTableWidgetItem *__qtablewidgetitem59 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(4, 1, __qtablewidgetitem59);
        QTableWidgetItem *__qtablewidgetitem60 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(4, 2, __qtablewidgetitem60);
        QTableWidgetItem *__qtablewidgetitem61 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(4, 3, __qtablewidgetitem61);
        QTableWidgetItem *__qtablewidgetitem62 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(5, 0, __qtablewidgetitem62);
        QTableWidgetItem *__qtablewidgetitem63 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(5, 1, __qtablewidgetitem63);
        QTableWidgetItem *__qtablewidgetitem64 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(5, 2, __qtablewidgetitem64);
        QTableWidgetItem *__qtablewidgetitem65 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(5, 3, __qtablewidgetitem65);
        QTableWidgetItem *__qtablewidgetitem66 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(6, 0, __qtablewidgetitem66);
        QTableWidgetItem *__qtablewidgetitem67 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(6, 1, __qtablewidgetitem67);
        QTableWidgetItem *__qtablewidgetitem68 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(6, 2, __qtablewidgetitem68);
        QTableWidgetItem *__qtablewidgetitem69 = new QTableWidgetItem();
        UI_TW_GAS_CTRL_CT->setItem(6, 3, __qtablewidgetitem69);
        UI_TW_GAS_CTRL_CT->setObjectName(QString::fromUtf8("UI_TW_GAS_CTRL_CT"));
        UI_TW_GAS_CTRL_CT->setMinimumSize(QSize(600, 300));
        UI_TW_GAS_CTRL_CT->setMaximumSize(QSize(600, 300));

        horizontalLayout_4->addWidget(UI_TW_GAS_CTRL_CT);

        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        CID_Gas_ON = new QPushButton(groupBox_2);
        CID_Gas_ON->setObjectName(QString::fromUtf8("CID_Gas_ON"));
        CID_Gas_ON->setMinimumSize(QSize(56, 56));
        CID_Gas_ON->setMaximumSize(QSize(56, 56));

        verticalLayout_4->addWidget(CID_Gas_ON);

        CID_Rel_ON = new QPushButton(groupBox_2);
        CID_Rel_ON->setObjectName(QString::fromUtf8("CID_Rel_ON"));
        CID_Rel_ON->setMinimumSize(QSize(56, 56));
        CID_Rel_ON->setMaximumSize(QSize(56, 56));

        verticalLayout_4->addWidget(CID_Rel_ON);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_4->addItem(verticalSpacer);


        horizontalLayout_4->addLayout(verticalLayout_4);


        horizontalLayout_2->addWidget(groupBox_2);


        verticalLayout_2->addLayout(horizontalLayout_2);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        groupBox_6 = new QGroupBox(uiCtrlTFG);
        groupBox_6->setObjectName(QString::fromUtf8("groupBox_6"));
        gridLayout_4 = new QGridLayout(groupBox_6);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        IG0_RF_ON = new QPushButton(groupBox_6);
        IG0_RF_ON->setObjectName(QString::fromUtf8("IG0_RF_ON"));

        gridLayout_4->addWidget(IG0_RF_ON, 0, 1, 1, 1);

        IG12_RF_Tuning = new QLineEdit(groupBox_6);
        IG12_RF_Tuning->setObjectName(QString::fromUtf8("IG12_RF_Tuning"));

        gridLayout_4->addWidget(IG12_RF_Tuning, 1, 2, 1, 1);

        IG0_RF_State = new QLabel(groupBox_6);
        IG0_RF_State->setObjectName(QString::fromUtf8("IG0_RF_State"));
        IG0_RF_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_4->addWidget(IG0_RF_State, 2, 1, 1, 1);

        IG0_RF_Tuning = new QLineEdit(groupBox_6);
        IG0_RF_Tuning->setObjectName(QString::fromUtf8("IG0_RF_Tuning"));

        gridLayout_4->addWidget(IG0_RF_Tuning, 1, 1, 1, 1);

        IG12_RF_State = new QLabel(groupBox_6);
        IG12_RF_State->setObjectName(QString::fromUtf8("IG12_RF_State"));
        IG12_RF_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_4->addWidget(IG12_RF_State, 2, 2, 1, 1);

        IG12_RF_ON = new QPushButton(groupBox_6);
        IG12_RF_ON->setObjectName(QString::fromUtf8("IG12_RF_ON"));

        gridLayout_4->addWidget(IG12_RF_ON, 0, 2, 1, 1);

        IG_Fan_State = new QLabel(groupBox_6);
        IG_Fan_State->setObjectName(QString::fromUtf8("IG_Fan_State"));
        IG_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));
        IG_Fan_State->setAlignment(Qt::AlignCenter);

        gridLayout_4->addWidget(IG_Fan_State, 3, 1, 1, 2);

        label_19 = new QLabel(groupBox_6);
        label_19->setObjectName(QString::fromUtf8("label_19"));

        gridLayout_4->addWidget(label_19, 0, 0, 4, 1);


        horizontalLayout_5->addWidget(groupBox_6);

        groupBox_7 = new QGroupBox(uiCtrlTFG);
        groupBox_7->setObjectName(QString::fromUtf8("groupBox_7"));
        gridLayout_5 = new QGridLayout(groupBox_7);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        CC_RF_ON = new QPushButton(groupBox_7);
        CC_RF_ON->setObjectName(QString::fromUtf8("CC_RF_ON"));

        gridLayout_5->addWidget(CC_RF_ON, 0, 0, 1, 1);

        CC_RF_Tuning = new QLineEdit(groupBox_7);
        CC_RF_Tuning->setObjectName(QString::fromUtf8("CC_RF_Tuning"));

        gridLayout_5->addWidget(CC_RF_Tuning, 1, 0, 1, 1);

        label_25 = new QLabel(groupBox_7);
        label_25->setObjectName(QString::fromUtf8("label_25"));
        label_25->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_5->addWidget(label_25, 2, 0, 1, 1);

        CC_Fan_State = new QLabel(groupBox_7);
        CC_Fan_State->setObjectName(QString::fromUtf8("CC_Fan_State"));
        CC_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_5->addWidget(CC_Fan_State, 3, 0, 1, 1);


        horizontalLayout_5->addWidget(groupBox_7);

        groupBox_9 = new QGroupBox(uiCtrlTFG);
        groupBox_9->setObjectName(QString::fromUtf8("groupBox_9"));
        gridLayout_6 = new QGridLayout(groupBox_9);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        Q1_ABMode = new QPushButton(groupBox_9);
        Q1_ABMode->setObjectName(QString::fromUtf8("Q1_ABMode"));

        gridLayout_6->addWidget(Q1_ABMode, 0, 1, 1, 1);

        Q1_RF_ON = new QPushButton(groupBox_9);
        Q1_RF_ON->setObjectName(QString::fromUtf8("Q1_RF_ON"));

        gridLayout_6->addWidget(Q1_RF_ON, 0, 0, 1, 1);

        Q1_FB_Temp = new QLineEdit(groupBox_9);
        Q1_FB_Temp->setObjectName(QString::fromUtf8("Q1_FB_Temp"));

        gridLayout_6->addWidget(Q1_FB_Temp, 2, 1, 1, 1);

        Q1_RF_Tuning = new QLineEdit(groupBox_9);
        Q1_RF_Tuning->setObjectName(QString::fromUtf8("Q1_RF_Tuning"));

        gridLayout_6->addWidget(Q1_RF_Tuning, 1, 1, 1, 1);

        Q1_Fan_State = new QLabel(groupBox_9);
        Q1_Fan_State->setObjectName(QString::fromUtf8("Q1_Fan_State"));
        Q1_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_6->addWidget(Q1_Fan_State, 3, 1, 1, 1);

        label_28 = new QLabel(groupBox_9);
        label_28->setObjectName(QString::fromUtf8("label_28"));

        gridLayout_6->addWidget(label_28, 1, 0, 1, 1);

        label_29 = new QLabel(groupBox_9);
        label_29->setObjectName(QString::fromUtf8("label_29"));

        gridLayout_6->addWidget(label_29, 2, 0, 1, 1);

        Q1_RF_State = new QLabel(groupBox_9);
        Q1_RF_State->setObjectName(QString::fromUtf8("Q1_RF_State"));
        Q1_RF_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_6->addWidget(Q1_RF_State, 3, 0, 1, 1);


        horizontalLayout_5->addWidget(groupBox_9);

        groupBox_10 = new QGroupBox(uiCtrlTFG);
        groupBox_10->setObjectName(QString::fromUtf8("groupBox_10"));
        gridLayout_8 = new QGridLayout(groupBox_10);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        Q3_ABmode_ctrl = new QPushButton(groupBox_10);
        Q3_ABmode_ctrl->setObjectName(QString::fromUtf8("Q3_ABmode_ctrl"));

        gridLayout_8->addWidget(Q3_ABmode_ctrl, 0, 1, 1, 1);

        label_31 = new QLabel(groupBox_10);
        label_31->setObjectName(QString::fromUtf8("label_31"));

        gridLayout_8->addWidget(label_31, 1, 0, 1, 1);

        Q3_FB_Temp = new QLineEdit(groupBox_10);
        Q3_FB_Temp->setObjectName(QString::fromUtf8("Q3_FB_Temp"));

        gridLayout_8->addWidget(Q3_FB_Temp, 2, 1, 1, 1);

        label_32 = new QLabel(groupBox_10);
        label_32->setObjectName(QString::fromUtf8("label_32"));

        gridLayout_8->addWidget(label_32, 2, 0, 1, 1);

        Q3_RF_on_ctrl = new QPushButton(groupBox_10);
        Q3_RF_on_ctrl->setObjectName(QString::fromUtf8("Q3_RF_on_ctrl"));

        gridLayout_8->addWidget(Q3_RF_on_ctrl, 0, 0, 1, 1);

        Q3_RF_Tuning = new QLineEdit(groupBox_10);
        Q3_RF_Tuning->setObjectName(QString::fromUtf8("Q3_RF_Tuning"));

        gridLayout_8->addWidget(Q3_RF_Tuning, 1, 1, 1, 1);

        Q3_Fan_State = new QLabel(groupBox_10);
        Q3_Fan_State->setObjectName(QString::fromUtf8("Q3_Fan_State"));
        Q3_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_8->addWidget(Q3_Fan_State, 3, 1, 1, 1);

        Q3_RF_State = new QLabel(groupBox_10);
        Q3_RF_State->setObjectName(QString::fromUtf8("Q3_RF_State"));
        Q3_RF_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_8->addWidget(Q3_RF_State, 3, 0, 1, 1);


        horizontalLayout_5->addWidget(groupBox_10);

        groupBox_8 = new QGroupBox(uiCtrlTFG);
        groupBox_8->setObjectName(QString::fromUtf8("groupBox_8"));
        gridLayout_7 = new QGridLayout(groupBox_8);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        IS_ID = new QLineEdit(groupBox_8);
        IS_ID->setObjectName(QString::fromUtf8("IS_ID"));

        gridLayout_7->addWidget(IS_ID, 2, 0, 1, 1);

        label_21 = new QLabel(groupBox_8);
        label_21->setObjectName(QString::fromUtf8("label_21"));

        gridLayout_7->addWidget(label_21, 1, 0, 1, 1);

        IS_Door = new QLabel(groupBox_8);
        IS_Door->setObjectName(QString::fromUtf8("IS_Door"));
        IS_Door->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));
        IS_Door->setAlignment(Qt::AlignCenter);

        gridLayout_7->addWidget(IS_Door, 0, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_7->addItem(verticalSpacer_2, 3, 0, 1, 1);


        horizontalLayout_5->addWidget(groupBox_8);


        verticalLayout_2->addLayout(horizontalLayout_5);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(-1, -1, 0, -1);
        groupBox_11 = new QGroupBox(uiCtrlTFG);
        groupBox_11->setObjectName(QString::fromUtf8("groupBox_11"));
        gridLayout_9 = new QGridLayout(groupBox_11);
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        Lens_DC3 = new QLineEdit(groupBox_11);
        Lens_DC3->setObjectName(QString::fromUtf8("Lens_DC3"));

        gridLayout_9->addWidget(Lens_DC3, 1, 2, 1, 1);

        label_40 = new QLabel(groupBox_11);
        label_40->setObjectName(QString::fromUtf8("label_40"));

        gridLayout_9->addWidget(label_40, 0, 5, 1, 1);

        Lens_DC1 = new QLineEdit(groupBox_11);
        Lens_DC1->setObjectName(QString::fromUtf8("Lens_DC1"));

        gridLayout_9->addWidget(Lens_DC1, 1, 0, 1, 1);

        label_45 = new QLabel(groupBox_11);
        label_45->setObjectName(QString::fromUtf8("label_45"));

        gridLayout_9->addWidget(label_45, 2, 2, 1, 1);

        label_38 = new QLabel(groupBox_11);
        label_38->setObjectName(QString::fromUtf8("label_38"));

        gridLayout_9->addWidget(label_38, 0, 3, 1, 1);

        label_48 = new QLabel(groupBox_11);
        label_48->setObjectName(QString::fromUtf8("label_48"));

        gridLayout_9->addWidget(label_48, 2, 5, 1, 1);

        label_50 = new QLabel(groupBox_11);
        label_50->setObjectName(QString::fromUtf8("label_50"));

        gridLayout_9->addWidget(label_50, 2, 7, 1, 1);

        label_41 = new QLabel(groupBox_11);
        label_41->setObjectName(QString::fromUtf8("label_41"));

        gridLayout_9->addWidget(label_41, 0, 6, 1, 1);

        label_39 = new QLabel(groupBox_11);
        label_39->setObjectName(QString::fromUtf8("label_39"));

        gridLayout_9->addWidget(label_39, 0, 4, 1, 1);

        label_35 = new QLabel(groupBox_11);
        label_35->setObjectName(QString::fromUtf8("label_35"));

        gridLayout_9->addWidget(label_35, 0, 0, 1, 1);

        Lens_DC5 = new QLineEdit(groupBox_11);
        Lens_DC5->setObjectName(QString::fromUtf8("Lens_DC5"));

        gridLayout_9->addWidget(Lens_DC5, 1, 4, 1, 1);

        Lens_DC4 = new QLineEdit(groupBox_11);
        Lens_DC4->setObjectName(QString::fromUtf8("Lens_DC4"));

        gridLayout_9->addWidget(Lens_DC4, 1, 3, 1, 1);

        label_44 = new QLabel(groupBox_11);
        label_44->setObjectName(QString::fromUtf8("label_44"));

        gridLayout_9->addWidget(label_44, 2, 1, 1, 1);

        Lens_DC8 = new QLineEdit(groupBox_11);
        Lens_DC8->setObjectName(QString::fromUtf8("Lens_DC8"));

        gridLayout_9->addWidget(Lens_DC8, 1, 7, 1, 1);

        label_49 = new QLabel(groupBox_11);
        label_49->setObjectName(QString::fromUtf8("label_49"));

        gridLayout_9->addWidget(label_49, 2, 6, 1, 1);

        Lens_DC6 = new QLineEdit(groupBox_11);
        Lens_DC6->setObjectName(QString::fromUtf8("Lens_DC6"));

        gridLayout_9->addWidget(Lens_DC6, 1, 5, 1, 1);

        label_47 = new QLabel(groupBox_11);
        label_47->setObjectName(QString::fromUtf8("label_47"));

        gridLayout_9->addWidget(label_47, 2, 4, 1, 1);

        label_43 = new QLabel(groupBox_11);
        label_43->setObjectName(QString::fromUtf8("label_43"));

        gridLayout_9->addWidget(label_43, 2, 0, 1, 1);

        Lens_DC7 = new QLineEdit(groupBox_11);
        Lens_DC7->setObjectName(QString::fromUtf8("Lens_DC7"));

        gridLayout_9->addWidget(Lens_DC7, 1, 6, 1, 1);

        Lens_DC2 = new QLineEdit(groupBox_11);
        Lens_DC2->setObjectName(QString::fromUtf8("Lens_DC2"));

        gridLayout_9->addWidget(Lens_DC2, 1, 1, 1, 1);

        label_36 = new QLabel(groupBox_11);
        label_36->setObjectName(QString::fromUtf8("label_36"));

        gridLayout_9->addWidget(label_36, 0, 1, 1, 1);

        label_42 = new QLabel(groupBox_11);
        label_42->setObjectName(QString::fromUtf8("label_42"));

        gridLayout_9->addWidget(label_42, 0, 7, 1, 1);

        label_46 = new QLabel(groupBox_11);
        label_46->setObjectName(QString::fromUtf8("label_46"));

        gridLayout_9->addWidget(label_46, 2, 3, 1, 1);

        label_37 = new QLabel(groupBox_11);
        label_37->setObjectName(QString::fromUtf8("label_37"));

        gridLayout_9->addWidget(label_37, 0, 2, 1, 1);

        Lens_DC9 = new QLineEdit(groupBox_11);
        Lens_DC9->setObjectName(QString::fromUtf8("Lens_DC9"));

        gridLayout_9->addWidget(Lens_DC9, 3, 0, 1, 1);

        Lens_DC10 = new QLineEdit(groupBox_11);
        Lens_DC10->setObjectName(QString::fromUtf8("Lens_DC10"));

        gridLayout_9->addWidget(Lens_DC10, 3, 1, 1, 1);

        Lens_DC11 = new QLineEdit(groupBox_11);
        Lens_DC11->setObjectName(QString::fromUtf8("Lens_DC11"));

        gridLayout_9->addWidget(Lens_DC11, 3, 2, 1, 1);

        Lens_DC12 = new QLineEdit(groupBox_11);
        Lens_DC12->setObjectName(QString::fromUtf8("Lens_DC12"));

        gridLayout_9->addWidget(Lens_DC12, 3, 3, 1, 1);

        Lens_DC13 = new QLineEdit(groupBox_11);
        Lens_DC13->setObjectName(QString::fromUtf8("Lens_DC13"));

        gridLayout_9->addWidget(Lens_DC13, 3, 4, 1, 1);

        Lens_DC14 = new QLineEdit(groupBox_11);
        Lens_DC14->setObjectName(QString::fromUtf8("Lens_DC14"));

        gridLayout_9->addWidget(Lens_DC14, 3, 5, 1, 1);

        Lens_DC15 = new QLineEdit(groupBox_11);
        Lens_DC15->setObjectName(QString::fromUtf8("Lens_DC15"));

        gridLayout_9->addWidget(Lens_DC15, 3, 6, 1, 1);

        Lens_DC16 = new QLineEdit(groupBox_11);
        Lens_DC16->setObjectName(QString::fromUtf8("Lens_DC16"));

        gridLayout_9->addWidget(Lens_DC16, 3, 7, 1, 1);


        horizontalLayout_6->addWidget(groupBox_11);

        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        Monitor_ON = new QPushButton(uiCtrlTFG);
        Monitor_ON->setObjectName(QString::fromUtf8("Monitor_ON"));
        Monitor_ON->setMinimumSize(QSize(132, 56));
        Monitor_ON->setStyleSheet(QString::fromUtf8("QPushButton { background-color: LightGray; }"));

        verticalLayout_5->addWidget(Monitor_ON);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_5->addItem(verticalSpacer_4);

        Scan_Ctrl = new QPushButton(uiCtrlTFG);
        Scan_Ctrl->setObjectName(QString::fromUtf8("Scan_Ctrl"));
        Scan_Ctrl->setMinimumSize(QSize(132, 56));

        verticalLayout_5->addWidget(Scan_Ctrl);

        verticalSpacer_5 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_5->addItem(verticalSpacer_5);


        horizontalLayout_6->addLayout(verticalLayout_5);

        verticalLayout_6 = new QVBoxLayout();
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        COMLen = new QLineEdit(uiCtrlTFG);
        COMLen->setObjectName(QString::fromUtf8("COMLen"));

        verticalLayout_6->addWidget(COMLen);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_6->addItem(verticalSpacer_3);


        horizontalLayout_6->addLayout(verticalLayout_6);


        verticalLayout_2->addLayout(horizontalLayout_6);


        horizontalLayout_7->addLayout(verticalLayout_2);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        groupBox_3 = new QGroupBox(uiCtrlTFG);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        gridLayout_3 = new QGridLayout(groupBox_3);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        Auto_StartUp = new QPushButton(groupBox_3);
        Auto_StartUp->setObjectName(QString::fromUtf8("Auto_StartUp"));

        gridLayout_3->addWidget(Auto_StartUp, 0, 0, 1, 1);

        Rpump_ON = new QPushButton(groupBox_3);
        Rpump_ON->setObjectName(QString::fromUtf8("Rpump_ON"));

        gridLayout_3->addWidget(Rpump_ON, 1, 0, 1, 1);

        label_13 = new QLabel(groupBox_3);
        label_13->setObjectName(QString::fromUtf8("label_13"));

        gridLayout_3->addWidget(label_13, 4, 0, 1, 1);

        IG_ON = new QPushButton(groupBox_3);
        IG_ON->setObjectName(QString::fromUtf8("IG_ON"));

        gridLayout_3->addWidget(IG_ON, 2, 0, 1, 1);

        Auto_ShutDown = new QPushButton(groupBox_3);
        Auto_ShutDown->setObjectName(QString::fromUtf8("Auto_ShutDown"));

        gridLayout_3->addWidget(Auto_ShutDown, 0, 1, 1, 1);

        PG_P = new QLineEdit(groupBox_3);
        PG_P->setObjectName(QString::fromUtf8("PG_P"));

        gridLayout_3->addWidget(PG_P, 4, 1, 1, 1);

        Tpump_ON = new QPushButton(groupBox_3);
        Tpump_ON->setObjectName(QString::fromUtf8("Tpump_ON"));

        gridLayout_3->addWidget(Tpump_ON, 1, 1, 1, 1);

        Tpump_Frq = new QLineEdit(groupBox_3);
        Tpump_Frq->setObjectName(QString::fromUtf8("Tpump_Frq"));
        Tpump_Frq->setStyleSheet(QString::fromUtf8("QLineEdit { background-color: LightGray; }"));

        gridLayout_3->addWidget(Tpump_Frq, 3, 1, 1, 1);

        Tpump_Fan_State = new QLabel(groupBox_3);
        Tpump_Fan_State->setObjectName(QString::fromUtf8("Tpump_Fan_State"));
        Tpump_Fan_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_3->addWidget(Tpump_Fan_State, 6, 0, 1, 2);

        label_12 = new QLabel(groupBox_3);
        label_12->setObjectName(QString::fromUtf8("label_12"));

        gridLayout_3->addWidget(label_12, 3, 0, 1, 1);

        IG_P = new QLineEdit(groupBox_3);
        IG_P->setObjectName(QString::fromUtf8("IG_P"));

        gridLayout_3->addWidget(IG_P, 5, 1, 1, 1);

        Purge_Gas_ON = new QPushButton(groupBox_3);
        Purge_Gas_ON->setObjectName(QString::fromUtf8("Purge_Gas_ON"));

        gridLayout_3->addWidget(Purge_Gas_ON, 2, 1, 1, 1);

        label_14 = new QLabel(groupBox_3);
        label_14->setObjectName(QString::fromUtf8("label_14"));

        gridLayout_3->addWidget(label_14, 5, 0, 1, 1);

        Rpump_Frq = new QLineEdit(groupBox_3);
        Rpump_Frq->setObjectName(QString::fromUtf8("Rpump_Frq"));

        gridLayout_3->addWidget(Rpump_Frq, 4, 2, 1, 1);


        verticalLayout->addWidget(groupBox_3);

        groupBox_4 = new QGroupBox(uiCtrlTFG);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        gridLayout_2 = new QGridLayout(groupBox_4);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        label_5 = new QLabel(groupBox_4);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        gridLayout_2->addWidget(label_5, 2, 3, 1, 1);

        label_6 = new QLabel(groupBox_4);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        gridLayout_2->addWidget(label_6, 3, 3, 1, 1);

        DET_HV_State = new QLabel(groupBox_4);
        DET_HV_State->setObjectName(QString::fromUtf8("DET_HV_State"));
        DET_HV_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_2->addWidget(DET_HV_State, 4, 1, 1, 1);

        label_7 = new QLabel(groupBox_4);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        gridLayout_2->addWidget(label_7, 4, 3, 1, 1);

        label_3 = new QLabel(groupBox_4);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        gridLayout_2->addWidget(label_3, 0, 3, 1, 1);

        ESI_HV_IFB = new QLineEdit(groupBox_4);
        ESI_HV_IFB->setObjectName(QString::fromUtf8("ESI_HV_IFB"));

        gridLayout_2->addWidget(ESI_HV_IFB, 1, 2, 1, 1);

        CUR_HV_State = new QLabel(groupBox_4);
        CUR_HV_State->setObjectName(QString::fromUtf8("CUR_HV_State"));
        CUR_HV_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_2->addWidget(CUR_HV_State, 2, 1, 1, 1);

        label_4 = new QLabel(groupBox_4);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        gridLayout_2->addWidget(label_4, 1, 3, 1, 1);

        ESI_HV_VFB = new QLineEdit(groupBox_4);
        ESI_HV_VFB->setObjectName(QString::fromUtf8("ESI_HV_VFB"));

        gridLayout_2->addWidget(ESI_HV_VFB, 0, 2, 1, 1);

        Curtain_HV_VFB = new QLineEdit(groupBox_4);
        Curtain_HV_VFB->setObjectName(QString::fromUtf8("Curtain_HV_VFB"));

        gridLayout_2->addWidget(Curtain_HV_VFB, 2, 2, 1, 1);

        HED_HV_VFB = new QLineEdit(groupBox_4);
        HED_HV_VFB->setObjectName(QString::fromUtf8("HED_HV_VFB"));

        gridLayout_2->addWidget(HED_HV_VFB, 3, 2, 1, 1);

        HED_HV_State = new QLabel(groupBox_4);
        HED_HV_State->setObjectName(QString::fromUtf8("HED_HV_State"));
        HED_HV_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_2->addWidget(HED_HV_State, 3, 1, 1, 1);

        DET_HV_VFB = new QLineEdit(groupBox_4);
        DET_HV_VFB->setObjectName(QString::fromUtf8("DET_HV_VFB"));

        gridLayout_2->addWidget(DET_HV_VFB, 4, 2, 1, 1);

        CUR_HV_ON = new QPushButton(groupBox_4);
        CUR_HV_ON->setObjectName(QString::fromUtf8("CUR_HV_ON"));

        gridLayout_2->addWidget(CUR_HV_ON, 2, 0, 1, 1);

        HED_HV_ON = new QPushButton(groupBox_4);
        HED_HV_ON->setObjectName(QString::fromUtf8("HED_HV_ON"));

        gridLayout_2->addWidget(HED_HV_ON, 3, 0, 1, 1);

        DET_HV_ON = new QPushButton(groupBox_4);
        DET_HV_ON->setObjectName(QString::fromUtf8("DET_HV_ON"));

        gridLayout_2->addWidget(DET_HV_ON, 4, 0, 1, 1);

        ESI_HV_ON = new QPushButton(groupBox_4);
        ESI_HV_ON->setObjectName(QString::fromUtf8("ESI_HV_ON"));

        gridLayout_2->addWidget(ESI_HV_ON, 0, 0, 2, 1);

        ESI_HV_State = new QLabel(groupBox_4);
        ESI_HV_State->setObjectName(QString::fromUtf8("ESI_HV_State"));
        ESI_HV_State->setStyleSheet(QString::fromUtf8("QLabel { background-color: LightCoral; }"));

        gridLayout_2->addWidget(ESI_HV_State, 0, 1, 2, 1);


        verticalLayout->addWidget(groupBox_4);

        groupBox_5 = new QGroupBox(uiCtrlTFG);
        groupBox_5->setObjectName(QString::fromUtf8("groupBox_5"));
        gridLayout = new QGridLayout(groupBox_5);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        label_2 = new QLabel(groupBox_5);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        gridLayout->addWidget(label_2, 1, 0, 1, 1);

        comboBox2 = new QComboBox(groupBox_5);
        comboBox2->setObjectName(QString::fromUtf8("comboBox2"));

        gridLayout->addWidget(comboBox2, 1, 1, 1, 1);

        comboBox1 = new QComboBox(groupBox_5);
        comboBox1->setObjectName(QString::fromUtf8("comboBox1"));

        gridLayout->addWidget(comboBox1, 0, 1, 1, 1);

        label = new QLabel(groupBox_5);
        label->setObjectName(QString::fromUtf8("label"));

        gridLayout->addWidget(label, 0, 0, 1, 1);

        Open_SerialPort = new QPushButton(groupBox_5);
        Open_SerialPort->setObjectName(QString::fromUtf8("Open_SerialPort"));
        Open_SerialPort->setMinimumSize(QSize(56, 56));
        Open_SerialPort->setMaximumSize(QSize(56, 16777215));

        gridLayout->addWidget(Open_SerialPort, 1, 2, 1, 1);

        update_SerialPort = new QPushButton(groupBox_5);
        update_SerialPort->setObjectName(QString::fromUtf8("update_SerialPort"));
        update_SerialPort->setMaximumSize(QSize(56, 16777215));

        gridLayout->addWidget(update_SerialPort, 0, 2, 1, 1);


        verticalLayout->addWidget(groupBox_5);


        horizontalLayout_7->addLayout(verticalLayout);


        retranslateUi(uiCtrlTFG);

        QMetaObject::connectSlotsByName(uiCtrlTFG);
    } // setupUi

    void retranslateUi(QWidget *uiCtrlTFG)
    {
        uiCtrlTFG->setWindowTitle(QApplication::translate("uiCtrlTFG", "Form", nullptr));
        groupBox->setTitle(QApplication::translate("uiCtrlTFG", "\346\270\251\345\272\246\346\216\247\345\210\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem = UI_TW_TEMP_CTRL_CT->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("uiCtrlTFG", "ESI", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = UI_TW_TEMP_CTRL_CT->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("uiCtrlTFG", "APCI", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = UI_TW_TEMP_CTRL_CT->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("uiCtrlTFG", "Cutain", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(0);
        ___qtablewidgetitem3->setText(QApplication::translate("uiCtrlTFG", "\350\256\276\345\256\232\346\270\251\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(1);
        ___qtablewidgetitem4->setText(QApplication::translate("uiCtrlTFG", "\345\256\236\351\231\205\346\270\251\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(2);
        ___qtablewidgetitem5->setText(QApplication::translate("uiCtrlTFG", "\345\256\236\351\231\205\346\270\251\345\272\246D", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(3);
        ___qtablewidgetitem6->setText(QApplication::translate("uiCtrlTFG", "P\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(4);
        ___qtablewidgetitem7->setText(QApplication::translate("uiCtrlTFG", "I\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(5);
        ___qtablewidgetitem8->setText(QApplication::translate("uiCtrlTFG", "D\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = UI_TW_TEMP_CTRL_CT->verticalHeaderItem(6);
        ___qtablewidgetitem9->setText(QApplication::translate("uiCtrlTFG", "PWM", nullptr));

        const bool __sortingEnabled = UI_TW_TEMP_CTRL_CT->isSortingEnabled();
        UI_TW_TEMP_CTRL_CT->setSortingEnabled(false);
        QTableWidgetItem *___qtablewidgetitem10 = UI_TW_TEMP_CTRL_CT->item(0, 0);
        ___qtablewidgetitem10->setText(QApplication::translate("uiCtrlTFG", "25", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = UI_TW_TEMP_CTRL_CT->item(0, 1);
        ___qtablewidgetitem11->setText(QApplication::translate("uiCtrlTFG", "25", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = UI_TW_TEMP_CTRL_CT->item(0, 2);
        ___qtablewidgetitem12->setText(QApplication::translate("uiCtrlTFG", "25", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = UI_TW_TEMP_CTRL_CT->item(1, 0);
        ___qtablewidgetitem13->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = UI_TW_TEMP_CTRL_CT->item(1, 1);
        ___qtablewidgetitem14->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem15 = UI_TW_TEMP_CTRL_CT->item(1, 2);
        ___qtablewidgetitem15->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = UI_TW_TEMP_CTRL_CT->item(2, 0);
        ___qtablewidgetitem16->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem17 = UI_TW_TEMP_CTRL_CT->item(2, 1);
        ___qtablewidgetitem17->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem18 = UI_TW_TEMP_CTRL_CT->item(2, 2);
        ___qtablewidgetitem18->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem19 = UI_TW_TEMP_CTRL_CT->item(3, 0);
        ___qtablewidgetitem19->setText(QApplication::translate("uiCtrlTFG", "25", nullptr));
        QTableWidgetItem *___qtablewidgetitem20 = UI_TW_TEMP_CTRL_CT->item(3, 1);
        ___qtablewidgetitem20->setText(QApplication::translate("uiCtrlTFG", "50", nullptr));
        QTableWidgetItem *___qtablewidgetitem21 = UI_TW_TEMP_CTRL_CT->item(3, 2);
        ___qtablewidgetitem21->setText(QApplication::translate("uiCtrlTFG", "50", nullptr));
        QTableWidgetItem *___qtablewidgetitem22 = UI_TW_TEMP_CTRL_CT->item(4, 0);
        ___qtablewidgetitem22->setText(QApplication::translate("uiCtrlTFG", "15", nullptr));
        QTableWidgetItem *___qtablewidgetitem23 = UI_TW_TEMP_CTRL_CT->item(4, 1);
        ___qtablewidgetitem23->setText(QApplication::translate("uiCtrlTFG", "20", nullptr));
        QTableWidgetItem *___qtablewidgetitem24 = UI_TW_TEMP_CTRL_CT->item(4, 2);
        ___qtablewidgetitem24->setText(QApplication::translate("uiCtrlTFG", "20", nullptr));
        QTableWidgetItem *___qtablewidgetitem25 = UI_TW_TEMP_CTRL_CT->item(5, 0);
        ___qtablewidgetitem25->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem26 = UI_TW_TEMP_CTRL_CT->item(5, 1);
        ___qtablewidgetitem26->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem27 = UI_TW_TEMP_CTRL_CT->item(5, 2);
        ___qtablewidgetitem27->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem28 = UI_TW_TEMP_CTRL_CT->item(6, 0);
        ___qtablewidgetitem28->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem29 = UI_TW_TEMP_CTRL_CT->item(6, 1);
        ___qtablewidgetitem29->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem30 = UI_TW_TEMP_CTRL_CT->item(6, 2);
        ___qtablewidgetitem30->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        UI_TW_TEMP_CTRL_CT->setSortingEnabled(__sortingEnabled);

        Heat_PW_ON->setText(QApplication::translate("uiCtrlTFG", "\345\212\240\347\203\255\n"
"\347\224\265\346\272\220", nullptr));
        label_16->setText(QApplication::translate("uiCtrlTFG", "\351\243\216\346\211\207", nullptr));
        Heat_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\347\212\266\346\200\201", nullptr));
        Heat_ON->setText(QApplication::translate("uiCtrlTFG", "\345\212\240\347\203\255\n"
"\345\274\200", nullptr));
        label_18->setText(QApplication::translate("uiCtrlTFG", "\345\212\240\347\203\255\347\256\241", nullptr));
        HeaterID->setItemText(0, QApplication::translate("uiCtrlTFG", "\345\260\217", nullptr));
        HeaterID->setItemText(1, QApplication::translate("uiCtrlTFG", "\345\244\247", nullptr));

        groupBox_2->setTitle(QApplication::translate("uiCtrlTFG", "\346\260\224\346\265\201\346\216\247\345\210\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem31 = UI_TW_GAS_CTRL_CT->horizontalHeaderItem(0);
        ___qtablewidgetitem31->setText(QApplication::translate("uiCtrlTFG", "\345\212\240\347\203\255\346\260\224", nullptr));
        QTableWidgetItem *___qtablewidgetitem32 = UI_TW_GAS_CTRL_CT->horizontalHeaderItem(1);
        ___qtablewidgetitem32->setText(QApplication::translate("uiCtrlTFG", "\351\233\276\345\214\226\346\260\224", nullptr));
        QTableWidgetItem *___qtablewidgetitem33 = UI_TW_GAS_CTRL_CT->horizontalHeaderItem(2);
        ___qtablewidgetitem33->setText(QApplication::translate("uiCtrlTFG", "CurtainHV", nullptr));
        QTableWidgetItem *___qtablewidgetitem34 = UI_TW_GAS_CTRL_CT->horizontalHeaderItem(3);
        ___qtablewidgetitem34->setText(QApplication::translate("uiCtrlTFG", "HV_on", nullptr));
        QTableWidgetItem *___qtablewidgetitem35 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(0);
        ___qtablewidgetitem35->setText(QApplication::translate("uiCtrlTFG", "\350\256\276\345\256\232\346\265\201\351\207\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem36 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(1);
        ___qtablewidgetitem36->setText(QApplication::translate("uiCtrlTFG", "\345\256\236\351\231\205\346\265\201\351\207\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem37 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(2);
        ___qtablewidgetitem37->setText(QApplication::translate("uiCtrlTFG", "\345\256\236\351\231\205\346\265\201\351\207\217D", nullptr));
        QTableWidgetItem *___qtablewidgetitem38 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(3);
        ___qtablewidgetitem38->setText(QApplication::translate("uiCtrlTFG", "P\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem39 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(4);
        ___qtablewidgetitem39->setText(QApplication::translate("uiCtrlTFG", "I\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem40 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(5);
        ___qtablewidgetitem40->setText(QApplication::translate("uiCtrlTFG", "D\345\217\202\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem41 = UI_TW_GAS_CTRL_CT->verticalHeaderItem(6);
        ___qtablewidgetitem41->setText(QApplication::translate("uiCtrlTFG", "PID", nullptr));

        const bool __sortingEnabled1 = UI_TW_GAS_CTRL_CT->isSortingEnabled();
        UI_TW_GAS_CTRL_CT->setSortingEnabled(false);
        QTableWidgetItem *___qtablewidgetitem42 = UI_TW_GAS_CTRL_CT->item(0, 0);
        ___qtablewidgetitem42->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem43 = UI_TW_GAS_CTRL_CT->item(0, 1);
        ___qtablewidgetitem43->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem44 = UI_TW_GAS_CTRL_CT->item(0, 2);
        ___qtablewidgetitem44->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem45 = UI_TW_GAS_CTRL_CT->item(0, 3);
        ___qtablewidgetitem45->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem46 = UI_TW_GAS_CTRL_CT->item(1, 0);
        ___qtablewidgetitem46->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem47 = UI_TW_GAS_CTRL_CT->item(1, 1);
        ___qtablewidgetitem47->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem48 = UI_TW_GAS_CTRL_CT->item(1, 2);
        ___qtablewidgetitem48->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem49 = UI_TW_GAS_CTRL_CT->item(1, 3);
        ___qtablewidgetitem49->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem50 = UI_TW_GAS_CTRL_CT->item(2, 0);
        ___qtablewidgetitem50->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem51 = UI_TW_GAS_CTRL_CT->item(2, 1);
        ___qtablewidgetitem51->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem52 = UI_TW_GAS_CTRL_CT->item(2, 2);
        ___qtablewidgetitem52->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem53 = UI_TW_GAS_CTRL_CT->item(2, 3);
        ___qtablewidgetitem53->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem54 = UI_TW_GAS_CTRL_CT->item(3, 0);
        ___qtablewidgetitem54->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem55 = UI_TW_GAS_CTRL_CT->item(3, 1);
        ___qtablewidgetitem55->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem56 = UI_TW_GAS_CTRL_CT->item(3, 2);
        ___qtablewidgetitem56->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem57 = UI_TW_GAS_CTRL_CT->item(3, 3);
        ___qtablewidgetitem57->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem58 = UI_TW_GAS_CTRL_CT->item(4, 0);
        ___qtablewidgetitem58->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem59 = UI_TW_GAS_CTRL_CT->item(4, 1);
        ___qtablewidgetitem59->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem60 = UI_TW_GAS_CTRL_CT->item(4, 2);
        ___qtablewidgetitem60->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem61 = UI_TW_GAS_CTRL_CT->item(4, 3);
        ___qtablewidgetitem61->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem62 = UI_TW_GAS_CTRL_CT->item(5, 0);
        ___qtablewidgetitem62->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem63 = UI_TW_GAS_CTRL_CT->item(5, 1);
        ___qtablewidgetitem63->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem64 = UI_TW_GAS_CTRL_CT->item(5, 2);
        ___qtablewidgetitem64->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem65 = UI_TW_GAS_CTRL_CT->item(5, 3);
        ___qtablewidgetitem65->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem66 = UI_TW_GAS_CTRL_CT->item(6, 0);
        ___qtablewidgetitem66->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem67 = UI_TW_GAS_CTRL_CT->item(6, 1);
        ___qtablewidgetitem67->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem68 = UI_TW_GAS_CTRL_CT->item(6, 2);
        ___qtablewidgetitem68->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem69 = UI_TW_GAS_CTRL_CT->item(6, 3);
        ___qtablewidgetitem69->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        UI_TW_GAS_CTRL_CT->setSortingEnabled(__sortingEnabled1);

        CID_Gas_ON->setText(QApplication::translate("uiCtrlTFG", "CID\n"
"ON", nullptr));
        CID_Rel_ON->setText(QApplication::translate("uiCtrlTFG", "CID\n"
"Rel", nullptr));
        groupBox_6->setTitle(QApplication::translate("uiCtrlTFG", "IG_RF\346\216\247\345\210\266", nullptr));
        IG0_RF_ON->setText(QApplication::translate("uiCtrlTFG", "IG0_ON", nullptr));
        IG12_RF_Tuning->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        IG0_RF_State->setText(QApplication::translate("uiCtrlTFG", "RF\347\212\266\346\200\201", nullptr));
        IG0_RF_Tuning->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        IG12_RF_State->setText(QApplication::translate("uiCtrlTFG", "RF\347\212\266\346\200\201", nullptr));
        IG12_RF_ON->setText(QApplication::translate("uiCtrlTFG", "IG12_ON", nullptr));
        IG_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\351\243\216\346\211\207\347\212\266\346\200\201", nullptr));
        label_19->setText(QApplication::translate("uiCtrlTFG", "Tuning", nullptr));
        groupBox_7->setTitle(QApplication::translate("uiCtrlTFG", "CC_RF\346\216\247\345\210\266", nullptr));
        CC_RF_ON->setText(QApplication::translate("uiCtrlTFG", "CC_ON", nullptr));
        CC_RF_Tuning->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_25->setText(QApplication::translate("uiCtrlTFG", "RF\347\212\266\346\200\201", nullptr));
        CC_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\351\243\216\346\211\207\347\212\266\346\200\201", nullptr));
        groupBox_9->setTitle(QApplication::translate("uiCtrlTFG", "Q1_RF\346\216\247\345\210\266", nullptr));
        Q1_ABMode->setText(QApplication::translate("uiCtrlTFG", "A\346\250\241\345\274\217", nullptr));
        Q1_RF_ON->setText(QApplication::translate("uiCtrlTFG", "Q1_ON", nullptr));
        Q1_FB_Temp->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Q1_RF_Tuning->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Q1_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\351\243\216\346\211\207\347\212\266\346\200\201", nullptr));
        label_28->setText(QApplication::translate("uiCtrlTFG", "Tuning", nullptr));
        label_29->setText(QApplication::translate("uiCtrlTFG", "\345\217\215\351\246\210\346\270\251\345\272\246", nullptr));
        Q1_RF_State->setText(QApplication::translate("uiCtrlTFG", "RF\347\212\266\346\200\201", nullptr));
        groupBox_10->setTitle(QApplication::translate("uiCtrlTFG", "Q3_RF\346\216\247\345\210\266", nullptr));
        Q3_ABmode_ctrl->setText(QApplication::translate("uiCtrlTFG", "A\346\250\241\345\274\217", nullptr));
        label_31->setText(QApplication::translate("uiCtrlTFG", "Tuning", nullptr));
        Q3_FB_Temp->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_32->setText(QApplication::translate("uiCtrlTFG", "\345\217\215\351\246\210\346\270\251\345\272\246", nullptr));
        Q3_RF_on_ctrl->setText(QApplication::translate("uiCtrlTFG", "Q3_ON", nullptr));
        Q3_RF_Tuning->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Q3_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\351\243\216\346\211\207\347\212\266\346\200\201", nullptr));
        Q3_RF_State->setText(QApplication::translate("uiCtrlTFG", "RF\347\212\266\346\200\201", nullptr));
        groupBox_8->setTitle(QApplication::translate("uiCtrlTFG", "\347\246\273\345\255\220\346\272\220", nullptr));
        IS_ID->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_21->setText(QApplication::translate("uiCtrlTFG", "\347\246\273\345\255\220\346\272\220", nullptr));
        IS_Door->setText(QApplication::translate("uiCtrlTFG", "\347\246\273\345\255\220\351\227\250", nullptr));
        groupBox_11->setTitle(QApplication::translate("uiCtrlTFG", "Lens_DC\347\233\221\346\216\247", nullptr));
        Lens_DC3->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_40->setText(QApplication::translate("uiCtrlTFG", "DC6", nullptr));
        Lens_DC1->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_45->setText(QApplication::translate("uiCtrlTFG", "DC11", nullptr));
        label_38->setText(QApplication::translate("uiCtrlTFG", "DC4", nullptr));
        label_48->setText(QApplication::translate("uiCtrlTFG", "DC14", nullptr));
        label_50->setText(QApplication::translate("uiCtrlTFG", "DC16", nullptr));
        label_41->setText(QApplication::translate("uiCtrlTFG", "DC7", nullptr));
        label_39->setText(QApplication::translate("uiCtrlTFG", "DC5", nullptr));
        label_35->setText(QApplication::translate("uiCtrlTFG", "DC1", nullptr));
        Lens_DC5->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC4->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_44->setText(QApplication::translate("uiCtrlTFG", "DC10", nullptr));
        Lens_DC8->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_49->setText(QApplication::translate("uiCtrlTFG", "DC15", nullptr));
        Lens_DC6->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_47->setText(QApplication::translate("uiCtrlTFG", "DC13", nullptr));
        label_43->setText(QApplication::translate("uiCtrlTFG", "DC9", nullptr));
        Lens_DC7->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC2->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        label_36->setText(QApplication::translate("uiCtrlTFG", "DC2", nullptr));
        label_42->setText(QApplication::translate("uiCtrlTFG", "DC8", nullptr));
        label_46->setText(QApplication::translate("uiCtrlTFG", "DC12", nullptr));
        label_37->setText(QApplication::translate("uiCtrlTFG", "DC3", nullptr));
        Lens_DC9->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC10->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC11->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC12->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC13->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC14->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC15->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Lens_DC16->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Monitor_ON->setText(QApplication::translate("uiCtrlTFG", "\346\211\223\345\274\200\347\224\265\345\216\213\347\233\221\346\216\247", nullptr));
        Scan_Ctrl->setText(QApplication::translate("uiCtrlTFG", "\344\270\213\345\217\221\345\217\202\346\225\260", nullptr));
        groupBox_3->setTitle(QApplication::translate("uiCtrlTFG", "\347\234\237\347\251\272\346\216\247\345\210\266", nullptr));
        Auto_StartUp->setText(QApplication::translate("uiCtrlTFG", "\350\207\252\345\212\250\345\274\200\347\234\237\347\251\272", nullptr));
        Rpump_ON->setText(QApplication::translate("uiCtrlTFG", "\345\274\200\346\234\272\346\242\260\346\263\265", nullptr));
        label_13->setText(QApplication::translate("uiCtrlTFG", "PG\347\234\237\347\251\272\345\272\246", nullptr));
        IG_ON->setText(QApplication::translate("uiCtrlTFG", "\345\274\200IG", nullptr));
        Auto_ShutDown->setText(QApplication::translate("uiCtrlTFG", "\350\207\252\345\212\250\345\205\263\347\234\237\347\251\272", nullptr));
        PG_P->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Tpump_ON->setText(QApplication::translate("uiCtrlTFG", "\345\274\200\345\210\206\345\255\220\346\263\265", nullptr));
        Tpump_Frq->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Tpump_Fan_State->setText(QApplication::translate("uiCtrlTFG", "\345\210\206\345\255\220\346\263\265\351\243\216\346\211\207\347\212\266\346\200\201", nullptr));
        label_12->setText(QApplication::translate("uiCtrlTFG", "\345\210\206\345\255\220\346\263\265\350\275\254\351\200\237", nullptr));
        IG_P->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Purge_Gas_ON->setText(QApplication::translate("uiCtrlTFG", "\345\274\200\350\277\233\346\260\224", nullptr));
        label_14->setText(QApplication::translate("uiCtrlTFG", "IG\347\234\237\347\251\272\345\272\246", nullptr));
        Rpump_Frq->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        groupBox_4->setTitle(QApplication::translate("uiCtrlTFG", "\351\253\230\345\216\213\346\216\247\345\210\266", nullptr));
        label_5->setText(QApplication::translate("uiCtrlTFG", "V", nullptr));
        label_6->setText(QApplication::translate("uiCtrlTFG", "V", nullptr));
        DET_HV_State->setText(QApplication::translate("uiCtrlTFG", "\347\212\266\346\200\201", nullptr));
        label_7->setText(QApplication::translate("uiCtrlTFG", "V", nullptr));
        label_3->setText(QApplication::translate("uiCtrlTFG", "V", nullptr));
        ESI_HV_IFB->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        CUR_HV_State->setText(QApplication::translate("uiCtrlTFG", "\347\212\266\346\200\201", nullptr));
        label_4->setText(QApplication::translate("uiCtrlTFG", "uA", nullptr));
        ESI_HV_VFB->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        Curtain_HV_VFB->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        HED_HV_VFB->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        HED_HV_State->setText(QApplication::translate("uiCtrlTFG", "\347\212\266\346\200\201", nullptr));
        DET_HV_VFB->setText(QApplication::translate("uiCtrlTFG", "0", nullptr));
        CUR_HV_ON->setText(QApplication::translate("uiCtrlTFG", "CUR_ON", nullptr));
        HED_HV_ON->setText(QApplication::translate("uiCtrlTFG", "HED_ON", nullptr));
        DET_HV_ON->setText(QApplication::translate("uiCtrlTFG", "DET_ON", nullptr));
        ESI_HV_ON->setText(QApplication::translate("uiCtrlTFG", "ESI_ON", nullptr));
        ESI_HV_State->setText(QApplication::translate("uiCtrlTFG", "\347\212\266\346\200\201", nullptr));
        groupBox_5->setTitle(QApplication::translate("uiCtrlTFG", "\344\270\262\345\217\243\350\256\276\347\275\256", nullptr));
        label_2->setText(QApplication::translate("uiCtrlTFG", "\346\263\242\347\211\271\347\216\207", nullptr));
        label->setText(QApplication::translate("uiCtrlTFG", "\347\253\257\345\217\243\345\217\267", nullptr));
        Open_SerialPort->setText(QApplication::translate("uiCtrlTFG", "\346\211\223\345\274\200\n"
"\344\270\262\345\217\243", nullptr));
        update_SerialPort->setText(QApplication::translate("uiCtrlTFG", "\345\210\267\346\226\260", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiCtrlTFG: public Ui_uiCtrlTFG {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UICTRLTFG_H
