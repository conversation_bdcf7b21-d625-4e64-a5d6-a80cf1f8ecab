/********************************************************************************
** Form generated from reading UI file 'uiLicenses.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UILICENSES_H
#define UI_UILICENSES_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiLicenses
{
public:

    void setupUi(QWidget *uiLicenses)
    {
        if (uiLicenses->objectName().isEmpty())
            uiLicenses->setObjectName(QString::fromUtf8("uiLicenses"));
        uiLicenses->resize(810, 526);

        retranslateUi(uiLicenses);

        QMetaObject::connectSlotsByName(uiLicenses);
    } // setupUi

    void retranslateUi(QWidget *uiLicenses)
    {
        uiLicenses->setWindowTitle(QApplication::translate("uiLicenses", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiLicenses: public Ui_uiLicenses {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UILICENSES_H
