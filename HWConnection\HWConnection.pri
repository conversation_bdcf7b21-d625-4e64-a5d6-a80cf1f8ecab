
SOURCES += \
    $$PWD/DataIO/FileShared.cc \
    $$PWD/HWConnection.cpp \
    $$PWD/UMAStructure/UMAEventSegmentSplitter.cpp \
    $$PWD/UMAStructure/UMA_HCS_Data.cpp \
    $$PWD/USBConnection/HCSCommandUSB.cpp \
    $$PWD/USBConnection/HCSCommunicationCMD.cpp

HEADERS += \
    $$PWD/DataIO/FileShared.h \
    $$PWD/HWConnection.h \
    $$PWD/UMAStructure/UMAEventSegmentSplitter.h \
    $$PWD/UMAStructure/UMA_HCS_Data.h \
    $$PWD/UMAStructure/Utility.h \
    $$PWD/USBConnection/HCSCommandUSB.h \
    $$PWD/USBConnection/HCSCommunicationCMD.h

include($$PWD/LibControlCCS_R/LibControlCCS.pri)

INCLUDEPATH += \
    $$PWD \
    $$PWD/UMAStructure

include("../../Libs/Libs/boost/boost.pri")
