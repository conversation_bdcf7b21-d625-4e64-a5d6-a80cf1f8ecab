#pragma once

#include <QList>
#include <QMutex>
#include <QVariant>
#include "LibGlobalToolsR/include/sThread.h"
#include <libusb/libusb-1.0/libusb.h>
#include <cParamCCS.h>

#define SCOMM_STATUS_OPENED 1
#define THREAD_WAITING_TIME 1000
namespace libControlCCS{
/**
 * @brief The MidWareUSB class
 * USB驱动的封装类，内含通讯数据池线程，writeForBack为USB读写接口
 */
class CONTROLCCS_LIBRARY_EXPORT MidWareUSB: public QObject
{
    Q_OBJECT
public:
    struct _COMMUNICATION_STRUCT{
        const void * pBufferW= nullptr;
        uint nBufferW= 0;
        void * pBufferR= nullptr;
        uint nBufferR= 0;
        volatile bool isOK= false;
    };
    volatile bool mIsScan=false;
    MidWareUSB(QObject *parent = nullptr);
    ~MidWareUSB(void);
    bool writeForBack(const void* lpInBuffer,
                      unsigned int nInBufferSize,
                      void* lpOutBuffer,
                      unsigned int nOutBufferSize,
                      unsigned int* lpBytesReturned = nullptr,
                      unsigned int nTimeOutMs = 500);

protected:
    QList<_COMMUNICATION_STRUCT*> mBufferList;
    SThread m_msThread;
    QMutex mBufferrMutex;
    QMutex mWriteMutex;
    int pushBuff(_COMMUNICATION_STRUCT* p_COMMUNICATION_STRUCT);//, int overTime
    int popBuff(const _COMMUNICATION_STRUCT* p_COMMUNICATION_STRUCT);//, int overTime
    int clearBuff(/*_COMMUNICATION_STRUCT* p_COMMUNICATION_STRUCT*/);
    static int controlThread(void *pParam, const bool &bRunning);
    bool reConnect();
    bool open(ushort pid= 4098, ushort vid= 1351);
    void close(void);
    bool write(const void* lpBuffer,
               unsigned int nNumberOfBytesToWrite,
               unsigned int* lpNumberOfBytesWritten = nullptr,
               unsigned int nTimeOutMs = ~0);
    bool read(void* lpBuffer,
              unsigned int nNumberOfBytesToRead,
              unsigned int* lpNumberOfBytesRead = nullptr,
              unsigned int nTimeOutMs = ~0);
private:
    //int m_status = 0; /**< The status for this communication module */
    libusb_context* m_context; /**< TODO */
    libusb_device_handle* m_device; /**< TODO */
//    int status(void){
//        return m_status;
//    }
};
}
