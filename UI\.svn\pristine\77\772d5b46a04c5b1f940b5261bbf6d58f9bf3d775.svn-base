<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiTune</class>
 <widget class="QWidget" name="uiTune">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1097</width>
    <height>601</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QWidget" name="UI_W_TOOL_BAR_TUNE" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout"/>
    </widget>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>1</number>
     </property>
     <widget class="QWidget" name="UI_TAB_VOLTAGETABLE_TUNE">
      <attribute name="title">
       <string>Voltage Table</string>
      </attribute>
      <layout class="QVBoxLayout" name="UI_LAYOUT_VOLTAGETABLE_TUNE">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
      </layout>
     </widget>
     <widget class="QWidget" name="UI_TAB_CAL_TUNE">
      <attribute name="title">
       <string>Calibration</string>
      </attribute>
      <layout class="QVBoxLayout" name="UI_LAYOUT_CAL_TUNE">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
