﻿#include "UMAEventSegmentSplitter.h"
#include <iostream>
#include <set>

#include "UMAStructure/Utility.h"

using namespace UMA_HCS;

//static bool UMA_EventComparer(const UMA_Event& a, const UMA_Event& b)
//{
//    if (Numeric::isAlmostEqual(a.startTime, b.startTime))
//    {
//        return a.endTime < b.endTime;
//    } else
//    {
//        return a.startTime < b.startTime;
//    }
//}

UMAEventSegmentSplitter::UMAEventSegmentSplitter()
{

}

void UMAEventSegmentSplitter::setUMAEvents(const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& events)
{
    mEvents = events;
    prepareSegmentData(mEvents, &mSegments);
}

//void UMAEventSegmentSplitter::setUMAEvents(const std::vector<UMA_HCS::UMA_Event> &events)
//{
//    mEvents = events;
//    prepareSegmentData(mEvents, &mSegments);
//}

const cTQ_StructCMD_HZH::_EVENT_PARAM_SET &UMAEventSegmentSplitter::umaEvents() const
{
    return mEvents;
}
/**
 * @brief UMAEventSegmentSplitter::getUMAEventNo
 * 返回第lChannelId个uma通道数据对应的UMA Event的index， 默认起始索引为 1
 * @param lChannelId
 * @param lpEventNo
 * @return
 */
bool UMAEventSegmentSplitter::getUMAEventNo(uint64_t lChannelId, uint64_t *lpEventNo)
{
    uint64_t lSegmentNo = 0;
    uint64_t lEventNo = 0;
    if (!getUMASegmentNo(lChannelId, &lSegmentNo))
    {
        return false;
    }
    for(uint64_t id = 0; id < lSegmentNo; ++id)
    {
        lChannelId -= mSegments[id].channelCounts;
    }

    uint64_t lChannelSizePerLoop = 0;
    for(auto ev : mSegments[lSegmentNo].events){
        lChannelSizePerLoop += ev.listChannel.size();
    }
    lChannelId = lChannelId % lChannelSizePerLoop;
    for (auto ev : mSegments[lSegmentNo].events){
            if (lChannelId >= ev.listChannel.size()){
                lChannelId -= ev.listChannel.size();
            }else{
                //lEventNo = ev.index;
                break;
            }
    }

    if (lpEventNo != nullptr)
    {
        *lpEventNo = lEventNo;
    }
    return true;
}
/**
 * @brief UMAEventSegmentSplitter::getUMAChannelNo
 * 返回第lChannelId个uma通道数据对应的UMA Event中的通道号，起始为 0
 * @param lChannelId
 * @param lpChannelNo
 * @return
 */
bool UMAEventSegmentSplitter::getUMAChannelNo(uint64_t lChannelId, uint64_t *lpChannelNo)
{
    uint64_t lSegmentNo = 0;
    if (!getUMASegmentNo(lChannelId, &lSegmentNo))
    {
        return false;
    }
    for(uint64_t id = 0; id < lSegmentNo; ++id)
    {
        lChannelId -= mSegments[id].channelCounts;
    }

    uint64_t lChannelSizePerLoop = 0;
    for(auto ev : mSegments[lSegmentNo].events)
    {
        lChannelSizePerLoop += ev.listChannel.size();
    }
    lChannelId = lChannelId % lChannelSizePerLoop;
    for (auto ev : mSegments[lSegmentNo].events)
    {
        if (lChannelId >= ev.listChannel.size())
        {
            lChannelId -= ev.listChannel.size();
        }else
        {
            break;
        }
    }

    if (lpChannelNo != nullptr)
    {
        *lpChannelNo = lChannelId;
    }
    return true;
}

bool UMAEventSegmentSplitter::getUMAChannelPeroid(uint64_t lChannelId, uint64_t *lpTimeMs)
{
    uint64_t lSegmentNo = 0;
    uint64_t lTimeMs = 0;
    if (!getUMASegmentNo(lChannelId, &lSegmentNo)){
        return false;
    }
    for(uint64_t id = 0; id < lSegmentNo; ++id){
        lChannelId -= mSegments[id].channelCounts;
    }

    uint64_t lChannelSizePerLoop = 0;
    for(auto ev : mSegments[lSegmentNo].events){
        lChannelSizePerLoop += ev.listChannel.size();
    }
    lChannelId = lChannelId % lChannelSizePerLoop;
    for (auto ev : mSegments[lSegmentNo].events){
        if (lChannelId >= ev.listChannel.size()){
            lChannelId -= ev.listChannel.size();
        }else{
            lTimeMs = ev.listChannel[lChannelId].eventTime_ms()/*.period() / 1000*/;
            break;
        }
    }

    if (lpTimeMs != nullptr){
        *lpTimeMs = lTimeMs;
    }
    return true;
}

bool UMAEventSegmentSplitter::getUMASegmentNo(uint64_t lChannelId, uint64_t *lpSegmentNo)
{
    uint64_t segmentNo = segmentCount() + 1;
    uint64_t lChannelCount = 0;
    for(uint64_t id=0;id<mSegments.size();++id){
        if(Numeric::isInRange(lChannelCount, lChannelCount + mSegments[id].channelCounts, lChannelId)){
            segmentNo = id;
            break;
        }
        lChannelCount += mSegments[id].channelCounts;
    }

    if (segmentNo == segmentCount() + 1){
        // Not in range;
        std::cerr << "The channel id is not in range." << std::endl;
        return false;
    }

    if (lpSegmentNo != nullptr){
        *lpSegmentNo = segmentNo;
    }
    return true;
}

bool UMAEventSegmentSplitter::getUMATime(uint64_t lChannelId, uint64_t *lpTimeMs)
{
    uint64_t lSegmentNo = 0;
    if (!getUMASegmentNo(lChannelId, &lSegmentNo)){
        return false;
    }
    for(uint64_t id = 0; id < lSegmentNo; ++id){
        lChannelId -= mSegments[id].channelCounts;
    }
    double dStartTimeMin = mSegments[lSegmentNo].startTimeMin;
    uint64_t lTimeMs = static_cast<uint64_t>(dStartTimeMin * 60 * 1000);
    // XXX refactor this code segment to a member function.
    uint64_t lPeriodMs = 0;
    uint64_t lChannelsPerPeriod = 0;
    for (auto ev : mSegments[lSegmentNo].events){
        lChannelsPerPeriod += ev.listChannel.size();
        for(auto ch : ev.listChannel){
            lPeriodMs += ch.eventTime_ms()/*period() / 1000*/;
        }
    }
    uint64_t lLoop = lChannelId / lChannelsPerPeriod;
    uint64_t lChannelRemain = lChannelId % lChannelsPerPeriod;
    lTimeMs += lLoop * lPeriodMs;

    for (auto ev : mSegments[lSegmentNo].events){
        if (lChannelRemain == 0){
            break;
        }
        for(auto ch : ev.listChannel){
            lChannelRemain--;
            lTimeMs += ch.eventTime_ms()/*period() / 1000*/;
            if (lChannelRemain == 0){
                break;
            }
        }
    }

    if (lpTimeMs != nullptr){
        *lpTimeMs = lTimeMs;
    }
    return true;
}
/**
 * @brief UMAEventSegmentSplitter::getTriggerInfo
 * 获取channel ID对应的uma信息,输入channelID的起始值为1, 输出的起始ID为0
 * @param channel
 * @param segIndex
 * @param evtIndex
 * @param chIndex
 */
void UMAEventSegmentSplitter::getTriggerInfo(int channelID, int *segIndex, int *evtIndex, int *chIndex)
{
    int triggerCnt = channelID;
    *segIndex = -1; *evtIndex = -1; *chIndex = -1;
    for (const auto& seg : mSegments)
    {
        (*segIndex)++;
        if (seg.channelCounts < triggerCnt){
            triggerCnt -= seg.channelCounts;
            continue;
        }
        triggerCnt = (triggerCnt-1) % seg.umaChCount() + 1;
        for (const auto &msEvt : seg.events)
        {
            (*evtIndex)++;
            if (msEvt.listChannel.size() < triggerCnt){
                triggerCnt -= msEvt.listChannel.size();
                continue;
            }
            *chIndex = triggerCnt - 1;
            break;
        }
        break;
    }
}

uint64_t UMAEventSegmentSplitter::segmentCount() const
{
    return mSegments.size();
}

const std::vector<UMAEventSegmentSplitter::EventSegment> &UMAEventSegmentSplitter::segment() const
{
    return mSegments;
}

void UMAEventSegmentSplitter::prepareSegmentData(const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& events,
                        std::vector<EventSegment> *pSegments)
{
    auto contains = [&](const cTQ_StructCMD_HZH::_EVENT_PARAM& evt, uint startSeg, uint endSeg)->bool{
        return evt.TimeEndMin > startSeg && evt.TimeStartMin < endSeg;
    };
//    auto contains = [&](const cTQ_StructCMD_HZH::_EVENT_PARAM_SET& evt, double start, double end)->bool{
//        return evt.endTime > start && evt.startTime < end;
//    };
    pSegments->clear();
    std::set<double> timePoint;
    for (auto& msEvt : events.listEvent){
        timePoint.insert(msEvt.TimeStartMin);
        timePoint.insert(msEvt.TimeEndMin);
    }
    std::set<double>::iterator iter = timePoint.begin();
    std::set<double>::iterator iter_end = --timePoint.end();
    while (iter != iter_end){
        EventSegment segment;
        segment.startTimeMin = *iter++;
        segment.endTimeMin = *iter;
        for (auto & evt : events.listEvent){
            if (contains(evt, segment.startTimeMin, segment.endTimeMin))
                segment.events.push_back(evt);
        }
        if (!segment.events.empty())
            pSegments->push_back(segment);
    }
    calculateSegmentChannels(pSegments);
}

//void UMAEventSegmentSplitter::prepareSegmentData(const std::vector<UMA_Event> &events,
//                                                std::vector<EventSegment> *pSegments)
//{
//    auto contains = [&](const UMA_Event& evt, double start, double end)->bool{
//        return evt.endTime > start && evt.startTime < end;
//    };
//    pSegments->clear();
//    std::set<double> timePoint;
//    for (auto& evt : events){
//        timePoint.insert(evt.startTime);
//        timePoint.insert(evt.endTime);
//    }

//    std::set<double>::iterator iter = timePoint.begin();
//    std::set<double>::iterator iter_end = --timePoint.end();
//    while (iter != iter_end){
//        EventSegment segment;
//        segment.startTime = *iter++;
//        segment.endTime = *iter;
//        for (auto & evt : events)
//        {
//            if (contains(evt, segment.startTime, segment.endTime))
//                segment.events.push_back(evt);
//        }
//        if (!segment.events.empty())
//            pSegments->push_back(segment);
//    }
//    calculateSegmentChannels(pSegments);
//#if 0
//    pSegment->clear();
//    std::vector<UMA_Event> events_cp = events;
//    std::sort(events_cp.begin(), events_cp.end(), UMA_EventComparer);
//    std::vector<UMA_Event> events_inner = events_cp;
//    double startTime = 0;
//    double endTime = UINT64_MAX;
//    uint64_t endEvent;
//    uint64_t eventNoOffset = 0;
//    while(events_inner.size() > 0)
//    {
//        std::sort(events_inner.begin(), events_inner.end(), UMA_EventComparer);
//        EventSegment segment;
//        startTime = events_inner.front().startTime;
//        endTime = events_inner.front().endTime;
//        if (events_inner.size() == 1)
//        {
//            segment.events.push_back(events_cp.front());
//            events_inner.clear();
//        }else
//        {
//            // find end time;
//            for (uint64_t id = 0; id < events_inner.size(); ++id)
//            {
//                // check end time;
//                if (endTime > events_inner[id].endTime)
//                {
//                    endTime = events_inner[id].endTime;
//                }

//                //check start time;
//                if (Numeric::isAlmostEqual(startTime, events_inner[id].startTime)){
//                    continue;
//                }
//                if (endTime > events_inner[id].startTime)
//                {
//                    endTime = events_inner[id].startTime;
//                }
//            }

//            // update the inner event
//            for (uint64_t id = 0; id < events_inner.size(); ++id)
//            {
//                if (Numeric::isAlmostEqual(startTime, events_inner[id].startTime)){
//                    if (endTime > events_inner[id].endTime
//                            || Numeric::isAlmostEqual(endTime, events_inner[id].endTime))
//                    {
//                        segment.events.push_back((events_cp[id + eventNoOffset]));
//                        events_inner.erase(events_inner.begin());
//                        eventNoOffset++;
//                        --id;
//                    }
//                    else
//                    {
//                        events_inner[id].startTime = endTime;
//                        segment.events.push_back(events_cp[id + eventNoOffset]);
//                    }
//                }
//            }
//        }
//        segment.startTime = startTime;
//        segment.endTime = endTime;
//        pSegment->push_back(segment);
//    }
//    calculateSegmentChannels(pSegment);
//#endif
//}

void UMAEventSegmentSplitter::calculateSegmentChannels(std::vector<UMAEventSegmentSplitter::EventSegment> *segment)
{

    for(auto iter=segment->begin(); iter!=segment->end();++iter){
        uint64_t lChannel = 0;
        uint64_t lLoopTime_us = (iter->endTimeMin - iter->startTimeMin) * 60.0 * 1000000;
        std::vector<uint64_t> eventTime_us(iter->events.size());//一个segment中event的时间列表
        uint64_t lChannelPeriod = 0;                            //一个segment中所有Channel数
        for (uint64_t id = 0; id < eventTime_us.size(); ++id){
            //uint64_t evTime_us = 0;
            auto ev = iter->events[id];
//            for (auto ch : ev.listChannel){
//                evTime_us += ch.eventTime_us();//.period();
//            }
            //eventTimes[id] = evTime_us;
            eventTime_us[id] = ev.eventTime_ms()*1000;
            lChannelPeriod += ev.listChannel.size();
        }
        uint64_t totalEventPeroid_us = std::accumulate(eventTime_us.begin(), eventTime_us.end(), 0ULL);//一个segment中所有event的总时间
        uint64_t lLoopCount = lLoopTime_us / totalEventPeroid_us;//一个segment中所有event的循环次数
        uint64_t lTimeRemain_us = lLoopTime_us % totalEventPeroid_us;
        for(uint64_t id = 0; id < eventTime_us.size(); ++id){
            if (lTimeRemain_us < eventTime_us[id]){
                break;
            }
            lTimeRemain_us -= eventTime_us[id];
            lChannel += iter->events[id].listChannel.size();
        }
        lChannel += lChannelPeriod * lLoopCount;
        iter->channelCounts = lChannel;
    }
}


