﻿#ifndef ASIOSOCKETCOMMUNICATION_H
#define ASIOSOCKETCOMMUNICATION_H

//#include "HWConnection_global.h"
#include <boost/asio.hpp>
#include <boost/lockfree/queue.hpp>
//#include <boost/asio/detail/socket_ops.hpp>
//#include <boost/format.hpp>
#include <string>
#include <QThread>
#include <QQueue>
#include <QTime>
#include <fstream>
#include <QFile>
#include <QVector>
//#include "SocketPacketAbstractParser.h"
#include "DataIO/UMAData.h"
#include "DataIO/DataIO.h"
#include "DataIO/UMADataIO.h"

#define PACKET_MAX_SIZE 512
#define TOF_DEBUG 0

//#define HOST "*************"
//#define PORT "10001"
#define HOST "127.0.0.1"
#define PORT "8000"


class /*HWCONNECTION_EXPORT*/ AsioSocketCommunication : public QThread
{
    Q_OBJECT
public:
    explicit AsioSocketCommunication(QObject *parent = nullptr);
    virtual ~AsioSocketCommunication() override;
    bool isConnected() const;
public:
    enum DataFlow{
        TOF,
        UMA
    };
    Q_ENUM(DataFlow)
    typedef struct packet
    {
    public:
        char data[PACKET_MAX_SIZE];
        int nSize;
//        packet() {}
    } Packet;

    typedef struct
    {
        char * pData;
        int nSize;
        int nUMA;
    } BlockData;
public:
    Q_INVOKABLE void connectToHost(QString const& host, QString const& port);
    Q_INVOKABLE void disconnectFromHost();
    Q_INVOKABLE bool send(QList<QByteArray> const& byteList);
    Q_INVOKABLE bool send(QByteArray const& byte, int nSize);
    Q_INVOKABLE bool sendForBack(QByteArray const& byte, int nSize, QByteArray & back, int nTimeout=50);
//    Q_INVOKABLE bool rename(std::string const& fileName);
    Q_INVOKABLE bool switchToTOF();
    Q_INVOKABLE bool switchToUMA();
    Q_INVOKABLE bool startDAQ();
    Q_INVOKABLE bool stopDAQ();
    Q_INVOKABLE void setCCSCalibration(bool b);
    bool isCCSCalibration();
//    bool getPacket(Packet & p);
protected:
//    virtual void parse(QByteArray const& packet);   ///< Parse the bit buff
    virtual inline void parse(char * packet, int const& nSize);   ///< Parse the bit buff
    virtual void run() override;
signals:
    void connectStateChanged(bool b);
    void lostFrame();
private:
    bool writeHandler();
    bool readHandler();
    bool parseHandler();
    void clearBuff();
public slots:
private:
    boost::asio::io_context m_service;
    boost::asio::ip::tcp::socket m_socket;
    QString m_addr;
    QString m_port;
    ParsingAction m_eParsingAction;
    ParsingState m_eParsingState;
    int m_nCurrentFrameHead;
    UMA::DataFrame * m_pFrame;
    UMA::UMAFrame * m_pUMA;
//    QTime m_timer;
    int m_nRetry;
    DataFlow m_DataFlow;
    UMA::MSData m_MsData;
    QVector<double> m_pMobiX;
    QVector<double> m_pMobiY;
    UMADataIO * m_pUMADataIO;
    double m_dChroTime;
    QTime m_chroTimer;
    bool m_isCCSCalibration;
protected:
    QByteArray m_parseBuff;
    QByteArray m_writeBuff;
//    std::fstream m_fhLog;
    volatile bool m_bConnected;
    volatile bool m_bDAQRunning;
    bool m_bFindHead;
    QQueue<QByteArray> m_sendBuffQueue;
    QList<QByteArray> m_sendBuffList;
//    UMA::DataWriter m_writer;
//    std::fstream m_buffWriter;

public:
    boost::lockfree::queue<UMA::UMAFrame *, boost::lockfree::fixed_sized<true>> m_readUMAQueue;
    boost::lockfree::queue<Packet * , boost::lockfree::fixed_sized<true>> m_packetDataQueue;///< read DAQ from socket
    boost::lockfree::queue<Packet *, boost::lockfree::fixed_sized<true>> m_commandBackDataQueue; ///< read CMD Back data
};


#endif // ASIOSOCKETCOMMUNICATION_H
