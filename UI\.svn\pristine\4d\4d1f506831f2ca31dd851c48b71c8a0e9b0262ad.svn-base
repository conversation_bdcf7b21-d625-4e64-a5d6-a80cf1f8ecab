﻿#pragma once

#include "uiBaseParamEditor.h"
#include "ui_uiQ13ScanParamEditor.h"
#include <uiMethod/cTQ_StructCMD_HZH.h>

class uiQ13ScanParamEditor : public uiBaseParamEditor
{
    Q_OBJECT

public:
    explicit uiQ13ScanParamEditor(QString Q,
                                  QWidget* parent = nullptr);

    ~uiQ13ScanParamEditor() ;//override;
    virtual void initClass(QString& filePath);

public:
    //void saveParameter() override;
    float getStartMZ(bool *ok=nullptr){
        return ui.lineEdit_startMZ->text().toFloat(ok);
    }
    float getEndMZ(bool *ok=nullptr){
        return ui.lineEdit_endMZ->text().toFloat(ok);
    }
    void setScanSpeed(float speed){
        ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
    }
private slots:
    void on_lineEdit_endMZ_textEdited(const QString &arg1);
    void on_lineEdit_startMZ_textEdited(const QString &arg1);
    //    void on_lineEdit_scanSpeed_textChanged(const QString &arg1);
    void on_lineEdit_pauseTime_textEdited(const QString &arg1);
    void on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1);
    void on_lineEdit_eventTime_textEdited(const QString &arg1);

protected:
    Ui::uiQ13ScanParamEditor ui;
    bool initUI(QString& filePath);
    virtual bool calcUseMapMZ(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE&,
                              cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM&){
        return false;
    }
};

