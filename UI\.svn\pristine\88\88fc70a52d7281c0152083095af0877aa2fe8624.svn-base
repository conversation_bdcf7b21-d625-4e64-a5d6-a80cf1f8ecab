﻿#ifndef UMA_PUBLIC_H
#define UMA_PUBLIC_H

#include <QObject>
#include <QMap>
#include <QDebug>
#include <QDateTime>
#include <QLoggingCategory>
#include <QMutex>

namespace HZH{

struct /*HWCONNECTION_EXPORT*/ ParamAdvanceBase
{
    ParamAdvanceBase() {}
    double voltages[12+ 36] = {0};

    bool operator==(const ParamAdvanceBase &p) const{
        if (this == &p)
            return true;
        else
            return (memcmp(this, &p, sizeof(ParamAdvanceBase)) == 0) ? true : false;
    }

    ParamAdvanceBase& operator =(const ParamAdvanceBase& p){
        if (this == &p)
            return *this;
        memcpy(this, &p, sizeof(ParamAdvanceBase));
        return *this;
    }

    ParamAdvanceBase& operator +=(const ParamAdvanceBase& o){
        auto p1 = reinterpret_cast<double*>(this);
        auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o));
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            p1[i] += p2[i];
        return *this;
    }

    ParamAdvanceBase& operator *=(double factor){
        auto p1 = reinterpret_cast<double*>(this);
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            p1[i] *= factor;
        return *this;
    }
    /**
     * @brief lessThan
     * 任一元素小于o对应元素，返回true, 否则，返回false
     * @param o
     * @return
     */
    bool lessThan(const ParamAdvanceBase& o) const{
        auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(this));
        auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o));
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            if (p1[i] < p2[i])
                return true;

        return false;
    }
    /**
     * @brief moreThan
     * 任一元素大于于o对应元素，返回true, 否则，返回false
     * @param o
     * @return
     */
    bool moreThan(const ParamAdvanceBase& o) const{
        auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(this));
        auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o));
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            if (p1[i] > p2[i])
                return true;

        return false;
    }

    /**
     * @brief operator >
     * 所有元素均大于o的对应元素，返回true, 否则返回false
     * @param o
     * @return
     */
    bool operator > (const ParamAdvanceBase& o) const{
        auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(this));
        auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o));
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            if (p1[i] <= p2[i])
                return false;

        return true;
    }
    /**
     * @brief operator <
     * 所有元素均小于o的对应元素，返回true, 否则返回false
     * @param o
     * @return
     */
    bool operator < (const ParamAdvanceBase& o) const{
        auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(this));
        auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o));
        for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
            if (p1[i] >= p2[i])
                return false;

        return true;
    }
    /**
     * @brief fromQString
     * @param rf 使用,分割的RF
     * @param U 使用,分割的voltages
     * @return true ok; false format err
     */
    bool fromString(/*const QString& rf, */const QString& U){
        QStringList voltages = U.simplified().split(',');
        int sizeV= voltages.size();
        if (sizeV> 12+ 36)
            return false;
        bool ok = true;
        ParamAdvanceBase tmp;
        for (int i = 0; i < (sizeV<12+ 36? sizeV: 12+ 36); ++i) {
            tmp.voltages[i] = voltages.at(i).toDouble(&ok);
            if (!ok)
                return false;
        }
        *this = tmp;
        return true;
    }
    /**
     * @brief toQString
     * @param rf使用,分割的RF
     * @param U 使用,分割的voltage
     * @param prec 精度
     */
    QString toString(int prec = 2) const {
        QString U= QString::number(this->voltages[0],'f',prec);
                for (int i = 1; i < 12+ 36; ++i) {
                    U += ", " + QString::number(this->voltages[i],'f',prec);
                }
        return U;
    }
};

/*HWCONNECTION_EXPORT*/ inline ParamAdvanceBase operator *(const ParamAdvanceBase& o1, double factor){
    auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o1));

    ParamAdvanceBase param;
    auto p = reinterpret_cast<double*>(&param);
    for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
        p[i] =  p1[i] * factor;

    return param;
}

/*HWCONNECTION_EXPORT*/ inline ParamAdvanceBase operator *(double factor, const ParamAdvanceBase& o1){
    return o1 * factor;
}

/*HWCONNECTION_EXPORT*/ inline ParamAdvanceBase operator +(const ParamAdvanceBase& o1, const ParamAdvanceBase& o2){
    auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o1));
    auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o2));
    ParamAdvanceBase param;
    auto p = reinterpret_cast<double*>(&param);
    for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
        p[i] =  p1[i] + p2[i];

    return param;
}

/*HWCONNECTION_EXPORT*/ inline ParamAdvanceBase operator -(const ParamAdvanceBase& o1, const ParamAdvanceBase& o2){
    auto p1 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o1));
    auto p2 = reinterpret_cast<double*>(const_cast<ParamAdvanceBase*>(&o2));
    ParamAdvanceBase param;
    auto p = reinterpret_cast<double*>(&param);
    for (auto i = 0u; i < sizeof (ParamAdvanceBase)/sizeof (double); ++i)
        p[i] =  p1[i] - p2[i];

    return param;
}

/**
 * @brief The paramMZ_Voltages struct
 * 用于质量-电压参数对应存储， 并提供插值功能
 */
struct /*HWCONNECTION_EXPORT*/ paramMZ_Voltages
{
    paramMZ_Voltages() {}
    //      mz , voltages
    QMap<double, ParamAdvanceBase> mzVoltages;
    static QString toString(paramMZ_Voltages* pParamMZ_Voltages){
        QStringList tmpList;
        foreach(const auto &key, pParamMZ_Voltages->mzVoltages.keys()){
            tmpList<< QString::number(key)+":"+pParamMZ_Voltages->mzVoltages[key].toString();
        }
        return tmpList.join(";");
    }
    QString toString(){
        QStringList tmpList;
        foreach(const auto &key, mzVoltages.keys()){
            tmpList<< QString::number(key)+":"+mzVoltages[key].toString();
        }
        return tmpList.join(";");
    }
    bool fromString(QString& str){
        if(str.isEmpty())
            return false;
        QStringList tmpList= str.split(";");
        if(tmpList.isEmpty())
            return false;
        mzVoltages.clear();
        foreach(auto pair, tmpList){
            QStringList tmpList1= pair.split(":");
            if(tmpList1.size()!=2)
                continue;
            ParamAdvanceBase tmpParamAdvanceBase;
            if(!tmpParamAdvanceBase.fromString(tmpList1[1]))
                continue;
            mzVoltages[tmpList1[0].toDouble()]=tmpParamAdvanceBase;
        }
        return true;
    }
    /**
     * @brief getMZParameters
     * 返回列表中mz对应的参数值，如果不存在则返回线性插值的结果。
     * 如果节点长度为0 ， 则返回空的paramAdvanceBase(全为0)；
     * 如果节点长度为1 ， 则返回仅有的节点值。
     * @param mz
     * @return mz对应的参数值
     */
    ParamAdvanceBase getMZParameters(double mz, bool *ok = nullptr) const{
        if (ok)
            *ok = true;
        QList<double> mzList = mzVoltages.keys();
//        qDebug() << __FUNCTION__ << mzList;
        if (mzList.contains(mz))
            return mzVoltages.value(mz);

        ParamAdvanceBase param;
        if (mzList.length() == 0)
            return param;
        if (mzList.length() == 1)
            return mzVoltages.first();

        int index = 0;
        foreach (double curMZ, mzList) {
            if (curMZ > mz)
                break;
            index += 1;
        }
        double startMz, endMz;
        if (index == mzList.length()){ //取最后两个节点向后插值
            startMz = mzList[index - 2];
            endMz = mzList[index - 1];
        }
        else if (index == 0) //取前两个节点向前插值
        {
            startMz = mzList[0];
            endMz = mzList[1];
        }
        else{//在第 index-1 和 第index 个节点间插值
            startMz = mzList[index - 1];
            endMz = mzList[index];
        }
        //线性插值
        bool bOk = linearInterpolation(mz, param,
                            startMz, mzVoltages[startMz],
                            endMz, mzVoltages[endMz]);
        if (nullptr != ok)
            *ok = bOk;
        return param;
    }

    /**
     * @brief linearInterpolation
     * 将从mzStart到mzEnd的参数插值成count个节点，并通过params返回各节点的值
     * @param count
     * @param params
     * @param mzStart
     * @param paramStart
     * @param mzEnd
     * @param paramEnd
     * @return none
     */
    bool linearInterpolation(int count, QMap<double, ParamAdvanceBase>& params,
                             const double mzStart, const double mzEnd) const
    {
        if (count < 1)
            return false;
        params.clear();
        double step = (mzEnd - mzStart) / (count - 1);
        bool ok = true;
        for (int i = 0; i < count; ++i) {
            double mz = mzStart + i * step;
            params.insert(mz, getMZParameters(mz, &ok));
            if (!ok)
                return false;
        }
        return true;
    }

    bool operator==(const paramMZ_Voltages &p) const
    {
        if (this == &p)
            return true;
        else
            return (this->mzVoltages == p.mzVoltages);
    }

    paramMZ_Voltages& operator =(const paramMZ_Voltages& p){
        if (this == &p)
            return *this;
        this->mzVoltages = p.mzVoltages;
        return *this;
    }

private:
    /**
     * @brief linearInterpolation
     * 线性插值，求取targetMz对应的targetParam.
     * 如果mzStart == mzEnd, 则令targetParam = paramStart ,并返回false;
     * @param targetMz 期望获取mz对应的ParamAdvanceBase
     * @param targetParam 存储获取的targetMz对应的ParamAdvanceBase
     * @param mzStart
     * @param paramStart mzStart对应的参数
     * @param mzEnd
     * @param paramEnd mzEnd对应的参数
     * @return 0
     */
    bool linearInterpolation(const double targetMz, ParamAdvanceBase& targetParam,
                             const double mzStart, const ParamAdvanceBase& paramStart,
                             const double mzEnd, const ParamAdvanceBase& paramEnd) const
    {
        if (qFuzzyCompare(mzStart, mzEnd)){
            targetParam = paramStart;
            return false;
        }
        for (int i = 0; i < 12+ 36; i++) {
            targetParam.voltages[i] = (paramEnd.voltages[i] - paramStart.voltages[i]) /
                                    (mzEnd - mzStart) * (targetMz-mzStart)
                                + paramStart.voltages[i];
        }
        return true;
    }
};
}

Q_DECLARE_METATYPE(HZH::ParamAdvanceBase)

#endif // UMA_PUBLIC_H
