<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiMainWindow</class>
 <widget class="QWidget" name="uiMainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>sMainWindow</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="UI_W_MENUBAR_MAINW" native="true">
     <layout class="QHBoxLayout" name="UI_LAYOUT_MENUBAR_MAINW">
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_MAIN_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_SYSTEM_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_MANUAL_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_LIST_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_CREATE_LIST_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_MASS_METHOD_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_LIQUID_METHOD_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_DATA_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_TUNE_MAINW" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_SINAL_SCAN_MAINW" native="true"/>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MPB_MENU_STATE_MAINW" native="true"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="UI_W_CONTAINER_WINDOW_MAINW" native="true">
     <layout class="QVBoxLayout" name="UI_LAYOUT_CONTAINER_WINDOW_MAINW">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="UI_W_MAINWIDOW" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QWidget" name="UI_W_MENU_MAINW" native="true">
           <layout class="QGridLayout" name="UI_LAYOUT_MENU_MAINW">
            <item row="3" column="3">
             <widget class="MyWidget::sMyButton" name="UI_MPB_LIQUID_METHOD_MAINW" native="true"/>
            </item>
            <item row="1" column="2">
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="2" column="1">
             <spacer name="verticalSpacer_3">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="7" column="3">
             <widget class="MyWidget::sMyButton" name="UI_MPB_SIGNAL_SCAN_MAINW" native="true"/>
            </item>
            <item row="1" column="1">
             <widget class="MyWidget::sMyButton" name="UI_MPB_LIST_MAINW" native="true"/>
            </item>
            <item row="8" column="1">
             <spacer name="verticalSpacer_2">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="5" column="1">
             <widget class="MyWidget::sMyButton" name="UI_MPB_DATA_MAINW" native="true"/>
            </item>
            <item row="0" column="1">
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="5" column="3">
             <widget class="MyWidget::sMyButton" name="UI_MPB_TUNE_MAINW" native="true"/>
            </item>
            <item row="7" column="1">
             <widget class="MyWidget::sMyButton" name="UI_MPB_SYSTEM_MAINW" native="true"/>
            </item>
            <item row="1" column="3">
             <widget class="MyWidget::sMyButton" name="UI_MPB_CREATE_LIST_MAINW" native="true"/>
            </item>
            <item row="3" column="1">
             <widget class="MyWidget::sMyButton" name="UI_MPB_MASS_METHOD_MAINW" native="true"/>
            </item>
            <item row="4" column="1">
             <spacer name="verticalSpacer_4">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="0" column="0">
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="6" column="1">
             <spacer name="verticalSpacer_5">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>385</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="UI_W_STATEBAR_MAINW" native="true">
     <layout class="QGridLayout" name="gridLayout">
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>6</number>
      </property>
      <item row="0" column="0">
       <widget class="QPushButton" name="pushButton_3">
        <property name="text">
         <string>PushButton</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyWidget::sMyButton</class>
   <extends>QWidget</extends>
   <header>LibWidget/sMyButton.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
