/********************************************************************************
** Form generated from reading UI file 'uiBatch.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIBATCH_H
#define UI_UIBATCH_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "LibWidget/sMyButton.h"

QT_BEGIN_NAMESPACE

class Ui_uiBatch
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *UI_W_TOOLBAR_BATCH;
    QHBoxLayout *horizontalLayout;
    MyWidget::sMyButton *UI_MB_NEW_BATCH;
    MyWidget::sMyButton *UI_MB_OPEN_BATCH;
    MyWidget::sMyButton *UI_MB_SAVE_BATCH;
    MyWidget::sMyButton *UI_MB_SAVEAS_BATCH;
    MyWidget::sMyButton *UI_MB_PLATE_LAYOUT_BATCH;
    MyWidget::sMyButton *UI_MB_SUBMIT_BATCH;
    QLabel *UI_L_DATE_BATCH;
    QSpacerItem *horizontalSpacer;
    QLabel *UI_L_CURRENT_FILE_BATCH;
    QTabWidget *UI_TABWIDGET_LIST_BATCH;
    QWidget *tab;
    QVBoxLayout *UI_LAYOUT_TAB1_BATCH;
    QTableWidget *UI_TW_BATCH;

    void setupUi(QWidget *uiBatch)
    {
        if (uiBatch->objectName().isEmpty())
            uiBatch->setObjectName(QString::fromUtf8("uiBatch"));
        uiBatch->resize(882, 532);
        verticalLayout = new QVBoxLayout(uiBatch);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        UI_W_TOOLBAR_BATCH = new QWidget(uiBatch);
        UI_W_TOOLBAR_BATCH->setObjectName(QString::fromUtf8("UI_W_TOOLBAR_BATCH"));
        horizontalLayout = new QHBoxLayout(UI_W_TOOLBAR_BATCH);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        UI_MB_NEW_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_NEW_BATCH->setObjectName(QString::fromUtf8("UI_MB_NEW_BATCH"));

        horizontalLayout->addWidget(UI_MB_NEW_BATCH);

        UI_MB_OPEN_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_OPEN_BATCH->setObjectName(QString::fromUtf8("UI_MB_OPEN_BATCH"));

        horizontalLayout->addWidget(UI_MB_OPEN_BATCH);

        UI_MB_SAVE_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_SAVE_BATCH->setObjectName(QString::fromUtf8("UI_MB_SAVE_BATCH"));

        horizontalLayout->addWidget(UI_MB_SAVE_BATCH);

        UI_MB_SAVEAS_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_SAVEAS_BATCH->setObjectName(QString::fromUtf8("UI_MB_SAVEAS_BATCH"));

        horizontalLayout->addWidget(UI_MB_SAVEAS_BATCH);

        UI_MB_PLATE_LAYOUT_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_PLATE_LAYOUT_BATCH->setObjectName(QString::fromUtf8("UI_MB_PLATE_LAYOUT_BATCH"));

        horizontalLayout->addWidget(UI_MB_PLATE_LAYOUT_BATCH);

        UI_MB_SUBMIT_BATCH = new MyWidget::sMyButton(UI_W_TOOLBAR_BATCH);
        UI_MB_SUBMIT_BATCH->setObjectName(QString::fromUtf8("UI_MB_SUBMIT_BATCH"));

        horizontalLayout->addWidget(UI_MB_SUBMIT_BATCH);

        UI_L_DATE_BATCH = new QLabel(UI_W_TOOLBAR_BATCH);
        UI_L_DATE_BATCH->setObjectName(QString::fromUtf8("UI_L_DATE_BATCH"));

        horizontalLayout->addWidget(UI_L_DATE_BATCH);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        verticalLayout->addWidget(UI_W_TOOLBAR_BATCH);

        UI_L_CURRENT_FILE_BATCH = new QLabel(uiBatch);
        UI_L_CURRENT_FILE_BATCH->setObjectName(QString::fromUtf8("UI_L_CURRENT_FILE_BATCH"));

        verticalLayout->addWidget(UI_L_CURRENT_FILE_BATCH);

        UI_TABWIDGET_LIST_BATCH = new QTabWidget(uiBatch);
        UI_TABWIDGET_LIST_BATCH->setObjectName(QString::fromUtf8("UI_TABWIDGET_LIST_BATCH"));
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        UI_LAYOUT_TAB1_BATCH = new QVBoxLayout(tab);
        UI_LAYOUT_TAB1_BATCH->setObjectName(QString::fromUtf8("UI_LAYOUT_TAB1_BATCH"));
        UI_TW_BATCH = new QTableWidget(tab);
        UI_TW_BATCH->setObjectName(QString::fromUtf8("UI_TW_BATCH"));

        UI_LAYOUT_TAB1_BATCH->addWidget(UI_TW_BATCH);

        UI_TABWIDGET_LIST_BATCH->addTab(tab, QString());

        verticalLayout->addWidget(UI_TABWIDGET_LIST_BATCH);


        retranslateUi(uiBatch);

        UI_TABWIDGET_LIST_BATCH->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(uiBatch);
    } // setupUi

    void retranslateUi(QWidget *uiBatch)
    {
        uiBatch->setWindowTitle(QApplication::translate("uiBatch", "Form", nullptr));
        UI_L_DATE_BATCH->setText(QString());
        UI_L_CURRENT_FILE_BATCH->setText(QString());
        UI_TABWIDGET_LIST_BATCH->setTabText(UI_TABWIDGET_LIST_BATCH->indexOf(tab), QString());
    } // retranslateUi

};

namespace Ui {
    class uiBatch: public Ui_uiBatch {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIBATCH_H
