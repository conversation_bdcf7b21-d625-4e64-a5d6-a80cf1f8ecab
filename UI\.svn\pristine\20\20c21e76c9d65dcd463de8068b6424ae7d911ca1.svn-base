/********************************************************************************
** Form generated from reading UI file 'uiSoftwareUpdates.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UISOFTWAREUPDATES_H
#define UI_UISOFTWAREUPDATES_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiSoftwareUpdates
{
public:

    void setupUi(QWidget *uiSoftwareUpdates)
    {
        if (uiSoftwareUpdates->objectName().isEmpty())
            uiSoftwareUpdates->setObjectName(QString::fromUtf8("uiSoftwareUpdates"));
        uiSoftwareUpdates->resize(836, 540);

        retranslateUi(uiSoftwareUpdates);

        QMetaObject::connectSlotsByName(uiSoftwareUpdates);
    } // setupUi

    void retranslateUi(QWidget *uiSoftwareUpdates)
    {
        uiSoftwareUpdates->setWindowTitle(QApplication::translate("uiSoftwareUpdates", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiSoftwareUpdates: public Ui_uiSoftwareUpdates {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UISOFTWAREUPDATES_H
