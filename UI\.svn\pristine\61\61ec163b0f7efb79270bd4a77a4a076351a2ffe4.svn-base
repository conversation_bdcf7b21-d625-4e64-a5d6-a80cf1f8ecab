#include "uiWindowMenu.h"


uiWindowMenu::uiWindowMenu(QWidget *parent) :
    sClickWidget(parent)/*,
    mParent(parent),
    ui(new Ui::uiWindowMenu)*/
{
    ui.setupUi(this);
    setWorkWidget(parent);
    connect(this,SIGNAL(moved()),this,SLOT(onMouseMove()));

    mMinButton= new MyWidget::sMyButton(QPixmap(":/Menu/picture/min_32.png"),
                                        QPixmap(":/Menu/picture/min_32.png"),
                                        QPixmap(":/Menu/picture/min_press_32.png"));
    ui.UI_LAYOUT_WINDOW_MENU->addWidget(mMinButton);
    connect(mMinButton,SIGNAL(ButtonClicked()),parent,SLOT(showMinimized()));
    mMaxButton= new MyWidget::sMyButton(QPixmap(":/Menu/picture/Normal_32.png"),
                                        QPixmap(":/Menu/picture/Normal_32.png"),
                                        QPixmap(":/Menu/picture/Normal_blue_32.png"));
    ui.UI_LAYOUT_WINDOW_MENU->addWidget(mMaxButton);
    connect(mMaxButton,SIGNAL(ButtonClicked()),this,SLOT(onClickedShowNormal()));
    mCloseButton= new MyWidget::sMyButton(QPixmap(":/Menu/picture/close_32.png"),
                                          QPixmap(":/Menu/picture/close_32.png"),
                                          QPixmap(":/Menu/picture/close_press_32.png"));
    ui.UI_LAYOUT_WINDOW_MENU->addWidget(mCloseButton);
    connect(mCloseButton,SIGNAL(ButtonClicked()),parent,SLOT(close()));
}

uiWindowMenu::~uiWindowMenu()
{
    //delete ui;
}
