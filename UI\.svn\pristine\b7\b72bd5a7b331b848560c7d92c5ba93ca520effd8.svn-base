<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>sChartXIC</class>
 <widget class="QWidget" name="sChartXIC">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>552</width>
    <height>341</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_2" native="true">
     <property name="palette">
      <palette>
       <active>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>90</red>
           <green>90</green>
           <blue>90</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>75</red>
           <green>75</green>
           <blue>75</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>40</red>
           <green>40</green>
           <blue>40</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="PlaceholderText">
         <brush brushstyle="NoBrush">
          <color alpha="128">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
       </active>
       <inactive>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>90</red>
           <green>90</green>
           <blue>90</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>75</red>
           <green>75</green>
           <blue>75</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>40</red>
           <green>40</green>
           <blue>40</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="PlaceholderText">
         <brush brushstyle="NoBrush">
          <color alpha="128">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
       </inactive>
       <disabled>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>90</red>
           <green>90</green>
           <blue>90</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>75</red>
           <green>75</green>
           <blue>75</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>40</red>
           <green>40</green>
           <blue>40</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>30</red>
           <green>30</green>
           <blue>30</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>60</red>
           <green>60</green>
           <blue>60</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="PlaceholderText">
         <brush brushstyle="NoBrush">
          <color alpha="128">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
       </disabled>
      </palette>
     </property>
     <property name="autoFillBackground">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="spacing">
         <number>2</number>
        </property>
        <item>
         <widget class="QListWidget" name="listWidget">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>170</red>
                <green>170</green>
                <blue>170</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Text">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="BrightText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ButtonText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>220</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="128">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>170</red>
                <green>170</green>
                <blue>170</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Text">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="BrightText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ButtonText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>220</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="128">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>170</red>
                <green>170</green>
                <blue>170</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Text">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="BrightText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ButtonText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>127</red>
                <green>127</green>
                <blue>127</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>220</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="ToolTipText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="128">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="font">
           <font>
            <family>Microsoft YaHei UI</family>
            <pointsize>11</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="autoFillBackground">
           <bool>true</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">QListWidget{background-color: rgb(60, 60, 60); border-style:none;
color:rgb(228,218,208);}
QListWidget::Item{height:44px;}
QListWidget::Item:hover{color:#EFD98B;}
QListWidget::item:selected{color:#EFD98B;}
QListWidget::item:selected:active{color:#EFD98B;}</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout">
          <property name="horizontalSpacing">
           <number>0</number>
          </property>
          <property name="verticalSpacing">
           <number>6</number>
          </property>
          <item row="10" column="0">
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="5" column="0">
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="4" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <widget class="QLabel" name="label">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>32</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="palette">
                 <palette>
                  <active>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </active>
                  <inactive>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </inactive>
                  <disabled>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </disabled>
                 </palette>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                  <pointsize>11</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>Mass offset(Th):</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="palette">
                 <palette>
                  <active>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>90</red>
                      <green>90</green>
                      <blue>90</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>75</red>
                      <green>75</green>
                      <blue>75</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>40</red>
                      <green>40</green>
                      <blue>40</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Text">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="BrightText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>255</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ButtonText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>255</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Base">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="127">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>220</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="PlaceholderText">
                    <brush brushstyle="NoBrush">
                     <color alpha="128">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </active>
                  <inactive>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>90</red>
                      <green>90</green>
                      <blue>90</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>75</red>
                      <green>75</green>
                      <blue>75</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>40</red>
                      <green>40</green>
                      <blue>40</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Text">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="BrightText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>255</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ButtonText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>255</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Base">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="127">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>220</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="PlaceholderText">
                    <brush brushstyle="NoBrush">
                     <color alpha="128">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </inactive>
                  <disabled>
                   <colorrole role="WindowText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>90</red>
                      <green>90</green>
                      <blue>90</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>75</red>
                      <green>75</green>
                      <blue>75</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>40</red>
                      <green>40</green>
                      <blue>40</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Text">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="BrightText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>255</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ButtonText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>30</red>
                      <green>30</green>
                      <blue>30</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Base">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="0">
                      <red>60</red>
                      <green>60</green>
                      <blue>60</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>255</red>
                      <green>255</green>
                      <blue>220</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="ToolTipText">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="PlaceholderText">
                    <brush brushstyle="NoBrush">
                     <color alpha="128">
                      <red>228</red>
                      <green>218</green>
                      <blue>208</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </disabled>
                 </palette>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                  <pointsize>11</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="autoFillBackground">
                 <bool>false</bool>
                </property>
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(60, 60, 60);
border-style: none;</string>
                </property>
                <property name="text">
                 <string>0.5</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignBottom|Qt::AlignHCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QWidget" name="widget" native="true">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>2</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>2</height>
               </size>
              </property>
              <property name="palette">
               <palette>
                <active>
                 <colorrole role="WindowText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Button">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Light">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Midlight">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>241</red>
                    <green>236</green>
                    <blue>231</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Dark">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Mid">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>152</red>
                    <green>145</green>
                    <blue>139</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Text">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="BrightText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ButtonText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Base">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Window">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Shadow">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="AlternateBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>241</red>
                    <green>236</green>
                    <blue>231</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>220</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="PlaceholderText">
                  <brush brushstyle="NoBrush">
                   <color alpha="128">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                </active>
                <inactive>
                 <colorrole role="WindowText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Button">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Light">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Midlight">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>241</red>
                    <green>236</green>
                    <blue>231</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Dark">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Mid">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>152</red>
                    <green>145</green>
                    <blue>139</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Text">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="BrightText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ButtonText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Base">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Window">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Shadow">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="AlternateBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>241</red>
                    <green>236</green>
                    <blue>231</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>220</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="PlaceholderText">
                  <brush brushstyle="NoBrush">
                   <color alpha="128">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                </inactive>
                <disabled>
                 <colorrole role="WindowText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Button">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Light">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Midlight">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>241</red>
                    <green>236</green>
                    <blue>231</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Dark">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Mid">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>152</red>
                    <green>145</green>
                    <blue>139</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Text">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="BrightText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>255</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ButtonText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>114</red>
                    <green>109</green>
                    <blue>104</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Base">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Window">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="Shadow">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="AlternateBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>228</red>
                    <green>218</green>
                    <blue>208</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipBase">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>255</red>
                    <green>255</green>
                    <blue>220</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="ToolTipText">
                  <brush brushstyle="SolidPattern">
                   <color alpha="255">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                 <colorrole role="PlaceholderText">
                  <brush brushstyle="NoBrush">
                   <color alpha="128">
                    <red>0</red>
                    <green>0</green>
                    <blue>0</blue>
                   </color>
                  </brush>
                 </colorrole>
                </disabled>
               </palette>
              </property>
              <property name="autoFillBackground">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="14" column="0">
           <spacer name="verticalSpacer_3">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="6" column="0">
           <layout class="QVBoxLayout" name="verticalLayout">
            <item>
             <widget class="QPushButton" name="B_Remove">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>11</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgba(0,0,0,0);
color:rgb(228,218,208);
border-style: outset;
border-width:2px;
border-radius:10px;
border-color:rgb(228,218,208);
padding:6px;</string>
              </property>
              <property name="text">
               <string>Remove</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="B_Modify">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>11</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgba(0,0,0,0);
color:rgb(228,218,208);
border-style: outset;
border-width:2px;
border-radius:10px;
border-color:rgb(228,218,208);
padding:6px;</string>
              </property>
              <property name="text">
               <string>Modify</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="B_Add">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>11</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgba(0,0,0,0);
color:rgb(228,218,208);
border-style: outset;
border-width:2px;
border-radius:10px;
border-color:rgb(228,218,208);
padding:6px;</string>
              </property>
              <property name="text">
               <string>Add</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="12" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <widget class="QPushButton" name="B_Cancel">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>11</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: #EFD98B;
color:#3c3c3c;
border-style:none;
border-radius:10px;
border-color:#EFD98B;
padding:6px;</string>
              </property>
              <property name="text">
               <string>Cancel</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="B_OK">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>11</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: #EFD98B;
color:#3c3c3c;
border-style:none;
border-radius:10px;
border-color:#EFD98B;
padding:6px;</string>
              </property>
              <property name="text">
               <string>OK</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
