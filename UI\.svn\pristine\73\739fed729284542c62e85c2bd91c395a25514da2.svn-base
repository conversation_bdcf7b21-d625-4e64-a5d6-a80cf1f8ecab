<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiMRMParameterEditor</class>
 <widget class="QWidget" name="uiMRMParameterEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>586</width>
    <height>403</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="uiMRMParameterEditorWidget" native="true">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>50</y>
     <width>278</width>
     <height>214</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QTableWidget" name="tableWidget_chanel">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="alternatingRowColors">
       <bool>true</bool>
      </property>
      <property name="selectionMode">
       <enum>QAbstractItemView::ExtendedSelection</enum>
      </property>
      <property name="selectionBehavior">
       <enum>QAbstractItemView::SelectItems</enum>
      </property>
      <attribute name="horizontalHeaderDefaultSectionSize">
       <number>95</number>
      </attribute>
      <attribute name="horizontalHeaderStretchLastSection">
       <bool>true</bool>
      </attribute>
      <attribute name="verticalHeaderVisible">
       <bool>false</bool>
      </attribute>
      <attribute name="verticalHeaderMinimumSectionSize">
       <number>20</number>
      </attribute>
      <attribute name="verticalHeaderDefaultSectionSize">
       <number>25</number>
      </attribute>
      <column>
       <property name="text">
        <string>Precursor m/z</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Product m/z</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Dwell Time(ms)</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>CE</string>
       </property>
      </column>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
