QT       += core gui script xml concurrent serialport network

CONFIG += c++17#c++11

SOURCES += \
    $$PWD/uiTools/mzVoltageTableEditor.cpp \
    $$PWD/uiMethod/cTQ_StructCMD_AD81416.cpp \
    $$PWD/uiMethod/cTQ_StructCMD_HZH.cpp \
    $$PWD/uiBatch.cpp \
    $$PWD/uiBatch/uiPlateLayout.cpp \
    $$PWD/uiBatch/uiXlsxSheetModel.cpp \
    $$PWD/uiState/uiCtrlTFG.cpp \
    $$PWD/uiMainWindow/sWindowLog.cpp \
    $$PWD/uiMainWindow/sListWidgetForLog.cpp \
    $$PWD/uiMainWindow.cpp \
    $$PWD/uiMainWindow/uiInitMainWindow.cpp \
    $$PWD/uiMainWindow/uiWindowMenu.cpp \
    $$PWD/uiQueue.cpp \
    $$PWD/uiState.cpp \
    $$PWD/uiSystem.cpp \
    $$PWD/uiSystem/uiDevices.cpp \
    $$PWD/uiSystem/uiLicenses.cpp \
    $$PWD/uiSystem/uiProjects.cpp \
    $$PWD/uiSystem/uiQueueSys.cpp \
    $$PWD/uiSystem/uiSoftwareUpdates.cpp \
    $$PWD/uiSystem/uiUserManagement.cpp \
    $$PWD/uiTune.cpp \
    $$PWD/uiTune/uiCalibrationMass.cpp \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationMassItem.cpp \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationView.cpp \
    $$PWD/uiTune/uiMapSetMZ.cpp \
    $$PWD/uiTune/uiStaticASG.cpp \
    $$PWD/uiManualMode.cpp

HEADERS += \
    $$PWD/cMathHZH.h \
    $$PWD/cPublicStructHZH.h \
    $$PWD/cConfigTQ_HZH.h \
    $$PWD/cGlobalStruct.h \
    $$PWD/uiTools/mzVoltageTableEditor.h \
    $$PWD/uiMethod/cParamMZ_Voltages.h \
    $$PWD/uiMethod/cTQ_StructCMD_AD81416.h \
    $$PWD/uiMethod/cTQ_StructCMD_HZH.h \
    $$PWD/uiBatch.h \
    $$PWD/uiBatch/uiPlateLayout.h \
    $$PWD/uiBatch/uiXlsxSheetModel.h \
    $$PWD/uiBatch/uiXlsxSheetModelHelp.h \
    $$PWD/uiState/uiCtrlTFG.h \
    $$PWD/uiMainWindow/sListWidgetForLog.h \
    $$PWD/uiMainWindow.h \
    $$PWD/uiMainWindow/uiInitMainWindow.h \
    $$PWD/uiMainWindow/uiWindowMenu.h \
    $$PWD/uiQueue.h \
    $$PWD/uiState.h \
    $$PWD/uiSystem.h \
    $$PWD/uiSystem/uiDevices.h \
    $$PWD/uiSystem/uiLicenses.h \
    $$PWD/uiSystem/uiProjects.h \
    $$PWD/uiSystem/uiQueueSys.h \
    $$PWD/uiSystem/uiSoftwareUpdates.h \
    $$PWD/uiSystem/uiUserManagement.h \
    $$PWD/uiTune.h \
    $$PWD/uiTune/uiCalibrationMass.h \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationMassItem.h \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationView.h \
    $$PWD/uiTune/uiMapSetMZ.h \
    $$PWD/uiTune/uiStaticASG.h \
    $$PWD/uiManualMode.h

FORMS += \
    $$PWD/uiBatch.ui \
    $$PWD/uiBatch/uiPlateLayout.ui \
    $$PWD/uiState/uiCtrlTFG.ui \
    $$PWD/uiMainWindow.ui \
    $$PWD/uiMainWindow/uiWindowMenu.ui \
    $$PWD/uiQueue.ui \
    $$PWD/uiState.ui \
    $$PWD/uiSystem.ui \
    $$PWD/uiSystem/uiDevices.ui \
    $$PWD/uiSystem/uiLicenses.ui \
    $$PWD/uiSystem/uiProjects.ui \
    $$PWD/uiSystem/uiQueueSys.ui \
    $$PWD/uiSystem/uiSoftwareUpdates.ui \
    $$PWD/uiSystem/uiUserManagement.ui \
    $$PWD/uiTune.ui \
    $$PWD/uiTune/uiCalibrationMass.ui \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationMassItem.ui \
    $$PWD/uiTune/uiCalibrationMass/uiCalibrationView.ui \
    $$PWD/uiTune/uiMapSetMZ.ui \
    $$PWD/uiTune/uiStaticASG.ui \
    $$PWD/uiManualMode.ui

INCLUDEPATH += \
    $$PWD \
    $$PWD/uiTune \
    $$PWD/uiMainWindow

DEPENDPATH += \
    $$PWD \
    $$PWD/uiTune \
    $$PWD/uiMainWindow

RESOURCES += \
    $$PWD/uiMainWindow/uiMainWindow.qrc \
    $$PWD/images/images.qrc


win32{
    include(D:/QT/GlobalStruct/libGlobalStruct.pri)
}
unix{
    include(/home/<USER>/work/GlobalStruct/libGlobalStruct.pri)
}

include($$PWD/LibGlobalToolsR/LibGlobalTools.pri)
include($$PWD/LibMatrixR/LibMatrix.pri)
include($$PWD/LibXlsx/qtxlsx.pri)
include($$PWD/LibWidget/LibWidget.pri)
include($$PWD/LibSerialDeviceManagerR/LibSerialDeviceManager.pri)
include($$PWD/uiSingleAcquisition.pri)
