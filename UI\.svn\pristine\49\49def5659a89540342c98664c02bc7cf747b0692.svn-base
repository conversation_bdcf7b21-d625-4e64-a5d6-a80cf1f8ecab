/****************************************************************************
** Meta object code from reading C++ file 'mzVoltageTableEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiTools/mzVoltageTableEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mzVoltageTableEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_mzVoltageTableEditor_t {
    QByteArrayData data[12];
    char stringdata0[174];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_mzVoltageTableEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_mzVoltageTableEditor_t qt_meta_stringdata_mzVoltageTableEditor = {
    {
QT_MOC_LITERAL(0, 0, 20), // "mzVoltageTableEditor"
QT_MOC_LITERAL(1, 21, 22), // "curMzVoltageMapChanged"
QT_MOC_LITERAL(2, 44, 0), // ""
QT_MOC_LITERAL(3, 45, 21), // "HZH::paramMZ_Voltages"
QT_MOC_LITERAL(4, 67, 3), // "map"
QT_MOC_LITERAL(5, 71, 20), // "onCurrentCellChanged"
QT_MOC_LITERAL(6, 92, 6), // "curRow"
QT_MOC_LITERAL(7, 99, 6), // "curCol"
QT_MOC_LITERAL(8, 106, 6), // "preRow"
QT_MOC_LITERAL(9, 113, 6), // "preCol"
QT_MOC_LITERAL(10, 120, 27), // "updateCurMzVolMapsFromTable"
QT_MOC_LITERAL(11, 148, 25) // "updateCurMzVolMapsToTable"

    },
    "mzVoltageTableEditor\0curMzVoltageMapChanged\0"
    "\0HZH::paramMZ_Voltages\0map\0"
    "onCurrentCellChanged\0curRow\0curCol\0"
    "preRow\0preCol\0updateCurMzVolMapsFromTable\0"
    "updateCurMzVolMapsToTable"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_mzVoltageTableEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    4,   37,    2, 0x08 /* Private */,
      10,    0,   46,    2, 0x08 /* Private */,
      11,    1,   47,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,    6,    7,    8,    9,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 3,    4,

       0        // eod
};

void mzVoltageTableEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<mzVoltageTableEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->curMzVoltageMapChanged((*reinterpret_cast< const HZH::paramMZ_Voltages(*)>(_a[1]))); break;
        case 1: _t->onCurrentCellChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        case 2: _t->updateCurMzVolMapsFromTable(); break;
        case 3: _t->updateCurMzVolMapsToTable((*reinterpret_cast< const HZH::paramMZ_Voltages(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (mzVoltageTableEditor::*)(const HZH::paramMZ_Voltages & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&mzVoltageTableEditor::curMzVoltageMapChanged)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject mzVoltageTableEditor::staticMetaObject = { {
    &Spreadsheet::staticMetaObject,
    qt_meta_stringdata_mzVoltageTableEditor.data,
    qt_meta_data_mzVoltageTableEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *mzVoltageTableEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *mzVoltageTableEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_mzVoltageTableEditor.stringdata0))
        return static_cast<void*>(this);
    return Spreadsheet::qt_metacast(_clname);
}

int mzVoltageTableEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = Spreadsheet::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void mzVoltageTableEditor::curMzVoltageMapChanged(const HZH::paramMZ_Voltages & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
