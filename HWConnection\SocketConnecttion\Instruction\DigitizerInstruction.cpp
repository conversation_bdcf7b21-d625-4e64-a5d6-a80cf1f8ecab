﻿#include "DigitizerInstruction.h"
#include <cstring>

DigitizerInstruction::DigitizerInstruction()
{

}

void DigitizerInstruction::StartDAQ(char *pData, int &nSize)
{
    pData[0] = 0xFF;
    pData[1] = 0xFF;
    pData[2] = 0x01;
    pData[3] = 0x86;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::StopDAQ(char *pData, int &nSize)
{
    pData[0] = 0x00;
    pData[1] = 0x00;
    pData[2] = 0x01;
    pData[3] = 0x86;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::SetState(char *pData, int &nSize)
{
    pData[0] = 0xFF;
    pData[1] = 0x00;
    pData[2] = 0xFF;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::ResetState(char *pData, int &nSize)
{
    pData[0] = 0x00;
    pData[1] = 0x00;
    pData[2] = 0xFF;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::ResetWiznet(char *pData, int &nSize)
{
    pData[0] = 0x00;
    pData[1] = 0x00;
    pData[2] = 0xFF;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    pData[8] = 0x00;
    pData[9] = 0xFF;
    pData[10] = 0xFF;
    pData[11] = 0x88;
    memset(&pData[12], 0x00, sizeof(char) * 4);
    nSize = 16;
}

void DigitizerInstruction::ResetAll(char *pData, int &nSize)
{
    pData[0] = 0x00;
    pData[1] = 0x00;
    pData[2] = 0xFF;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    pData[8] = 0xFF;
    pData[9] = 0xFF;
    pData[10] = 0xFF;
    pData[11] = 0x88;
    memset(&pData[12], 0x00, sizeof(char) * 4);
    nSize = 16;
}

void DigitizerInstruction::AccParam(char *pData, std::pair<unsigned short, unsigned short> pAcc, int &nSize)
{
    pData[0] = pAcc.first & 0xff;
    pData[1] = pAcc.first >> 8 & 0xff;
    pData[2] = 0x03;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    pData[8] = pAcc.second & 0xff;
    pData[9] = pAcc.second >> 8 & 0xff;
    pData[10] = 0x04;
    pData[11] = 0x88;
    memset(&pData[12], 0x00, sizeof(char) * 4);
    nSize = 16;
}

void DigitizerInstruction::SwitchToUMA(char *pData, int &nSize)
{
    pData[0] = 0xFF;
    pData[1] = 0xFF;
    pData[2] = 0x65;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::SwitchToTOF(char *pData, int &nSize)
{
    pData[0] = 0x00;
    pData[1] = 0x00;
    pData[2] = 0x65;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}



void DigitizerInstruction::BaseLineParam(char *pData, unsigned short uVal, int &nSize)
{
    pData[0] = uVal % 0xFF;
    pData[1] = (uVal >> 8) % 0xFF;
    pData[2] = 0x30;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}

void DigitizerInstruction::NoiseLevelParam(char *pData, unsigned short uVal, int &nSize)
{
    pData[0] = uVal % 0xFF;
    pData[1] = (uVal >> 8) % 0xFF;
    pData[2] = 0x31;
    pData[3] = 0x88;
    memset(&pData[4], 0x00, sizeof(char) * 4);
    nSize = 8;
}
