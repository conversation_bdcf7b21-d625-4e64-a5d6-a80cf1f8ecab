/********************************************************************************
** Form generated from reading UI file 'uiIonScanParamEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIIONSCANPARAMEDITOR_H
#define UI_UIIONSCANPARAMEDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiIonScanParamEditor
{
public:
    QWidget *uiIonScanParamEditorWidget;
    QVBoxLayout *verticalLayout;
    QGridLayout *gridLayout;
    QLabel *label_6;
    QLabel *label_IonMZ;
    QLineEdit *lineEdit_ionMZ;
    QLineEdit *lineEdit_scanSpeed_rOnly;
    QLineEdit *lineEdit_startMZ;
    QLabel *label_5;
    QLineEdit *lineEdit_endMZ;
    QLabel *label_scanSpeed;

    void setupUi(QWidget *uiIonScanParamEditor)
    {
        if (uiIonScanParamEditor->objectName().isEmpty())
            uiIonScanParamEditor->setObjectName(QString::fromUtf8("uiIonScanParamEditor"));
        uiIonScanParamEditor->resize(590, 435);
        uiIonScanParamEditorWidget = new QWidget(uiIonScanParamEditor);
        uiIonScanParamEditorWidget->setObjectName(QString::fromUtf8("uiIonScanParamEditorWidget"));
        uiIonScanParamEditorWidget->setGeometry(QRect(90, 90, 340, 179));
        verticalLayout = new QVBoxLayout(uiIonScanParamEditorWidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setHorizontalSpacing(5);
        gridLayout->setVerticalSpacing(10);
        gridLayout->setContentsMargins(-1, -1, 20, -1);
        label_6 = new QLabel(uiIonScanParamEditorWidget);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout->addWidget(label_6, 1, 0, 1, 1);

        label_IonMZ = new QLabel(uiIonScanParamEditorWidget);
        label_IonMZ->setObjectName(QString::fromUtf8("label_IonMZ"));
        label_IonMZ->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout->addWidget(label_IonMZ, 2, 0, 1, 1);

        lineEdit_ionMZ = new QLineEdit(uiIonScanParamEditorWidget);
        lineEdit_ionMZ->setObjectName(QString::fromUtf8("lineEdit_ionMZ"));

        gridLayout->addWidget(lineEdit_ionMZ, 2, 1, 1, 1);

        lineEdit_scanSpeed_rOnly = new QLineEdit(uiIonScanParamEditorWidget);
        lineEdit_scanSpeed_rOnly->setObjectName(QString::fromUtf8("lineEdit_scanSpeed_rOnly"));
        lineEdit_scanSpeed_rOnly->setReadOnly(true);

        gridLayout->addWidget(lineEdit_scanSpeed_rOnly, 3, 1, 1, 1);

        lineEdit_startMZ = new QLineEdit(uiIonScanParamEditorWidget);
        lineEdit_startMZ->setObjectName(QString::fromUtf8("lineEdit_startMZ"));

        gridLayout->addWidget(lineEdit_startMZ, 0, 1, 1, 1);

        label_5 = new QLabel(uiIonScanParamEditorWidget);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout->addWidget(label_5, 0, 0, 1, 1);

        lineEdit_endMZ = new QLineEdit(uiIonScanParamEditorWidget);
        lineEdit_endMZ->setObjectName(QString::fromUtf8("lineEdit_endMZ"));

        gridLayout->addWidget(lineEdit_endMZ, 1, 1, 1, 1);

        label_scanSpeed = new QLabel(uiIonScanParamEditorWidget);
        label_scanSpeed->setObjectName(QString::fromUtf8("label_scanSpeed"));
        label_scanSpeed->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        gridLayout->addWidget(label_scanSpeed, 3, 0, 1, 1);

        gridLayout->setColumnStretch(0, 3);

        verticalLayout->addLayout(gridLayout);


        retranslateUi(uiIonScanParamEditor);

        QMetaObject::connectSlotsByName(uiIonScanParamEditor);
    } // setupUi

    void retranslateUi(QWidget *uiIonScanParamEditor)
    {
        uiIonScanParamEditor->setWindowTitle(QApplication::translate("uiIonScanParamEditor", "Form", nullptr));
        label_6->setText(QApplication::translate("uiIonScanParamEditor", "End m/z:", nullptr));
        label_IonMZ->setText(QApplication::translate("uiIonScanParamEditor", "Precursor Ion m/z:", nullptr));
        label_5->setText(QApplication::translate("uiIonScanParamEditor", "Start m/z:", nullptr));
        lineEdit_endMZ->setText(QString());
        label_scanSpeed->setText(QApplication::translate("uiIonScanParamEditor", "Scan Speed(u/sec):", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiIonScanParamEditor: public Ui_uiIonScanParamEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIIONSCANPARAMEDITOR_H
