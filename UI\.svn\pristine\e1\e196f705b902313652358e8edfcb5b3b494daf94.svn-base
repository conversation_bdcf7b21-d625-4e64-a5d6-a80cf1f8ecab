#pragma once

#include <QWidget>
#include <QRegExp>

namespace Ui {
class ParameterEditor;
}

class ParameterEditor : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QString text READ text WRITE setText NOTIFY textChanged)
    Q_PROPERTY(QString label READ label WRITE setLabel NOTIFY labelChanged)
    Q_PROPERTY(QString unit READ unit WRITE setUnit NOTIFY unitChanged)
    Q_PROPERTY(double value READ value WRITE setValue NOTIFY valueChanged)

public:
    explicit ParameterEditor(QWidget *parent = nullptr);
    ~ParameterEditor();

    QString text() const
    {
        return m_text;
    }

    QString label() const
    {
        return m_label;
    }

    QString unit() const
    {
        return m_unit;
    }

    double value() const
    {
        return m_value;
    }
    void setModified(bool modify);

protected:


public slots:
    void setText(const QString& text);
    void setLabel(const QString& label);
    void setUnit(const QString& unit);
    void setValue(double value);
    void setRange(double min, double max);
    void setDecimals(int decimals);

private slots:
    void onTextEdited(const QString& text);

signals:
    void textChanged(QString text);
    void labelChanged(QString label);
    void unitChanged(QString unit);
    void valueChanged(double value);
    void textEdited();
    void sig_editingFinished();

private:
    Ui::ParameterEditor *ui;
    QRegExp m_rx;
    QString m_text;
    QString m_label;
    QString m_unit;
    double m_value;
    double m_min, m_max;
    int m_decimals;
};

