#pragma once

#include <QMouseEvent>
#include <QWidget>

class sClickWidget : public QWidget
{
    Q_OBJECT

public:
    QWidget* mWorkWidget= nullptr;
    explicit sClickWidget(QWidget *parent = nullptr);
    virtual ~sClickWidget(){}
    void setWorkWidget(QWidget *workWidget){
        mWorkWidget= workWidget;
    }

private:
    QWidget* mParent= nullptr;
    QPoint relativePos= QPoint(0,0);
    void mousePressEvent(QMouseEvent *event);
    void mouseReleaseEvent(QMouseEvent *event);
    void mouseMoveEvent(QMouseEvent *event);

signals:
    void clicked();
    void moved();
};

