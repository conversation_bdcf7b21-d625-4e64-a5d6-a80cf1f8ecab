/********************************************************************************
** Form generated from reading UI file 'uiMapSetMZ.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIMAPSETMZ_H
#define UI_UIMAPSETMZ_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "uiTools/mzvoltagetableeditor.h"

QT_BEGIN_NAMESPACE

class Ui_uiMapSetMZ
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_2;
    QTabWidget *UI_TABWIDGET_PARAM_SETMZ;
    mzVoltageTableEditor *UI_W_Q1Scan_SETMZ;
    QHBoxLayout *UI_LAYOUT_SCAN_SINGLEACQ;
    mzVoltageTableEditor *UI_W_Q3Scan_SETMZ;
    QHBoxLayout *UI_LAYOUT_Q3Scan_SETMZ;
    mzVoltageTableEditor *UI_W_ProductIonScan_SETMZ;
    QHBoxLayout *UI_LAYOUT_ProductIonScan_SETMZ;
    mzVoltageTableEditor *UI_W_PrecursorIonScan_SETMZ;
    QHBoxLayout *UI_LAYOUT_PrecursorIonScan_SETMZ;
    mzVoltageTableEditor *UI_W_NeutralLossScan_SETMZ;
    QHBoxLayout *UI_LAYOUT_NeutralLossScan_SETMZ;
    mzVoltageTableEditor *UI_W_Q1SIM_SETMZ;
    QHBoxLayout *UI_LAYOUT_Q1SIM_SETMZ;
    mzVoltageTableEditor *UI_W_Q3SIM_SETMZ;
    QHBoxLayout *UI_LAYOUT_Q3SIM_SETMZ;
    mzVoltageTableEditor *UI_W_MRM_SETMZ;
    QHBoxLayout *UI_LAYOUT_MRM_SETMZ;
    QTabWidget *tabWidget;
    QWidget *tab;
    QVBoxLayout *verticalLayout_2;
    QTableWidget *UI_TW_SET_SETMZ;
    QWidget *tab_2;
    QVBoxLayout *verticalLayout_3;
    QTableWidget *UI_TW_SET_SETMZ36;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QSpacerItem *horizontalSpacer;
    QPushButton *UI_PB_UPDATE_SETMZ;
    QPushButton *UI_PB_SAVE_SETMZ;

    void setupUi(QWidget *uiMapSetMZ)
    {
        if (uiMapSetMZ->objectName().isEmpty())
            uiMapSetMZ->setObjectName(QString::fromUtf8("uiMapSetMZ"));
        uiMapSetMZ->resize(1395, 561);
        verticalLayout = new QVBoxLayout(uiMapSetMZ);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        UI_TABWIDGET_PARAM_SETMZ = new QTabWidget(uiMapSetMZ);
        UI_TABWIDGET_PARAM_SETMZ->setObjectName(QString::fromUtf8("UI_TABWIDGET_PARAM_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->setMinimumSize(QSize(880, 0));
        UI_TABWIDGET_PARAM_SETMZ->setMaximumSize(QSize(16777215, 16777215));
        UI_W_Q1Scan_SETMZ = new mzVoltageTableEditor();
        UI_W_Q1Scan_SETMZ->setObjectName(QString::fromUtf8("UI_W_Q1Scan_SETMZ"));
        UI_LAYOUT_SCAN_SINGLEACQ = new QHBoxLayout(UI_W_Q1Scan_SETMZ);
        UI_LAYOUT_SCAN_SINGLEACQ->setObjectName(QString::fromUtf8("UI_LAYOUT_SCAN_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_Q1Scan_SETMZ, QString());
        UI_W_Q3Scan_SETMZ = new mzVoltageTableEditor();
        UI_W_Q3Scan_SETMZ->setObjectName(QString::fromUtf8("UI_W_Q3Scan_SETMZ"));
        UI_LAYOUT_Q3Scan_SETMZ = new QHBoxLayout(UI_W_Q3Scan_SETMZ);
        UI_LAYOUT_Q3Scan_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q3Scan_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_Q3Scan_SETMZ, QString());
        UI_W_ProductIonScan_SETMZ = new mzVoltageTableEditor();
        UI_W_ProductIonScan_SETMZ->setObjectName(QString::fromUtf8("UI_W_ProductIonScan_SETMZ"));
        UI_LAYOUT_ProductIonScan_SETMZ = new QHBoxLayout(UI_W_ProductIonScan_SETMZ);
        UI_LAYOUT_ProductIonScan_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_ProductIonScan_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_ProductIonScan_SETMZ, QString());
        UI_W_PrecursorIonScan_SETMZ = new mzVoltageTableEditor();
        UI_W_PrecursorIonScan_SETMZ->setObjectName(QString::fromUtf8("UI_W_PrecursorIonScan_SETMZ"));
        UI_LAYOUT_PrecursorIonScan_SETMZ = new QHBoxLayout(UI_W_PrecursorIonScan_SETMZ);
        UI_LAYOUT_PrecursorIonScan_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_PrecursorIonScan_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_PrecursorIonScan_SETMZ, QString());
        UI_W_NeutralLossScan_SETMZ = new mzVoltageTableEditor();
        UI_W_NeutralLossScan_SETMZ->setObjectName(QString::fromUtf8("UI_W_NeutralLossScan_SETMZ"));
        UI_LAYOUT_NeutralLossScan_SETMZ = new QHBoxLayout(UI_W_NeutralLossScan_SETMZ);
        UI_LAYOUT_NeutralLossScan_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_NeutralLossScan_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_NeutralLossScan_SETMZ, QString());
        UI_W_Q1SIM_SETMZ = new mzVoltageTableEditor();
        UI_W_Q1SIM_SETMZ->setObjectName(QString::fromUtf8("UI_W_Q1SIM_SETMZ"));
        UI_LAYOUT_Q1SIM_SETMZ = new QHBoxLayout(UI_W_Q1SIM_SETMZ);
        UI_LAYOUT_Q1SIM_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q1SIM_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_Q1SIM_SETMZ, QString());
        UI_W_Q3SIM_SETMZ = new mzVoltageTableEditor();
        UI_W_Q3SIM_SETMZ->setObjectName(QString::fromUtf8("UI_W_Q3SIM_SETMZ"));
        UI_LAYOUT_Q3SIM_SETMZ = new QHBoxLayout(UI_W_Q3SIM_SETMZ);
        UI_LAYOUT_Q3SIM_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_Q3SIM_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_Q3SIM_SETMZ, QString());
        UI_W_MRM_SETMZ = new mzVoltageTableEditor();
        UI_W_MRM_SETMZ->setObjectName(QString::fromUtf8("UI_W_MRM_SETMZ"));
        UI_LAYOUT_MRM_SETMZ = new QHBoxLayout(UI_W_MRM_SETMZ);
        UI_LAYOUT_MRM_SETMZ->setObjectName(QString::fromUtf8("UI_LAYOUT_MRM_SETMZ"));
        UI_TABWIDGET_PARAM_SETMZ->addTab(UI_W_MRM_SETMZ, QString());

        horizontalLayout_2->addWidget(UI_TABWIDGET_PARAM_SETMZ);

        tabWidget = new QTabWidget(uiMapSetMZ);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tabWidget->setMaximumSize(QSize(480, 16777215));
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        verticalLayout_2 = new QVBoxLayout(tab);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        UI_TW_SET_SETMZ = new QTableWidget(tab);
        if (UI_TW_SET_SETMZ->columnCount() < 3)
            UI_TW_SET_SETMZ->setColumnCount(3);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        if (UI_TW_SET_SETMZ->rowCount() < 12)
            UI_TW_SET_SETMZ->setRowCount(12);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(0, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(1, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(2, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(3, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(4, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(5, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(6, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(7, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(8, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(9, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(10, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setVerticalHeaderItem(11, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(0, 0, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(0, 1, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(0, 2, __qtablewidgetitem17);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(1, 0, __qtablewidgetitem18);
        QTableWidgetItem *__qtablewidgetitem19 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(1, 1, __qtablewidgetitem19);
        QTableWidgetItem *__qtablewidgetitem20 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(1, 2, __qtablewidgetitem20);
        QTableWidgetItem *__qtablewidgetitem21 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(2, 0, __qtablewidgetitem21);
        QTableWidgetItem *__qtablewidgetitem22 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(2, 1, __qtablewidgetitem22);
        QTableWidgetItem *__qtablewidgetitem23 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(2, 2, __qtablewidgetitem23);
        QTableWidgetItem *__qtablewidgetitem24 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(3, 0, __qtablewidgetitem24);
        QTableWidgetItem *__qtablewidgetitem25 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(3, 1, __qtablewidgetitem25);
        QTableWidgetItem *__qtablewidgetitem26 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(3, 2, __qtablewidgetitem26);
        QTableWidgetItem *__qtablewidgetitem27 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(4, 0, __qtablewidgetitem27);
        QTableWidgetItem *__qtablewidgetitem28 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(4, 1, __qtablewidgetitem28);
        QTableWidgetItem *__qtablewidgetitem29 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(4, 2, __qtablewidgetitem29);
        QTableWidgetItem *__qtablewidgetitem30 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(5, 0, __qtablewidgetitem30);
        QTableWidgetItem *__qtablewidgetitem31 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(5, 1, __qtablewidgetitem31);
        QTableWidgetItem *__qtablewidgetitem32 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(5, 2, __qtablewidgetitem32);
        QTableWidgetItem *__qtablewidgetitem33 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(6, 0, __qtablewidgetitem33);
        QTableWidgetItem *__qtablewidgetitem34 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(6, 1, __qtablewidgetitem34);
        QTableWidgetItem *__qtablewidgetitem35 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(6, 2, __qtablewidgetitem35);
        QTableWidgetItem *__qtablewidgetitem36 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(7, 0, __qtablewidgetitem36);
        QTableWidgetItem *__qtablewidgetitem37 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(7, 1, __qtablewidgetitem37);
        QTableWidgetItem *__qtablewidgetitem38 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(7, 2, __qtablewidgetitem38);
        QTableWidgetItem *__qtablewidgetitem39 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(8, 0, __qtablewidgetitem39);
        QTableWidgetItem *__qtablewidgetitem40 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(8, 1, __qtablewidgetitem40);
        QTableWidgetItem *__qtablewidgetitem41 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(8, 2, __qtablewidgetitem41);
        QTableWidgetItem *__qtablewidgetitem42 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(9, 0, __qtablewidgetitem42);
        QTableWidgetItem *__qtablewidgetitem43 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(9, 1, __qtablewidgetitem43);
        QTableWidgetItem *__qtablewidgetitem44 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(9, 2, __qtablewidgetitem44);
        QTableWidgetItem *__qtablewidgetitem45 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(10, 0, __qtablewidgetitem45);
        QTableWidgetItem *__qtablewidgetitem46 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(10, 1, __qtablewidgetitem46);
        QTableWidgetItem *__qtablewidgetitem47 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(10, 2, __qtablewidgetitem47);
        QTableWidgetItem *__qtablewidgetitem48 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(11, 0, __qtablewidgetitem48);
        QTableWidgetItem *__qtablewidgetitem49 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(11, 1, __qtablewidgetitem49);
        QTableWidgetItem *__qtablewidgetitem50 = new QTableWidgetItem();
        UI_TW_SET_SETMZ->setItem(11, 2, __qtablewidgetitem50);
        UI_TW_SET_SETMZ->setObjectName(QString::fromUtf8("UI_TW_SET_SETMZ"));
        UI_TW_SET_SETMZ->setMinimumSize(QSize(0, 0));
        UI_TW_SET_SETMZ->setMaximumSize(QSize(480, 16777215));

        verticalLayout_2->addWidget(UI_TW_SET_SETMZ);

        tabWidget->addTab(tab, QString());
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        verticalLayout_3 = new QVBoxLayout(tab_2);
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        UI_TW_SET_SETMZ36 = new QTableWidget(tab_2);
        if (UI_TW_SET_SETMZ36->columnCount() < 3)
            UI_TW_SET_SETMZ36->setColumnCount(3);
        QTableWidgetItem *__qtablewidgetitem51 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setHorizontalHeaderItem(0, __qtablewidgetitem51);
        QTableWidgetItem *__qtablewidgetitem52 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setHorizontalHeaderItem(1, __qtablewidgetitem52);
        QTableWidgetItem *__qtablewidgetitem53 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setHorizontalHeaderItem(2, __qtablewidgetitem53);
        if (UI_TW_SET_SETMZ36->rowCount() < 36)
            UI_TW_SET_SETMZ36->setRowCount(36);
        QTableWidgetItem *__qtablewidgetitem54 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(0, __qtablewidgetitem54);
        QTableWidgetItem *__qtablewidgetitem55 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(1, __qtablewidgetitem55);
        QTableWidgetItem *__qtablewidgetitem56 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(2, __qtablewidgetitem56);
        QTableWidgetItem *__qtablewidgetitem57 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(3, __qtablewidgetitem57);
        QTableWidgetItem *__qtablewidgetitem58 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(4, __qtablewidgetitem58);
        QTableWidgetItem *__qtablewidgetitem59 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(5, __qtablewidgetitem59);
        QTableWidgetItem *__qtablewidgetitem60 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(6, __qtablewidgetitem60);
        QTableWidgetItem *__qtablewidgetitem61 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(7, __qtablewidgetitem61);
        QTableWidgetItem *__qtablewidgetitem62 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(8, __qtablewidgetitem62);
        QTableWidgetItem *__qtablewidgetitem63 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(9, __qtablewidgetitem63);
        QTableWidgetItem *__qtablewidgetitem64 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(10, __qtablewidgetitem64);
        QTableWidgetItem *__qtablewidgetitem65 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(11, __qtablewidgetitem65);
        QTableWidgetItem *__qtablewidgetitem66 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(12, __qtablewidgetitem66);
        QTableWidgetItem *__qtablewidgetitem67 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(13, __qtablewidgetitem67);
        QTableWidgetItem *__qtablewidgetitem68 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(14, __qtablewidgetitem68);
        QTableWidgetItem *__qtablewidgetitem69 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(15, __qtablewidgetitem69);
        QTableWidgetItem *__qtablewidgetitem70 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(16, __qtablewidgetitem70);
        QTableWidgetItem *__qtablewidgetitem71 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(17, __qtablewidgetitem71);
        QTableWidgetItem *__qtablewidgetitem72 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(18, __qtablewidgetitem72);
        QTableWidgetItem *__qtablewidgetitem73 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(19, __qtablewidgetitem73);
        QTableWidgetItem *__qtablewidgetitem74 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(20, __qtablewidgetitem74);
        QTableWidgetItem *__qtablewidgetitem75 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(21, __qtablewidgetitem75);
        QTableWidgetItem *__qtablewidgetitem76 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(22, __qtablewidgetitem76);
        QTableWidgetItem *__qtablewidgetitem77 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(23, __qtablewidgetitem77);
        QTableWidgetItem *__qtablewidgetitem78 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(24, __qtablewidgetitem78);
        QTableWidgetItem *__qtablewidgetitem79 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(25, __qtablewidgetitem79);
        QTableWidgetItem *__qtablewidgetitem80 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(26, __qtablewidgetitem80);
        QTableWidgetItem *__qtablewidgetitem81 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(27, __qtablewidgetitem81);
        QTableWidgetItem *__qtablewidgetitem82 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(28, __qtablewidgetitem82);
        QTableWidgetItem *__qtablewidgetitem83 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(29, __qtablewidgetitem83);
        QTableWidgetItem *__qtablewidgetitem84 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(30, __qtablewidgetitem84);
        QTableWidgetItem *__qtablewidgetitem85 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(31, __qtablewidgetitem85);
        QTableWidgetItem *__qtablewidgetitem86 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(32, __qtablewidgetitem86);
        QTableWidgetItem *__qtablewidgetitem87 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(33, __qtablewidgetitem87);
        QTableWidgetItem *__qtablewidgetitem88 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(34, __qtablewidgetitem88);
        QTableWidgetItem *__qtablewidgetitem89 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setVerticalHeaderItem(35, __qtablewidgetitem89);
        QTableWidgetItem *__qtablewidgetitem90 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(0, 0, __qtablewidgetitem90);
        QTableWidgetItem *__qtablewidgetitem91 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(0, 1, __qtablewidgetitem91);
        QTableWidgetItem *__qtablewidgetitem92 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(0, 2, __qtablewidgetitem92);
        QTableWidgetItem *__qtablewidgetitem93 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(1, 0, __qtablewidgetitem93);
        QTableWidgetItem *__qtablewidgetitem94 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(1, 1, __qtablewidgetitem94);
        QTableWidgetItem *__qtablewidgetitem95 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(1, 2, __qtablewidgetitem95);
        QTableWidgetItem *__qtablewidgetitem96 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(2, 0, __qtablewidgetitem96);
        QTableWidgetItem *__qtablewidgetitem97 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(2, 1, __qtablewidgetitem97);
        QTableWidgetItem *__qtablewidgetitem98 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(2, 2, __qtablewidgetitem98);
        QTableWidgetItem *__qtablewidgetitem99 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(3, 0, __qtablewidgetitem99);
        QTableWidgetItem *__qtablewidgetitem100 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(3, 1, __qtablewidgetitem100);
        QTableWidgetItem *__qtablewidgetitem101 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(3, 2, __qtablewidgetitem101);
        QTableWidgetItem *__qtablewidgetitem102 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(4, 0, __qtablewidgetitem102);
        QTableWidgetItem *__qtablewidgetitem103 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(4, 1, __qtablewidgetitem103);
        QTableWidgetItem *__qtablewidgetitem104 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(4, 2, __qtablewidgetitem104);
        QTableWidgetItem *__qtablewidgetitem105 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(5, 0, __qtablewidgetitem105);
        QTableWidgetItem *__qtablewidgetitem106 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(5, 1, __qtablewidgetitem106);
        QTableWidgetItem *__qtablewidgetitem107 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(5, 2, __qtablewidgetitem107);
        QTableWidgetItem *__qtablewidgetitem108 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(6, 0, __qtablewidgetitem108);
        QTableWidgetItem *__qtablewidgetitem109 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(6, 1, __qtablewidgetitem109);
        QTableWidgetItem *__qtablewidgetitem110 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(6, 2, __qtablewidgetitem110);
        QTableWidgetItem *__qtablewidgetitem111 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(7, 0, __qtablewidgetitem111);
        QTableWidgetItem *__qtablewidgetitem112 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(7, 1, __qtablewidgetitem112);
        QTableWidgetItem *__qtablewidgetitem113 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(7, 2, __qtablewidgetitem113);
        QTableWidgetItem *__qtablewidgetitem114 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(8, 0, __qtablewidgetitem114);
        QTableWidgetItem *__qtablewidgetitem115 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(8, 1, __qtablewidgetitem115);
        QTableWidgetItem *__qtablewidgetitem116 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(8, 2, __qtablewidgetitem116);
        QTableWidgetItem *__qtablewidgetitem117 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(9, 0, __qtablewidgetitem117);
        QTableWidgetItem *__qtablewidgetitem118 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(9, 1, __qtablewidgetitem118);
        QTableWidgetItem *__qtablewidgetitem119 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(9, 2, __qtablewidgetitem119);
        QTableWidgetItem *__qtablewidgetitem120 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(10, 0, __qtablewidgetitem120);
        QTableWidgetItem *__qtablewidgetitem121 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(10, 1, __qtablewidgetitem121);
        QTableWidgetItem *__qtablewidgetitem122 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(10, 2, __qtablewidgetitem122);
        QTableWidgetItem *__qtablewidgetitem123 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(11, 0, __qtablewidgetitem123);
        QTableWidgetItem *__qtablewidgetitem124 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(11, 1, __qtablewidgetitem124);
        QTableWidgetItem *__qtablewidgetitem125 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(11, 2, __qtablewidgetitem125);
        QTableWidgetItem *__qtablewidgetitem126 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(12, 0, __qtablewidgetitem126);
        QTableWidgetItem *__qtablewidgetitem127 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(12, 1, __qtablewidgetitem127);
        QTableWidgetItem *__qtablewidgetitem128 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(12, 2, __qtablewidgetitem128);
        QTableWidgetItem *__qtablewidgetitem129 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(13, 0, __qtablewidgetitem129);
        QTableWidgetItem *__qtablewidgetitem130 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(13, 1, __qtablewidgetitem130);
        QTableWidgetItem *__qtablewidgetitem131 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(13, 2, __qtablewidgetitem131);
        QTableWidgetItem *__qtablewidgetitem132 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(14, 0, __qtablewidgetitem132);
        QTableWidgetItem *__qtablewidgetitem133 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(14, 1, __qtablewidgetitem133);
        QTableWidgetItem *__qtablewidgetitem134 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(14, 2, __qtablewidgetitem134);
        QTableWidgetItem *__qtablewidgetitem135 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(15, 0, __qtablewidgetitem135);
        QTableWidgetItem *__qtablewidgetitem136 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(15, 1, __qtablewidgetitem136);
        QTableWidgetItem *__qtablewidgetitem137 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(15, 2, __qtablewidgetitem137);
        QTableWidgetItem *__qtablewidgetitem138 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(16, 0, __qtablewidgetitem138);
        QTableWidgetItem *__qtablewidgetitem139 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(16, 1, __qtablewidgetitem139);
        QTableWidgetItem *__qtablewidgetitem140 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(16, 2, __qtablewidgetitem140);
        QTableWidgetItem *__qtablewidgetitem141 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(17, 0, __qtablewidgetitem141);
        QTableWidgetItem *__qtablewidgetitem142 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(17, 1, __qtablewidgetitem142);
        QTableWidgetItem *__qtablewidgetitem143 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(17, 2, __qtablewidgetitem143);
        QTableWidgetItem *__qtablewidgetitem144 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(18, 0, __qtablewidgetitem144);
        QTableWidgetItem *__qtablewidgetitem145 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(18, 1, __qtablewidgetitem145);
        QTableWidgetItem *__qtablewidgetitem146 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(18, 2, __qtablewidgetitem146);
        QTableWidgetItem *__qtablewidgetitem147 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(19, 0, __qtablewidgetitem147);
        QTableWidgetItem *__qtablewidgetitem148 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(19, 1, __qtablewidgetitem148);
        QTableWidgetItem *__qtablewidgetitem149 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(19, 2, __qtablewidgetitem149);
        QTableWidgetItem *__qtablewidgetitem150 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(20, 0, __qtablewidgetitem150);
        QTableWidgetItem *__qtablewidgetitem151 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(20, 1, __qtablewidgetitem151);
        QTableWidgetItem *__qtablewidgetitem152 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(20, 2, __qtablewidgetitem152);
        QTableWidgetItem *__qtablewidgetitem153 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(21, 0, __qtablewidgetitem153);
        QTableWidgetItem *__qtablewidgetitem154 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(21, 1, __qtablewidgetitem154);
        QTableWidgetItem *__qtablewidgetitem155 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(21, 2, __qtablewidgetitem155);
        QTableWidgetItem *__qtablewidgetitem156 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(22, 0, __qtablewidgetitem156);
        QTableWidgetItem *__qtablewidgetitem157 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(22, 1, __qtablewidgetitem157);
        QTableWidgetItem *__qtablewidgetitem158 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(22, 2, __qtablewidgetitem158);
        QTableWidgetItem *__qtablewidgetitem159 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(23, 0, __qtablewidgetitem159);
        QTableWidgetItem *__qtablewidgetitem160 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(23, 1, __qtablewidgetitem160);
        QTableWidgetItem *__qtablewidgetitem161 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(23, 2, __qtablewidgetitem161);
        QTableWidgetItem *__qtablewidgetitem162 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(24, 0, __qtablewidgetitem162);
        QTableWidgetItem *__qtablewidgetitem163 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(24, 1, __qtablewidgetitem163);
        QTableWidgetItem *__qtablewidgetitem164 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(24, 2, __qtablewidgetitem164);
        QTableWidgetItem *__qtablewidgetitem165 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(25, 0, __qtablewidgetitem165);
        QTableWidgetItem *__qtablewidgetitem166 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(25, 1, __qtablewidgetitem166);
        QTableWidgetItem *__qtablewidgetitem167 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(25, 2, __qtablewidgetitem167);
        QTableWidgetItem *__qtablewidgetitem168 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(26, 0, __qtablewidgetitem168);
        QTableWidgetItem *__qtablewidgetitem169 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(26, 1, __qtablewidgetitem169);
        QTableWidgetItem *__qtablewidgetitem170 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(26, 2, __qtablewidgetitem170);
        QTableWidgetItem *__qtablewidgetitem171 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(27, 0, __qtablewidgetitem171);
        QTableWidgetItem *__qtablewidgetitem172 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(27, 1, __qtablewidgetitem172);
        QTableWidgetItem *__qtablewidgetitem173 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(27, 2, __qtablewidgetitem173);
        QTableWidgetItem *__qtablewidgetitem174 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(28, 0, __qtablewidgetitem174);
        QTableWidgetItem *__qtablewidgetitem175 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(28, 1, __qtablewidgetitem175);
        QTableWidgetItem *__qtablewidgetitem176 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(28, 2, __qtablewidgetitem176);
        QTableWidgetItem *__qtablewidgetitem177 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(29, 0, __qtablewidgetitem177);
        QTableWidgetItem *__qtablewidgetitem178 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(29, 1, __qtablewidgetitem178);
        QTableWidgetItem *__qtablewidgetitem179 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(29, 2, __qtablewidgetitem179);
        QTableWidgetItem *__qtablewidgetitem180 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(30, 0, __qtablewidgetitem180);
        QTableWidgetItem *__qtablewidgetitem181 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(30, 1, __qtablewidgetitem181);
        QTableWidgetItem *__qtablewidgetitem182 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(30, 2, __qtablewidgetitem182);
        QTableWidgetItem *__qtablewidgetitem183 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(31, 0, __qtablewidgetitem183);
        QTableWidgetItem *__qtablewidgetitem184 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(31, 1, __qtablewidgetitem184);
        QTableWidgetItem *__qtablewidgetitem185 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(31, 2, __qtablewidgetitem185);
        QTableWidgetItem *__qtablewidgetitem186 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(32, 0, __qtablewidgetitem186);
        QTableWidgetItem *__qtablewidgetitem187 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(32, 1, __qtablewidgetitem187);
        QTableWidgetItem *__qtablewidgetitem188 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(32, 2, __qtablewidgetitem188);
        QTableWidgetItem *__qtablewidgetitem189 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(33, 0, __qtablewidgetitem189);
        QTableWidgetItem *__qtablewidgetitem190 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(33, 1, __qtablewidgetitem190);
        QTableWidgetItem *__qtablewidgetitem191 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(33, 2, __qtablewidgetitem191);
        QTableWidgetItem *__qtablewidgetitem192 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(34, 0, __qtablewidgetitem192);
        QTableWidgetItem *__qtablewidgetitem193 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(34, 1, __qtablewidgetitem193);
        QTableWidgetItem *__qtablewidgetitem194 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(34, 2, __qtablewidgetitem194);
        QTableWidgetItem *__qtablewidgetitem195 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(35, 0, __qtablewidgetitem195);
        QTableWidgetItem *__qtablewidgetitem196 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(35, 1, __qtablewidgetitem196);
        QTableWidgetItem *__qtablewidgetitem197 = new QTableWidgetItem();
        UI_TW_SET_SETMZ36->setItem(35, 2, __qtablewidgetitem197);
        UI_TW_SET_SETMZ36->setObjectName(QString::fromUtf8("UI_TW_SET_SETMZ36"));
        UI_TW_SET_SETMZ36->setMinimumSize(QSize(0, 0));
        UI_TW_SET_SETMZ36->setMaximumSize(QSize(480, 16777215));

        verticalLayout_3->addWidget(UI_TW_SET_SETMZ36);

        tabWidget->addTab(tab_2, QString());

        horizontalLayout_2->addWidget(tabWidget);


        verticalLayout->addLayout(horizontalLayout_2);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label = new QLabel(uiMapSetMZ);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout->addWidget(label);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        UI_PB_UPDATE_SETMZ = new QPushButton(uiMapSetMZ);
        UI_PB_UPDATE_SETMZ->setObjectName(QString::fromUtf8("UI_PB_UPDATE_SETMZ"));

        horizontalLayout->addWidget(UI_PB_UPDATE_SETMZ);

        UI_PB_SAVE_SETMZ = new QPushButton(uiMapSetMZ);
        UI_PB_SAVE_SETMZ->setObjectName(QString::fromUtf8("UI_PB_SAVE_SETMZ"));

        horizontalLayout->addWidget(UI_PB_SAVE_SETMZ);


        verticalLayout->addLayout(horizontalLayout);


        retranslateUi(uiMapSetMZ);

        UI_TABWIDGET_PARAM_SETMZ->setCurrentIndex(0);
        tabWidget->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(uiMapSetMZ);
    } // setupUi

    void retranslateUi(QWidget *uiMapSetMZ)
    {
        uiMapSetMZ->setWindowTitle(QApplication::translate("uiMapSetMZ", "Form", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_Q1Scan_SETMZ), QApplication::translate("uiMapSetMZ", "Q1Scan", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_Q3Scan_SETMZ), QApplication::translate("uiMapSetMZ", "Q3Scan", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_ProductIonScan_SETMZ), QApplication::translate("uiMapSetMZ", "ProductIonScan", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_PrecursorIonScan_SETMZ), QApplication::translate("uiMapSetMZ", "PrecursorIonScan", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_NeutralLossScan_SETMZ), QApplication::translate("uiMapSetMZ", "NeutralLossScan", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_Q1SIM_SETMZ), QApplication::translate("uiMapSetMZ", "Q1 SIM", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_Q3SIM_SETMZ), QApplication::translate("uiMapSetMZ", "Q3 SIM", nullptr));
        UI_TABWIDGET_PARAM_SETMZ->setTabText(UI_TABWIDGET_PARAM_SETMZ->indexOf(UI_W_MRM_SETMZ), QApplication::translate("uiMapSetMZ", "MRM", nullptr));
        QTableWidgetItem *___qtablewidgetitem = UI_TW_SET_SETMZ->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("uiMapSetMZ", "Name", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = UI_TW_SET_SETMZ->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("uiMapSetMZ", "Gain", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = UI_TW_SET_SETMZ->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("uiMapSetMZ", "Offset", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = UI_TW_SET_SETMZ->verticalHeaderItem(0);
        ___qtablewidgetitem3->setText(QApplication::translate("uiMapSetMZ", "HDAC1", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = UI_TW_SET_SETMZ->verticalHeaderItem(1);
        ___qtablewidgetitem4->setText(QApplication::translate("uiMapSetMZ", "HDAC2", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = UI_TW_SET_SETMZ->verticalHeaderItem(2);
        ___qtablewidgetitem5->setText(QApplication::translate("uiMapSetMZ", "HDAC3", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = UI_TW_SET_SETMZ->verticalHeaderItem(3);
        ___qtablewidgetitem6->setText(QApplication::translate("uiMapSetMZ", "HDAC4", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = UI_TW_SET_SETMZ->verticalHeaderItem(4);
        ___qtablewidgetitem7->setText(QApplication::translate("uiMapSetMZ", "HDAC5", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = UI_TW_SET_SETMZ->verticalHeaderItem(5);
        ___qtablewidgetitem8->setText(QApplication::translate("uiMapSetMZ", "HDAC6", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = UI_TW_SET_SETMZ->verticalHeaderItem(6);
        ___qtablewidgetitem9->setText(QApplication::translate("uiMapSetMZ", "HDAC7", nullptr));
        QTableWidgetItem *___qtablewidgetitem10 = UI_TW_SET_SETMZ->verticalHeaderItem(7);
        ___qtablewidgetitem10->setText(QApplication::translate("uiMapSetMZ", "HDAC8", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = UI_TW_SET_SETMZ->verticalHeaderItem(8);
        ___qtablewidgetitem11->setText(QApplication::translate("uiMapSetMZ", "HDAC9", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = UI_TW_SET_SETMZ->verticalHeaderItem(9);
        ___qtablewidgetitem12->setText(QApplication::translate("uiMapSetMZ", "HDAC10", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = UI_TW_SET_SETMZ->verticalHeaderItem(10);
        ___qtablewidgetitem13->setText(QApplication::translate("uiMapSetMZ", "HDAC11", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = UI_TW_SET_SETMZ->verticalHeaderItem(11);
        ___qtablewidgetitem14->setText(QApplication::translate("uiMapSetMZ", "HDAC12", nullptr));

        const bool __sortingEnabled = UI_TW_SET_SETMZ->isSortingEnabled();
        UI_TW_SET_SETMZ->setSortingEnabled(false);
        QTableWidgetItem *___qtablewidgetitem15 = UI_TW_SET_SETMZ->item(0, 0);
        ___qtablewidgetitem15->setText(QApplication::translate("uiMapSetMZ", "IG0RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = UI_TW_SET_SETMZ->item(0, 1);
        ___qtablewidgetitem16->setText(QApplication::translate("uiMapSetMZ", "30", nullptr));
        QTableWidgetItem *___qtablewidgetitem17 = UI_TW_SET_SETMZ->item(0, 2);
        ___qtablewidgetitem17->setText(QApplication::translate("uiMapSetMZ", "-5.8553", nullptr));
        QTableWidgetItem *___qtablewidgetitem18 = UI_TW_SET_SETMZ->item(1, 0);
        ___qtablewidgetitem18->setText(QApplication::translate("uiMapSetMZ", "IG12RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem19 = UI_TW_SET_SETMZ->item(1, 1);
        ___qtablewidgetitem19->setText(QApplication::translate("uiMapSetMZ", "60", nullptr));
        QTableWidgetItem *___qtablewidgetitem20 = UI_TW_SET_SETMZ->item(1, 2);
        ___qtablewidgetitem20->setText(QApplication::translate("uiMapSetMZ", "-6", nullptr));
        QTableWidgetItem *___qtablewidgetitem21 = UI_TW_SET_SETMZ->item(2, 0);
        ___qtablewidgetitem21->setText(QApplication::translate("uiMapSetMZ", "Q1PRE", nullptr));
        QTableWidgetItem *___qtablewidgetitem22 = UI_TW_SET_SETMZ->item(2, 1);
        ___qtablewidgetitem22->setText(QApplication::translate("uiMapSetMZ", "-32.2", nullptr));
        QTableWidgetItem *___qtablewidgetitem23 = UI_TW_SET_SETMZ->item(2, 2);
        ___qtablewidgetitem23->setText(QApplication::translate("uiMapSetMZ", "-0.133", nullptr));
        QTableWidgetItem *___qtablewidgetitem24 = UI_TW_SET_SETMZ->item(3, 0);
        ___qtablewidgetitem24->setText(QApplication::translate("uiMapSetMZ", "Q1MAIN", nullptr));
        QTableWidgetItem *___qtablewidgetitem25 = UI_TW_SET_SETMZ->item(3, 1);
        ___qtablewidgetitem25->setText(QApplication::translate("uiMapSetMZ", "-23.895", nullptr));
        QTableWidgetItem *___qtablewidgetitem26 = UI_TW_SET_SETMZ->item(3, 2);
        ___qtablewidgetitem26->setText(QApplication::translate("uiMapSetMZ", "0.033", nullptr));
        QTableWidgetItem *___qtablewidgetitem27 = UI_TW_SET_SETMZ->item(4, 0);
        ___qtablewidgetitem27->setText(QApplication::translate("uiMapSetMZ", "CCRF", nullptr));
        QTableWidgetItem *___qtablewidgetitem28 = UI_TW_SET_SETMZ->item(4, 1);
        ___qtablewidgetitem28->setText(QApplication::translate("uiMapSetMZ", "60", nullptr));
        QTableWidgetItem *___qtablewidgetitem29 = UI_TW_SET_SETMZ->item(4, 2);
        ___qtablewidgetitem29->setText(QApplication::translate("uiMapSetMZ", "-4", nullptr));
        QTableWidgetItem *___qtablewidgetitem30 = UI_TW_SET_SETMZ->item(5, 0);
        ___qtablewidgetitem30->setText(QApplication::translate("uiMapSetMZ", "Q1RESO", nullptr));
        QTableWidgetItem *___qtablewidgetitem31 = UI_TW_SET_SETMZ->item(5, 1);
        ___qtablewidgetitem31->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem32 = UI_TW_SET_SETMZ->item(5, 2);
        ___qtablewidgetitem32->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem33 = UI_TW_SET_SETMZ->item(6, 0);
        ___qtablewidgetitem33->setText(QApplication::translate("uiMapSetMZ", "CCBIAS", nullptr));
        QTableWidgetItem *___qtablewidgetitem34 = UI_TW_SET_SETMZ->item(6, 1);
        ___qtablewidgetitem34->setText(QApplication::translate("uiMapSetMZ", "-32.155", nullptr));
        QTableWidgetItem *___qtablewidgetitem35 = UI_TW_SET_SETMZ->item(6, 2);
        ___qtablewidgetitem35->setText(QApplication::translate("uiMapSetMZ", "-0.0133", nullptr));
        QTableWidgetItem *___qtablewidgetitem36 = UI_TW_SET_SETMZ->item(7, 0);
        ___qtablewidgetitem36->setText(QApplication::translate("uiMapSetMZ", "Q1RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem37 = UI_TW_SET_SETMZ->item(7, 1);
        ___qtablewidgetitem37->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem38 = UI_TW_SET_SETMZ->item(7, 2);
        ___qtablewidgetitem38->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem39 = UI_TW_SET_SETMZ->item(8, 0);
        ___qtablewidgetitem39->setText(QApplication::translate("uiMapSetMZ", "Q3PRE", nullptr));
        QTableWidgetItem *___qtablewidgetitem40 = UI_TW_SET_SETMZ->item(8, 1);
        ___qtablewidgetitem40->setText(QApplication::translate("uiMapSetMZ", "-32.115", nullptr));
        QTableWidgetItem *___qtablewidgetitem41 = UI_TW_SET_SETMZ->item(8, 2);
        ___qtablewidgetitem41->setText(QApplication::translate("uiMapSetMZ", "0.16", nullptr));
        QTableWidgetItem *___qtablewidgetitem42 = UI_TW_SET_SETMZ->item(9, 0);
        ___qtablewidgetitem42->setText(QApplication::translate("uiMapSetMZ", "Q3MAIN", nullptr));
        QTableWidgetItem *___qtablewidgetitem43 = UI_TW_SET_SETMZ->item(9, 1);
        ___qtablewidgetitem43->setText(QApplication::translate("uiMapSetMZ", "-23.875", nullptr));
        QTableWidgetItem *___qtablewidgetitem44 = UI_TW_SET_SETMZ->item(9, 2);
        ___qtablewidgetitem44->setText(QApplication::translate("uiMapSetMZ", "0.0267", nullptr));
        QTableWidgetItem *___qtablewidgetitem45 = UI_TW_SET_SETMZ->item(10, 0);
        ___qtablewidgetitem45->setText(QApplication::translate("uiMapSetMZ", "Q3RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem46 = UI_TW_SET_SETMZ->item(10, 1);
        ___qtablewidgetitem46->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem47 = UI_TW_SET_SETMZ->item(10, 2);
        ___qtablewidgetitem47->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem48 = UI_TW_SET_SETMZ->item(11, 0);
        ___qtablewidgetitem48->setText(QApplication::translate("uiMapSetMZ", "Q3RESO", nullptr));
        QTableWidgetItem *___qtablewidgetitem49 = UI_TW_SET_SETMZ->item(11, 1);
        ___qtablewidgetitem49->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem50 = UI_TW_SET_SETMZ->item(11, 2);
        ___qtablewidgetitem50->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        UI_TW_SET_SETMZ->setSortingEnabled(__sortingEnabled);

        tabWidget->setTabText(tabWidget->indexOf(tab), QApplication::translate("uiMapSetMZ", "HDAC", nullptr));
        QTableWidgetItem *___qtablewidgetitem51 = UI_TW_SET_SETMZ36->horizontalHeaderItem(0);
        ___qtablewidgetitem51->setText(QApplication::translate("uiMapSetMZ", "Name", nullptr));
        QTableWidgetItem *___qtablewidgetitem52 = UI_TW_SET_SETMZ36->horizontalHeaderItem(1);
        ___qtablewidgetitem52->setText(QApplication::translate("uiMapSetMZ", "Gain", nullptr));
        QTableWidgetItem *___qtablewidgetitem53 = UI_TW_SET_SETMZ36->horizontalHeaderItem(2);
        ___qtablewidgetitem53->setText(QApplication::translate("uiMapSetMZ", "Offset", nullptr));
        QTableWidgetItem *___qtablewidgetitem54 = UI_TW_SET_SETMZ36->verticalHeaderItem(0);
        ___qtablewidgetitem54->setText(QApplication::translate("uiMapSetMZ", "LDAC1", nullptr));
        QTableWidgetItem *___qtablewidgetitem55 = UI_TW_SET_SETMZ36->verticalHeaderItem(1);
        ___qtablewidgetitem55->setText(QApplication::translate("uiMapSetMZ", "LDAC2", nullptr));
        QTableWidgetItem *___qtablewidgetitem56 = UI_TW_SET_SETMZ36->verticalHeaderItem(2);
        ___qtablewidgetitem56->setText(QApplication::translate("uiMapSetMZ", "LDAC3", nullptr));
        QTableWidgetItem *___qtablewidgetitem57 = UI_TW_SET_SETMZ36->verticalHeaderItem(3);
        ___qtablewidgetitem57->setText(QApplication::translate("uiMapSetMZ", "LDAC4", nullptr));
        QTableWidgetItem *___qtablewidgetitem58 = UI_TW_SET_SETMZ36->verticalHeaderItem(4);
        ___qtablewidgetitem58->setText(QApplication::translate("uiMapSetMZ", "LDAC5", nullptr));
        QTableWidgetItem *___qtablewidgetitem59 = UI_TW_SET_SETMZ36->verticalHeaderItem(5);
        ___qtablewidgetitem59->setText(QApplication::translate("uiMapSetMZ", "LDAC6", nullptr));
        QTableWidgetItem *___qtablewidgetitem60 = UI_TW_SET_SETMZ36->verticalHeaderItem(6);
        ___qtablewidgetitem60->setText(QApplication::translate("uiMapSetMZ", "LDAC7", nullptr));
        QTableWidgetItem *___qtablewidgetitem61 = UI_TW_SET_SETMZ36->verticalHeaderItem(7);
        ___qtablewidgetitem61->setText(QApplication::translate("uiMapSetMZ", "LDAC8", nullptr));
        QTableWidgetItem *___qtablewidgetitem62 = UI_TW_SET_SETMZ36->verticalHeaderItem(8);
        ___qtablewidgetitem62->setText(QApplication::translate("uiMapSetMZ", "LDAC9", nullptr));
        QTableWidgetItem *___qtablewidgetitem63 = UI_TW_SET_SETMZ36->verticalHeaderItem(9);
        ___qtablewidgetitem63->setText(QApplication::translate("uiMapSetMZ", "LDAC10", nullptr));
        QTableWidgetItem *___qtablewidgetitem64 = UI_TW_SET_SETMZ36->verticalHeaderItem(10);
        ___qtablewidgetitem64->setText(QApplication::translate("uiMapSetMZ", "LDAC11", nullptr));
        QTableWidgetItem *___qtablewidgetitem65 = UI_TW_SET_SETMZ36->verticalHeaderItem(11);
        ___qtablewidgetitem65->setText(QApplication::translate("uiMapSetMZ", "LDAC12", nullptr));
        QTableWidgetItem *___qtablewidgetitem66 = UI_TW_SET_SETMZ36->verticalHeaderItem(12);
        ___qtablewidgetitem66->setText(QApplication::translate("uiMapSetMZ", "LDAC13", nullptr));
        QTableWidgetItem *___qtablewidgetitem67 = UI_TW_SET_SETMZ36->verticalHeaderItem(13);
        ___qtablewidgetitem67->setText(QApplication::translate("uiMapSetMZ", "LDAC14", nullptr));
        QTableWidgetItem *___qtablewidgetitem68 = UI_TW_SET_SETMZ36->verticalHeaderItem(14);
        ___qtablewidgetitem68->setText(QApplication::translate("uiMapSetMZ", "LDAC15", nullptr));
        QTableWidgetItem *___qtablewidgetitem69 = UI_TW_SET_SETMZ36->verticalHeaderItem(15);
        ___qtablewidgetitem69->setText(QApplication::translate("uiMapSetMZ", "LDAC16", nullptr));
        QTableWidgetItem *___qtablewidgetitem70 = UI_TW_SET_SETMZ36->verticalHeaderItem(16);
        ___qtablewidgetitem70->setText(QApplication::translate("uiMapSetMZ", "LDAC17", nullptr));
        QTableWidgetItem *___qtablewidgetitem71 = UI_TW_SET_SETMZ36->verticalHeaderItem(17);
        ___qtablewidgetitem71->setText(QApplication::translate("uiMapSetMZ", "LDAC18", nullptr));
        QTableWidgetItem *___qtablewidgetitem72 = UI_TW_SET_SETMZ36->verticalHeaderItem(18);
        ___qtablewidgetitem72->setText(QApplication::translate("uiMapSetMZ", "LDAC19", nullptr));
        QTableWidgetItem *___qtablewidgetitem73 = UI_TW_SET_SETMZ36->verticalHeaderItem(19);
        ___qtablewidgetitem73->setText(QApplication::translate("uiMapSetMZ", "LDAC20", nullptr));
        QTableWidgetItem *___qtablewidgetitem74 = UI_TW_SET_SETMZ36->verticalHeaderItem(20);
        ___qtablewidgetitem74->setText(QApplication::translate("uiMapSetMZ", "LDAC21", nullptr));
        QTableWidgetItem *___qtablewidgetitem75 = UI_TW_SET_SETMZ36->verticalHeaderItem(21);
        ___qtablewidgetitem75->setText(QApplication::translate("uiMapSetMZ", "LDAC22", nullptr));
        QTableWidgetItem *___qtablewidgetitem76 = UI_TW_SET_SETMZ36->verticalHeaderItem(22);
        ___qtablewidgetitem76->setText(QApplication::translate("uiMapSetMZ", "LDAC23", nullptr));
        QTableWidgetItem *___qtablewidgetitem77 = UI_TW_SET_SETMZ36->verticalHeaderItem(23);
        ___qtablewidgetitem77->setText(QApplication::translate("uiMapSetMZ", "LDAC24", nullptr));
        QTableWidgetItem *___qtablewidgetitem78 = UI_TW_SET_SETMZ36->verticalHeaderItem(24);
        ___qtablewidgetitem78->setText(QApplication::translate("uiMapSetMZ", "LDAC25", nullptr));
        QTableWidgetItem *___qtablewidgetitem79 = UI_TW_SET_SETMZ36->verticalHeaderItem(25);
        ___qtablewidgetitem79->setText(QApplication::translate("uiMapSetMZ", "LDAC26", nullptr));
        QTableWidgetItem *___qtablewidgetitem80 = UI_TW_SET_SETMZ36->verticalHeaderItem(26);
        ___qtablewidgetitem80->setText(QApplication::translate("uiMapSetMZ", "LDAC27", nullptr));
        QTableWidgetItem *___qtablewidgetitem81 = UI_TW_SET_SETMZ36->verticalHeaderItem(27);
        ___qtablewidgetitem81->setText(QApplication::translate("uiMapSetMZ", "LDAC28", nullptr));
        QTableWidgetItem *___qtablewidgetitem82 = UI_TW_SET_SETMZ36->verticalHeaderItem(28);
        ___qtablewidgetitem82->setText(QApplication::translate("uiMapSetMZ", "LDAC29", nullptr));
        QTableWidgetItem *___qtablewidgetitem83 = UI_TW_SET_SETMZ36->verticalHeaderItem(29);
        ___qtablewidgetitem83->setText(QApplication::translate("uiMapSetMZ", "LDAC30", nullptr));
        QTableWidgetItem *___qtablewidgetitem84 = UI_TW_SET_SETMZ36->verticalHeaderItem(30);
        ___qtablewidgetitem84->setText(QApplication::translate("uiMapSetMZ", "LDAC31", nullptr));
        QTableWidgetItem *___qtablewidgetitem85 = UI_TW_SET_SETMZ36->verticalHeaderItem(31);
        ___qtablewidgetitem85->setText(QApplication::translate("uiMapSetMZ", "LDAC32", nullptr));
        QTableWidgetItem *___qtablewidgetitem86 = UI_TW_SET_SETMZ36->verticalHeaderItem(32);
        ___qtablewidgetitem86->setText(QApplication::translate("uiMapSetMZ", "LDAC33", nullptr));
        QTableWidgetItem *___qtablewidgetitem87 = UI_TW_SET_SETMZ36->verticalHeaderItem(33);
        ___qtablewidgetitem87->setText(QApplication::translate("uiMapSetMZ", "LDAC34", nullptr));
        QTableWidgetItem *___qtablewidgetitem88 = UI_TW_SET_SETMZ36->verticalHeaderItem(34);
        ___qtablewidgetitem88->setText(QApplication::translate("uiMapSetMZ", "LDAC35", nullptr));
        QTableWidgetItem *___qtablewidgetitem89 = UI_TW_SET_SETMZ36->verticalHeaderItem(35);
        ___qtablewidgetitem89->setText(QApplication::translate("uiMapSetMZ", "LDAC36", nullptr));

        const bool __sortingEnabled1 = UI_TW_SET_SETMZ36->isSortingEnabled();
        UI_TW_SET_SETMZ36->setSortingEnabled(false);
        QTableWidgetItem *___qtablewidgetitem90 = UI_TW_SET_SETMZ36->item(0, 0);
        ___qtablewidgetitem90->setText(QApplication::translate("uiMapSetMZ", "IG0RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem91 = UI_TW_SET_SETMZ36->item(0, 1);
        ___qtablewidgetitem91->setText(QApplication::translate("uiMapSetMZ", "30", nullptr));
        QTableWidgetItem *___qtablewidgetitem92 = UI_TW_SET_SETMZ36->item(0, 2);
        ___qtablewidgetitem92->setText(QApplication::translate("uiMapSetMZ", "-5.8553", nullptr));
        QTableWidgetItem *___qtablewidgetitem93 = UI_TW_SET_SETMZ36->item(1, 0);
        ___qtablewidgetitem93->setText(QApplication::translate("uiMapSetMZ", "IG12RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem94 = UI_TW_SET_SETMZ36->item(1, 1);
        ___qtablewidgetitem94->setText(QApplication::translate("uiMapSetMZ", "60", nullptr));
        QTableWidgetItem *___qtablewidgetitem95 = UI_TW_SET_SETMZ36->item(1, 2);
        ___qtablewidgetitem95->setText(QApplication::translate("uiMapSetMZ", "-6", nullptr));
        QTableWidgetItem *___qtablewidgetitem96 = UI_TW_SET_SETMZ36->item(2, 0);
        ___qtablewidgetitem96->setText(QApplication::translate("uiMapSetMZ", "Q1PRE", nullptr));
        QTableWidgetItem *___qtablewidgetitem97 = UI_TW_SET_SETMZ36->item(2, 1);
        ___qtablewidgetitem97->setText(QApplication::translate("uiMapSetMZ", "-32.2", nullptr));
        QTableWidgetItem *___qtablewidgetitem98 = UI_TW_SET_SETMZ36->item(2, 2);
        ___qtablewidgetitem98->setText(QApplication::translate("uiMapSetMZ", "-0.133", nullptr));
        QTableWidgetItem *___qtablewidgetitem99 = UI_TW_SET_SETMZ36->item(3, 0);
        ___qtablewidgetitem99->setText(QApplication::translate("uiMapSetMZ", "Q1MAIN", nullptr));
        QTableWidgetItem *___qtablewidgetitem100 = UI_TW_SET_SETMZ36->item(3, 1);
        ___qtablewidgetitem100->setText(QApplication::translate("uiMapSetMZ", "-23.895", nullptr));
        QTableWidgetItem *___qtablewidgetitem101 = UI_TW_SET_SETMZ36->item(3, 2);
        ___qtablewidgetitem101->setText(QApplication::translate("uiMapSetMZ", "0.033", nullptr));
        QTableWidgetItem *___qtablewidgetitem102 = UI_TW_SET_SETMZ36->item(4, 0);
        ___qtablewidgetitem102->setText(QApplication::translate("uiMapSetMZ", "CCRF", nullptr));
        QTableWidgetItem *___qtablewidgetitem103 = UI_TW_SET_SETMZ36->item(4, 1);
        ___qtablewidgetitem103->setText(QApplication::translate("uiMapSetMZ", "60", nullptr));
        QTableWidgetItem *___qtablewidgetitem104 = UI_TW_SET_SETMZ36->item(4, 2);
        ___qtablewidgetitem104->setText(QApplication::translate("uiMapSetMZ", "-4", nullptr));
        QTableWidgetItem *___qtablewidgetitem105 = UI_TW_SET_SETMZ36->item(5, 0);
        ___qtablewidgetitem105->setText(QApplication::translate("uiMapSetMZ", "Q1RESO", nullptr));
        QTableWidgetItem *___qtablewidgetitem106 = UI_TW_SET_SETMZ36->item(5, 1);
        ___qtablewidgetitem106->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem107 = UI_TW_SET_SETMZ36->item(5, 2);
        ___qtablewidgetitem107->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem108 = UI_TW_SET_SETMZ36->item(6, 0);
        ___qtablewidgetitem108->setText(QApplication::translate("uiMapSetMZ", "CCBIAS", nullptr));
        QTableWidgetItem *___qtablewidgetitem109 = UI_TW_SET_SETMZ36->item(6, 1);
        ___qtablewidgetitem109->setText(QApplication::translate("uiMapSetMZ", "-32.155", nullptr));
        QTableWidgetItem *___qtablewidgetitem110 = UI_TW_SET_SETMZ36->item(6, 2);
        ___qtablewidgetitem110->setText(QApplication::translate("uiMapSetMZ", "-0.0133", nullptr));
        QTableWidgetItem *___qtablewidgetitem111 = UI_TW_SET_SETMZ36->item(7, 0);
        ___qtablewidgetitem111->setText(QApplication::translate("uiMapSetMZ", "Q1RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem112 = UI_TW_SET_SETMZ36->item(7, 1);
        ___qtablewidgetitem112->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem113 = UI_TW_SET_SETMZ36->item(7, 2);
        ___qtablewidgetitem113->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem114 = UI_TW_SET_SETMZ36->item(8, 0);
        ___qtablewidgetitem114->setText(QApplication::translate("uiMapSetMZ", "Q3PRE", nullptr));
        QTableWidgetItem *___qtablewidgetitem115 = UI_TW_SET_SETMZ36->item(8, 1);
        ___qtablewidgetitem115->setText(QApplication::translate("uiMapSetMZ", "-32.115", nullptr));
        QTableWidgetItem *___qtablewidgetitem116 = UI_TW_SET_SETMZ36->item(8, 2);
        ___qtablewidgetitem116->setText(QApplication::translate("uiMapSetMZ", "0.16", nullptr));
        QTableWidgetItem *___qtablewidgetitem117 = UI_TW_SET_SETMZ36->item(9, 0);
        ___qtablewidgetitem117->setText(QApplication::translate("uiMapSetMZ", "Q3MAIN", nullptr));
        QTableWidgetItem *___qtablewidgetitem118 = UI_TW_SET_SETMZ36->item(9, 1);
        ___qtablewidgetitem118->setText(QApplication::translate("uiMapSetMZ", "-23.875", nullptr));
        QTableWidgetItem *___qtablewidgetitem119 = UI_TW_SET_SETMZ36->item(9, 2);
        ___qtablewidgetitem119->setText(QApplication::translate("uiMapSetMZ", "0.0267", nullptr));
        QTableWidgetItem *___qtablewidgetitem120 = UI_TW_SET_SETMZ36->item(10, 0);
        ___qtablewidgetitem120->setText(QApplication::translate("uiMapSetMZ", "Q3RF", nullptr));
        QTableWidgetItem *___qtablewidgetitem121 = UI_TW_SET_SETMZ36->item(10, 1);
        ___qtablewidgetitem121->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem122 = UI_TW_SET_SETMZ36->item(10, 2);
        ___qtablewidgetitem122->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem123 = UI_TW_SET_SETMZ36->item(11, 0);
        ___qtablewidgetitem123->setText(QApplication::translate("uiMapSetMZ", "Q3RESO", nullptr));
        QTableWidgetItem *___qtablewidgetitem124 = UI_TW_SET_SETMZ36->item(11, 1);
        ___qtablewidgetitem124->setText(QApplication::translate("uiMapSetMZ", "1", nullptr));
        QTableWidgetItem *___qtablewidgetitem125 = UI_TW_SET_SETMZ36->item(11, 2);
        ___qtablewidgetitem125->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem126 = UI_TW_SET_SETMZ36->item(12, 0);
        ___qtablewidgetitem126->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem127 = UI_TW_SET_SETMZ36->item(12, 1);
        ___qtablewidgetitem127->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem128 = UI_TW_SET_SETMZ36->item(12, 2);
        ___qtablewidgetitem128->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem129 = UI_TW_SET_SETMZ36->item(13, 0);
        ___qtablewidgetitem129->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem130 = UI_TW_SET_SETMZ36->item(13, 1);
        ___qtablewidgetitem130->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem131 = UI_TW_SET_SETMZ36->item(13, 2);
        ___qtablewidgetitem131->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem132 = UI_TW_SET_SETMZ36->item(14, 0);
        ___qtablewidgetitem132->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem133 = UI_TW_SET_SETMZ36->item(14, 1);
        ___qtablewidgetitem133->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem134 = UI_TW_SET_SETMZ36->item(14, 2);
        ___qtablewidgetitem134->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem135 = UI_TW_SET_SETMZ36->item(15, 0);
        ___qtablewidgetitem135->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem136 = UI_TW_SET_SETMZ36->item(15, 1);
        ___qtablewidgetitem136->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem137 = UI_TW_SET_SETMZ36->item(15, 2);
        ___qtablewidgetitem137->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem138 = UI_TW_SET_SETMZ36->item(16, 0);
        ___qtablewidgetitem138->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem139 = UI_TW_SET_SETMZ36->item(16, 1);
        ___qtablewidgetitem139->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem140 = UI_TW_SET_SETMZ36->item(16, 2);
        ___qtablewidgetitem140->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem141 = UI_TW_SET_SETMZ36->item(17, 0);
        ___qtablewidgetitem141->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem142 = UI_TW_SET_SETMZ36->item(17, 1);
        ___qtablewidgetitem142->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem143 = UI_TW_SET_SETMZ36->item(17, 2);
        ___qtablewidgetitem143->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem144 = UI_TW_SET_SETMZ36->item(18, 0);
        ___qtablewidgetitem144->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem145 = UI_TW_SET_SETMZ36->item(18, 1);
        ___qtablewidgetitem145->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem146 = UI_TW_SET_SETMZ36->item(18, 2);
        ___qtablewidgetitem146->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem147 = UI_TW_SET_SETMZ36->item(19, 0);
        ___qtablewidgetitem147->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem148 = UI_TW_SET_SETMZ36->item(19, 1);
        ___qtablewidgetitem148->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem149 = UI_TW_SET_SETMZ36->item(19, 2);
        ___qtablewidgetitem149->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem150 = UI_TW_SET_SETMZ36->item(20, 0);
        ___qtablewidgetitem150->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem151 = UI_TW_SET_SETMZ36->item(20, 1);
        ___qtablewidgetitem151->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem152 = UI_TW_SET_SETMZ36->item(20, 2);
        ___qtablewidgetitem152->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem153 = UI_TW_SET_SETMZ36->item(21, 0);
        ___qtablewidgetitem153->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem154 = UI_TW_SET_SETMZ36->item(21, 1);
        ___qtablewidgetitem154->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem155 = UI_TW_SET_SETMZ36->item(21, 2);
        ___qtablewidgetitem155->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem156 = UI_TW_SET_SETMZ36->item(22, 0);
        ___qtablewidgetitem156->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem157 = UI_TW_SET_SETMZ36->item(22, 1);
        ___qtablewidgetitem157->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem158 = UI_TW_SET_SETMZ36->item(22, 2);
        ___qtablewidgetitem158->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem159 = UI_TW_SET_SETMZ36->item(23, 0);
        ___qtablewidgetitem159->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem160 = UI_TW_SET_SETMZ36->item(23, 1);
        ___qtablewidgetitem160->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem161 = UI_TW_SET_SETMZ36->item(23, 2);
        ___qtablewidgetitem161->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem162 = UI_TW_SET_SETMZ36->item(24, 0);
        ___qtablewidgetitem162->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem163 = UI_TW_SET_SETMZ36->item(24, 1);
        ___qtablewidgetitem163->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem164 = UI_TW_SET_SETMZ36->item(24, 2);
        ___qtablewidgetitem164->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem165 = UI_TW_SET_SETMZ36->item(25, 0);
        ___qtablewidgetitem165->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem166 = UI_TW_SET_SETMZ36->item(25, 1);
        ___qtablewidgetitem166->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem167 = UI_TW_SET_SETMZ36->item(25, 2);
        ___qtablewidgetitem167->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem168 = UI_TW_SET_SETMZ36->item(26, 0);
        ___qtablewidgetitem168->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem169 = UI_TW_SET_SETMZ36->item(26, 1);
        ___qtablewidgetitem169->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem170 = UI_TW_SET_SETMZ36->item(26, 2);
        ___qtablewidgetitem170->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem171 = UI_TW_SET_SETMZ36->item(27, 0);
        ___qtablewidgetitem171->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem172 = UI_TW_SET_SETMZ36->item(27, 1);
        ___qtablewidgetitem172->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem173 = UI_TW_SET_SETMZ36->item(27, 2);
        ___qtablewidgetitem173->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem174 = UI_TW_SET_SETMZ36->item(28, 0);
        ___qtablewidgetitem174->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem175 = UI_TW_SET_SETMZ36->item(28, 1);
        ___qtablewidgetitem175->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem176 = UI_TW_SET_SETMZ36->item(28, 2);
        ___qtablewidgetitem176->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem177 = UI_TW_SET_SETMZ36->item(29, 0);
        ___qtablewidgetitem177->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem178 = UI_TW_SET_SETMZ36->item(29, 1);
        ___qtablewidgetitem178->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem179 = UI_TW_SET_SETMZ36->item(29, 2);
        ___qtablewidgetitem179->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem180 = UI_TW_SET_SETMZ36->item(30, 0);
        ___qtablewidgetitem180->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem181 = UI_TW_SET_SETMZ36->item(30, 1);
        ___qtablewidgetitem181->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem182 = UI_TW_SET_SETMZ36->item(30, 2);
        ___qtablewidgetitem182->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem183 = UI_TW_SET_SETMZ36->item(31, 0);
        ___qtablewidgetitem183->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem184 = UI_TW_SET_SETMZ36->item(31, 1);
        ___qtablewidgetitem184->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem185 = UI_TW_SET_SETMZ36->item(31, 2);
        ___qtablewidgetitem185->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem186 = UI_TW_SET_SETMZ36->item(32, 0);
        ___qtablewidgetitem186->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem187 = UI_TW_SET_SETMZ36->item(32, 1);
        ___qtablewidgetitem187->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem188 = UI_TW_SET_SETMZ36->item(32, 2);
        ___qtablewidgetitem188->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem189 = UI_TW_SET_SETMZ36->item(33, 0);
        ___qtablewidgetitem189->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem190 = UI_TW_SET_SETMZ36->item(33, 1);
        ___qtablewidgetitem190->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem191 = UI_TW_SET_SETMZ36->item(33, 2);
        ___qtablewidgetitem191->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem192 = UI_TW_SET_SETMZ36->item(34, 0);
        ___qtablewidgetitem192->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem193 = UI_TW_SET_SETMZ36->item(34, 1);
        ___qtablewidgetitem193->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem194 = UI_TW_SET_SETMZ36->item(34, 2);
        ___qtablewidgetitem194->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem195 = UI_TW_SET_SETMZ36->item(35, 0);
        ___qtablewidgetitem195->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem196 = UI_TW_SET_SETMZ36->item(35, 1);
        ___qtablewidgetitem196->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        QTableWidgetItem *___qtablewidgetitem197 = UI_TW_SET_SETMZ36->item(35, 2);
        ___qtablewidgetitem197->setText(QApplication::translate("uiMapSetMZ", "0", nullptr));
        UI_TW_SET_SETMZ36->setSortingEnabled(__sortingEnabled1);

        tabWidget->setTabText(tabWidget->indexOf(tab_2), QApplication::translate("uiMapSetMZ", "LDAC", nullptr));
        label->setText(QApplication::translate("uiMapSetMZ", "(HDAC+Offset)/Gain", nullptr));
        UI_PB_UPDATE_SETMZ->setText(QApplication::translate("uiMapSetMZ", "Update", nullptr));
        UI_PB_SAVE_SETMZ->setText(QApplication::translate("uiMapSetMZ", "Save", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiMapSetMZ: public Ui_uiMapSetMZ {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIMAPSETMZ_H
