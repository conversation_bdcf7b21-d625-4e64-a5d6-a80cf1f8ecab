﻿#include "uiQ13SIMParamEditor.h"

uiQ13SIMParamEditor::uiQ13SIMParamEditor(QString Q,
                                     QWidget* parent)
    : uiBaseParamEditor(parent)
{
    ui.setupUi(this);
    setProperty("currentQ", Q);
}

void uiQ13SIMParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiQ13SIMParamEditor::initUI(QString& filePath)
{
    uiBase.uiBaseParamEditorLayout->insertWidget(0, ui.uiQ13SIMParamEditorWidget);
    initPage();
    return true;
}

void uiQ13SIMParamEditor::initPage()
{
    ui.tableWidget_chanel->setRowCount(CHANNEL_COUNTS_MAX_2048);
    QStringList verticalHeader;
    for (int r = 0; r < CHANNEL_COUNTS_MAX_2048; ++r) {
        verticalHeader << QString("Ch%1").arg(r + 1);
        for(int column=0; column< 2; ++column){
            ui.tableWidget_chanel->setItem(r, column, new QTableWidgetItem(""));
        }
    }
    ui.tableWidget_chanel->setVerticalHeaderLabels(verticalHeader);
    connect(ui.tableWidget_chanel,&QTableWidget::itemChanged,
            this, &uiQ13SIMParamEditor::on_chanel_Changed);
}

bool uiQ13SIMParamEditor::getTableParam(QVector<float>& mass,
                                        QVector<float>& DwellTimeMs)
{
    //SCANSPEED=15 质量校正
    if(errorParam)
        return false;
    int rowCount= ui.tableWidget_chanel->rowCount();
    mass.resize(rowCount);
    DwellTimeMs.resize(rowCount);

    bool ok=false;
    int index= 0;
    for(int row= 0; row< rowCount; ++row){
        if((ui.tableWidget_chanel->item(row, 0)->text().isEmpty())||
                (ui.tableWidget_chanel->item(row, 1)->text().isEmpty()))
            continue;
        mass[index]= ui.tableWidget_chanel->item(row, 0)->text().toFloat(&ok);
        IF_CONTINUE(ok);
        DwellTimeMs[index]= ui.tableWidget_chanel->item(row, 1)->text().toFloat(&ok);
        IF_CONTINUE(ok);
        ++index;
    }
    mass.resize(index);
    DwellTimeMs.resize(index);
    if(index< 1)
        return false;
    return true;
}

void uiQ13SIMParamEditor::on_chanel_Changed(QTableWidgetItem *item)
{
    bool ok;
    double value = item->text().toDouble(&ok);
    if (!ok) {
        errorParam= true;
        return;
    }
    if(!getWaitTimeMs(uiBase.lineEdit_Polarity_switch_time->text(),
                      uiBase.lineEdit_pauseTime->text(),
                      uiBase.lineEdit_eventTime->text())){
        errorParam= true;
        return ;
    }
    errorParam= false;
}

void uiQ13SIMParamEditor::on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1)
{
    if(!getWaitTimeMs(arg1,
                      uiBase.lineEdit_pauseTime->text(),
                      uiBase.lineEdit_eventTime->text())){
        errorParam= true;
        return ;
    }
    errorParam= false;
}

void uiQ13SIMParamEditor::on_lineEdit_pauseTime_textEdited(const QString &arg1)
{
    if(!getWaitTimeMs(uiBase.lineEdit_Polarity_switch_time->text(),
                      arg1,
                      uiBase.lineEdit_eventTime->text())){
        errorParam= true;
        return ;
    }
    errorParam= false;
}

void uiQ13SIMParamEditor::on_lineEdit_eventTime_textEdited(const QString &arg1)
{
    if(!getWaitTimeMs(uiBase.lineEdit_Polarity_switch_time->text(),
                      uiBase.lineEdit_pauseTime->text(),
                      arg1)){
        errorParam= true;
        return ;
    }
    errorParam= false;
}

QTableWidgetItem* uiQ13SIMParamEditor::getItem(int r, int c)
{
    QTableWidgetItem* item;
    item = ui.tableWidget_chanel->item(r, c);
    if (item == nullptr) {
        item = new QTableWidgetItem();
        ui.tableWidget_chanel->setItem(r, c, item);
    }
    return item;
}

bool uiQ13SIMParamEditor::getWaitTimeMs(QString strPN_SwitchTimeMs, QString strPauseTimeMs,
                   QString strEventTimeMs)
{
    bool ok= false;

    double PN_SwitchTimeMs= strPN_SwitchTimeMs.toDouble(&ok);
    if(!ok)
        return false;
    PN_SwitchTimeMs= cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM::getUsableTimeMs(PN_SwitchTimeMs);
    uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(PN_SwitchTimeMs));

    double PauseTimeMs= strPauseTimeMs.toDouble(&ok);
    if(!ok)
        return false;
    PauseTimeMs= cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM::getUsableTimeMs(PauseTimeMs);
    uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(PauseTimeMs));

    if(PauseTimeMs+ PN_SwitchTimeMs< WAIT_TIME_MIN_MS)
        return false;

    double eventTimeMs= strEventTimeMs.toDouble(&ok);
    if(!ok)
        return false;

    double sumTime= 0;
    int sizeChanel= ui.tableWidget_chanel->rowCount();
    for(int row= 0; row< sizeChanel; ++row){
        if((ui.tableWidget_chanel->item(row, 0)->text().isEmpty())||
                (ui.tableWidget_chanel->item(row, 1)->text().isEmpty()))
            break;
        double channelTime= cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM::getUsableTimeMs(
                    ui.tableWidget_chanel->item(row, 1)->text().toFloat(&ok));
        if((channelTime> 0)&& ok)
            sumTime+= PauseTimeMs*1000+ channelTime*1000+ WAIT_TIME_MIN_US;
    }
    sumTime= (sumTime- WAIT_TIME_MIN_US)/1000;

    double WaitTimeMs= eventTimeMs- PN_SwitchTimeMs- sumTime;//tmpPARAM.WaitTimeMs= tmpPARAM.getWaitTimeMs(ui.lineEdit_eventTime->text().toFloat());
    uiBase.lineEdit_waitTime_rOnly->setText(QString::number(WaitTimeMs));
    if(WaitTimeMs< 1)
        return false;
    return true;
}
