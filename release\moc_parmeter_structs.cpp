/****************************************************************************
** Meta object code from reading C++ file 'parmeter_structs.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../sMethod/parmeter_structs.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'parmeter_structs.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_LabSolutionsRW_t {
    QByteArrayData data[13];
    char stringdata0[127];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_LabSolutionsRW_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_LabSolutionsRW_t qt_meta_stringdata_LabSolutionsRW = {
    {
QT_MOC_LITERAL(0, 0, 14), // "LabSolutionsRW"
QT_MOC_LITERAL(1, 15, 9), // "EventType"
QT_MOC_LITERAL(2, 25, 3), // "MRM"
QT_MOC_LITERAL(3, 29, 6), // "Q1Scan"
QT_MOC_LITERAL(4, 36, 5), // "Q1SIM"
QT_MOC_LITERAL(5, 42, 6), // "Q3Scan"
QT_MOC_LITERAL(6, 49, 5), // "Q3SIM"
QT_MOC_LITERAL(7, 55, 15), // "NeutralLossScan"
QT_MOC_LITERAL(8, 71, 16), // "PrecursorIonScan"
QT_MOC_LITERAL(9, 88, 14), // "ProductIonScan"
QT_MOC_LITERAL(10, 103, 6), // "QtflMs"
QT_MOC_LITERAL(11, 110, 8), // "QtflMsMs"
QT_MOC_LITERAL(12, 119, 7) // "QtflMRM"

    },
    "LabSolutionsRW\0EventType\0MRM\0Q1Scan\0"
    "Q1SIM\0Q3Scan\0Q3SIM\0NeutralLossScan\0"
    "PrecursorIonScan\0ProductIonScan\0QtflMs\0"
    "QtflMsMs\0QtflMRM"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_LabSolutionsRW[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,   11,   19,

 // enum data: key, value
       2, uint(LabSolutionsRW::MRM),
       3, uint(LabSolutionsRW::Q1Scan),
       4, uint(LabSolutionsRW::Q1SIM),
       5, uint(LabSolutionsRW::Q3Scan),
       6, uint(LabSolutionsRW::Q3SIM),
       7, uint(LabSolutionsRW::NeutralLossScan),
       8, uint(LabSolutionsRW::PrecursorIonScan),
       9, uint(LabSolutionsRW::ProductIonScan),
      10, uint(LabSolutionsRW::QtflMs),
      11, uint(LabSolutionsRW::QtflMsMs),
      12, uint(LabSolutionsRW::QtflMRM),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject LabSolutionsRW::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_LabSolutionsRW.data,
    qt_meta_data_LabSolutionsRW,
    nullptr,
    nullptr,
    nullptr
} };

QT_WARNING_POP
QT_END_MOC_NAMESPACE
