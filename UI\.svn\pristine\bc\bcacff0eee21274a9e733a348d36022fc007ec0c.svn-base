#pragma once
#include <QWidget>
#include <qUiWidget.h>
#include <uiDeviceControl/uiDeviceControlLC.h>
#include <uiDeviceControl/uiDeviceControlMASS.h>
#include "ui_uiDeviceControl.h"

class uiDeviceControl : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiDeviceControl(QWidget *parent = nullptr);
    ~uiDeviceControl();
    void initClass(QString& filePath){
        mDeviceControlLC= new uiDeviceControlLC();
        mDeviceControlMASS= new uiDeviceControlMASS();
        initUI(filePath);
    }
    QWidget *getToolBar(){
        return ui.UI_W_TOOLBAR_DeviceControl;
    }
protected:
    virtual bool initUI(QString& filePath){
        ui.setupUi(this);
        mDeviceControlLC->hide();
        mDeviceControlMASS->hide();
        return true;
    }

private slots:
    void on_UI_PB_LC_EDIT_DC_clicked();

    void on_UI_PB_MASS_EDIT_DC_clicked();

private:
    Ui::uiDeviceControl ui;
    uiDeviceControlLC* mDeviceControlLC = nullptr;
    uiDeviceControlMASS* mDeviceControlMASS = nullptr;
};

