/****************************************************************************
** Meta object code from reading C++ file 'ParameterEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/LibWidget/ParameterEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ParameterEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ParameterEditor_t {
    QByteArrayData data[22];
    char stringdata0[205];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ParameterEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ParameterEditor_t qt_meta_stringdata_ParameterEditor = {
    {
QT_MOC_LITERAL(0, 0, 15), // "ParameterEditor"
QT_MOC_LITERAL(1, 16, 11), // "textChanged"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 4), // "text"
QT_MOC_LITERAL(4, 34, 12), // "labelChanged"
QT_MOC_LITERAL(5, 47, 5), // "label"
QT_MOC_LITERAL(6, 53, 11), // "unitChanged"
QT_MOC_LITERAL(7, 65, 4), // "unit"
QT_MOC_LITERAL(8, 70, 12), // "valueChanged"
QT_MOC_LITERAL(9, 83, 5), // "value"
QT_MOC_LITERAL(10, 89, 10), // "textEdited"
QT_MOC_LITERAL(11, 100, 19), // "sig_editingFinished"
QT_MOC_LITERAL(12, 120, 7), // "setText"
QT_MOC_LITERAL(13, 128, 8), // "setLabel"
QT_MOC_LITERAL(14, 137, 7), // "setUnit"
QT_MOC_LITERAL(15, 145, 8), // "setValue"
QT_MOC_LITERAL(16, 154, 8), // "setRange"
QT_MOC_LITERAL(17, 163, 3), // "min"
QT_MOC_LITERAL(18, 167, 3), // "max"
QT_MOC_LITERAL(19, 171, 11), // "setDecimals"
QT_MOC_LITERAL(20, 183, 8), // "decimals"
QT_MOC_LITERAL(21, 192, 12) // "onTextEdited"

    },
    "ParameterEditor\0textChanged\0\0text\0"
    "labelChanged\0label\0unitChanged\0unit\0"
    "valueChanged\0value\0textEdited\0"
    "sig_editingFinished\0setText\0setLabel\0"
    "setUnit\0setValue\0setRange\0min\0max\0"
    "setDecimals\0decimals\0onTextEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ParameterEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       4,  116, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   79,    2, 0x06 /* Public */,
       4,    1,   82,    2, 0x06 /* Public */,
       6,    1,   85,    2, 0x06 /* Public */,
       8,    1,   88,    2, 0x06 /* Public */,
      10,    0,   91,    2, 0x06 /* Public */,
      11,    0,   92,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    1,   93,    2, 0x0a /* Public */,
      13,    1,   96,    2, 0x0a /* Public */,
      14,    1,   99,    2, 0x0a /* Public */,
      15,    1,  102,    2, 0x0a /* Public */,
      16,    2,  105,    2, 0x0a /* Public */,
      19,    1,  110,    2, 0x0a /* Public */,
      21,    1,  113,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Double,    9,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Double,    9,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,   17,   18,
    QMetaType::Void, QMetaType::Int,   20,
    QMetaType::Void, QMetaType::QString,    3,

 // properties: name, type, flags
       3, QMetaType::QString, 0x00495103,
       5, QMetaType::QString, 0x00495103,
       7, QMetaType::QString, 0x00495103,
       9, QMetaType::Double, 0x00495103,

 // properties: notify_signal_id
       0,
       1,
       2,
       3,

       0        // eod
};

void ParameterEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ParameterEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->textChanged((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->labelChanged((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 2: _t->unitChanged((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 3: _t->valueChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 4: _t->textEdited(); break;
        case 5: _t->sig_editingFinished(); break;
        case 6: _t->setText((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->setLabel((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->setUnit((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 9: _t->setValue((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 10: _t->setRange((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 11: _t->setDecimals((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 12: _t->onTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ParameterEditor::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::textChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ParameterEditor::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::labelChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ParameterEditor::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::unitChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ParameterEditor::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::valueChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ParameterEditor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::textEdited)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ParameterEditor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ParameterEditor::sig_editingFinished)) {
                *result = 5;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<ParameterEditor *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->text(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->label(); break;
        case 2: *reinterpret_cast< QString*>(_v) = _t->unit(); break;
        case 3: *reinterpret_cast< double*>(_v) = _t->value(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<ParameterEditor *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setText(*reinterpret_cast< QString*>(_v)); break;
        case 1: _t->setLabel(*reinterpret_cast< QString*>(_v)); break;
        case 2: _t->setUnit(*reinterpret_cast< QString*>(_v)); break;
        case 3: _t->setValue(*reinterpret_cast< double*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject ParameterEditor::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_ParameterEditor.data,
    qt_meta_data_ParameterEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ParameterEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ParameterEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ParameterEditor.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ParameterEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void ParameterEditor::textChanged(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ParameterEditor::labelChanged(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ParameterEditor::unitChanged(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ParameterEditor::valueChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ParameterEditor::textEdited()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ParameterEditor::sig_editingFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
