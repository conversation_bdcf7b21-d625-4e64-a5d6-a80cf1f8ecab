#include "cTuneXML.h"
#include <QDomDocument>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>
#include <QCoreApplication>

cTuneXML::cTuneXML(QWidget *parent) :
    QWidget(parent)
{
    mFile= new QFile();
}

void cTuneXML::domRead(QString fileName)
{
    if(fileName.isEmpty()){
        fileName = QFileDialog::getOpenFileName(this,
                                                "get tuning file",
                                                QCoreApplication::applicationDirPath(),
                                                "xml(*.xml)");
    }
    mFile->setFileName(fileName);
    bool ret =mFile->open(QIODevice::ReadOnly|QIODevice::Text);
    if(!ret){
        QMessageBox::warning(this, QString("warning"), QString("打开文件失败！"));
        return;
    }
    //对xml文件操作
    QDomDocument doc("TuningFile");//定义doc对象，初始化名字
    bool isLot = doc.setContent(mFile);//将文件与QDomDocument类关联
    if(!isLot){
        //关联xml文件失败
        mFile->close();
        QMessageBox::warning(this, "waring", "文件格式错误");
        return;
    }
    mFile->close();
    QDomElement firstElem = doc.documentElement();//获取到了<begin>一级目录
    qDebug()<<"一级目录<begin>:　"<<firstElem.nodeName();
    QDomNodeList secondList = firstElem.childNodes();//获取全部二级目录<Type1><Type2><Typ3>
    for (int i = 0; i < secondList.count(); ++i) {
        QString str = QString("      第 %1 个二级目录节点:").arg(i+1);
        qDebug()<<str.toUtf8().data()<<secondList.at(i).nodeName();//将QString转换成char *，使用qDebug输出没有引号
        QDomElement secondElem = secondList.at(i).toElement();//获取二级目录节点的属性值管理者domElement
        if(secondElem.hasAttribute("myLover")){
            //输出二级目录节点的属性值
            qDebug()<<"      "<<secondElem.nodeName().append("的属性值myLover为").toUtf8().data()<<secondElem.attribute("myLover");
        }
        else{
            qDebug()<<"      "<<secondElem.nodeName().append("该节点无属性值").toUtf8().data();
        }
        QDomNodeList thirdList = secondList.at(i).childNodes();//获取三级目录<具体值></具体值>
        for(int j = 0;j<thirdList.length();j++ ){
            QDomElement thirdElement=thirdList.at(j).toElement();
            QString str_1 = QString("            %1中第 %2 个三级目录节点 %3,他的文本值为 %4:")
                    .arg(secondList.at(i).nodeName(),QString::number(j+1),thirdList.at(j).nodeName(),thirdElement.text());
            qDebug()<<str_1.toUtf8().data();//将QString转换成char *，使用qDebug输出没有引号
        }
    }
}

void cTuneXML::domWrite()
{
    //add instruction
    QDomDocument doc;
    QDomProcessingInstruction instruction = doc.createProcessingInstruction("xml","version=\"1.0\" encoding=\"UTF-8\"");//写xml中的第一行说明
    doc.appendChild(instruction);

    //add 第一级目录节点
    QDomElement firstNode = doc.createElement("Tuning");
    doc.appendChild(firstNode);

    //add 第二级目录节点
    QDomElement secondNode = doc.createElement("Type");
    //为二级目录添加属性值
    QDomAttr secondAttr = doc.createAttribute("MyLover");
    secondAttr.setValue("yxx");
    secondNode.setAttributeNode(secondAttr);
    firstNode.appendChild(secondNode);//一级目录下添加二级节点
        //add 第三级目录节点 添加text文本值
        QDomElement thirdNode_1 = doc.createElement("sex");
        secondNode.appendChild(thirdNode_1);
        QDomText t1 = doc.createTextNode("girl");
        thirdNode_1.appendChild(t1);
        QDomElement thirdNode_2 = doc.createElement("school");
        secondNode.appendChild(thirdNode_2);
        QDomText t2 = doc.createTextNode("Xian");
        thirdNode_2.appendChild(t2);
        QDomElement thirdNode_3 = doc.createElement("age");
        secondNode.appendChild(thirdNode_3);
        QDomText t3 = doc.createTextNode("1996");
        thirdNode_3.appendChild(t3);
        QDomElement thirdNode_4 = doc.createElement("special");
        secondNode.appendChild(thirdNode_4);
        QDomText t4 = doc.createTextNode("beautiful");
        thirdNode_4.appendChild(t4);
    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "Save xml file",
                                                        "C:/Users/<USER>/Desktop",
                                                    "xml(*.xml)");
    if(fileName.isEmpty()){
        return;
    }
    mFile->setFileName(fileName);
    bool ret =mFile->open(QIODevice::WriteOnly|QIODevice::Text);
    if(!ret){
        QMessageBox::warning(this,"warning","保存文件失败！");
        return;
    }
    QTextStream stream(mFile);//使用QTextStream类与文件关联进行保存xml！
    stream.setCodec("UTF-8");//编码格式
    doc.save(stream,4);//4代表的是缩进个数
    mFile->close();
    QMessageBox::information(this,"save tip","xml文件保存成功！");
}

