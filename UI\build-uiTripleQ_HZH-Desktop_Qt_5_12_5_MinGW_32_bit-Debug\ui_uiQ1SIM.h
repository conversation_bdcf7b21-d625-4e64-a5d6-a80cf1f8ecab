/********************************************************************************
** Form generated from reading UI file 'uiQ1SIM.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIQ1SIM_H
#define UI_UIQ1SIM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiQ1SIM
{
public:
    QVBoxLayout *verticalLayout_2;
    QCheckBox *checkBox;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QComboBox *comboBox;
    QSpacerItem *horizontalSpacer;
    QLabel *label_2;
    QSpinBox *spinBox;
    QSpacerItem *horizontalSpacer_2;
    QLabel *label_3;
    QSpinBox *spinBox_2;
    QSpacerItem *horizontalSpacer_3;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout;
    QGridLayout *gridLayout;
    QSpacerItem *horizontalSpacer_4;
    QLabel *label_4;
    QSpinBox *spinBox_4;
    QSpacerItem *horizontalSpacer_5;
    QSpinBox *spinBox_3;
    QLabel *label_5;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_8;
    QPushButton *pushButton;
    QPushButton *pushButton_2;
    QSpacerItem *horizontalSpacer_6;
    QTableWidget *UI_TW_TABLE_EMRM;

    void setupUi(QWidget *uiQ1SIM)
    {
        if (uiQ1SIM->objectName().isEmpty())
            uiQ1SIM->setObjectName(QString::fromUtf8("uiQ1SIM"));
        uiQ1SIM->resize(1085, 755);
        verticalLayout_2 = new QVBoxLayout(uiQ1SIM);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        checkBox = new QCheckBox(uiQ1SIM);
        checkBox->setObjectName(QString::fromUtf8("checkBox"));
        checkBox->setChecked(true);

        verticalLayout_2->addWidget(checkBox);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label = new QLabel(uiQ1SIM);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout->addWidget(label);

        comboBox = new QComboBox(uiQ1SIM);
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->setObjectName(QString::fromUtf8("comboBox"));

        horizontalLayout->addWidget(comboBox);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        label_2 = new QLabel(uiQ1SIM);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout->addWidget(label_2);

        spinBox = new QSpinBox(uiQ1SIM);
        spinBox->setObjectName(QString::fromUtf8("spinBox"));

        horizontalLayout->addWidget(spinBox);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);

        label_3 = new QLabel(uiQ1SIM);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout->addWidget(label_3);

        spinBox_2 = new QSpinBox(uiQ1SIM);
        spinBox_2->setObjectName(QString::fromUtf8("spinBox_2"));

        horizontalLayout->addWidget(spinBox_2);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);


        verticalLayout_2->addLayout(horizontalLayout);

        groupBox = new QGroupBox(uiQ1SIM);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        verticalLayout = new QVBoxLayout(groupBox);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_4, 0, 2, 1, 1);

        label_4 = new QLabel(groupBox);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        gridLayout->addWidget(label_4, 0, 0, 1, 1);

        spinBox_4 = new QSpinBox(groupBox);
        spinBox_4->setObjectName(QString::fromUtf8("spinBox_4"));

        gridLayout->addWidget(spinBox_4, 0, 4, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_5, 0, 5, 1, 1);

        spinBox_3 = new QSpinBox(groupBox);
        spinBox_3->setObjectName(QString::fromUtf8("spinBox_3"));

        gridLayout->addWidget(spinBox_3, 0, 1, 1, 1);

        label_5 = new QLabel(groupBox);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        gridLayout->addWidget(label_5, 0, 3, 1, 1);


        verticalLayout->addLayout(gridLayout);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label_8 = new QLabel(groupBox);
        label_8->setObjectName(QString::fromUtf8("label_8"));

        horizontalLayout_2->addWidget(label_8);

        pushButton = new QPushButton(groupBox);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));

        horizontalLayout_2->addWidget(pushButton);

        pushButton_2 = new QPushButton(groupBox);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));

        horizontalLayout_2->addWidget(pushButton_2);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_6);


        verticalLayout->addLayout(horizontalLayout_2);

        UI_TW_TABLE_EMRM = new QTableWidget(groupBox);
        UI_TW_TABLE_EMRM->setObjectName(QString::fromUtf8("UI_TW_TABLE_EMRM"));

        verticalLayout->addWidget(UI_TW_TABLE_EMRM);


        verticalLayout_2->addWidget(groupBox);


        retranslateUi(uiQ1SIM);

        QMetaObject::connectSlotsByName(uiQ1SIM);
    } // setupUi

    void retranslateUi(QWidget *uiQ1SIM)
    {
        uiQ1SIM->setWindowTitle(QApplication::translate("uiQ1SIM", "Form", nullptr));
        checkBox->setText(QApplication::translate("uiQ1SIM", "Experiment", nullptr));
        label->setText(QApplication::translate("uiQ1SIM", "Polarity", nullptr));
        comboBox->setItemText(0, QApplication::translate("uiQ1SIM", "Negative", nullptr));
        comboBox->setItemText(1, QApplication::translate("uiQ1SIM", "Positive", nullptr));

        label_2->setText(QApplication::translate("uiQ1SIM", "Spray voltage(V)", nullptr));
        label_3->setText(QApplication::translate("uiQ1SIM", "Experiment cycle time(ms)", nullptr));
        groupBox->setTitle(QApplication::translate("uiQ1SIM", "Advanced Experiment Settings", nullptr));
        label_4->setText(QApplication::translate("uiQ1SIM", "Setting time(ms)", nullptr));
        label_5->setText(QApplication::translate("uiQ1SIM", "Pause time(ms)", nullptr));
        label_8->setText(QApplication::translate("uiQ1SIM", "Mass Table", nullptr));
        pushButton->setText(QApplication::translate("uiQ1SIM", "Import", nullptr));
        pushButton_2->setText(QApplication::translate("uiQ1SIM", "Export", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiQ1SIM: public Ui_uiQ1SIM {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIQ1SIM_H
