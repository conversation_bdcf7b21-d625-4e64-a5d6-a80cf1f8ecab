#include "uiEditMRM.h"
#include "ui_uiEditMRM.h"

#include <QFileDialog>

uiEditMRM::uiEditMRM(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::uiEditMRM)
{
    ui->setupUi(this);

    ui->UI_TW_TABLE_EMRM->setColumnCount(12);
    QStringList headers;
    headers << "Compound\nName" << "Ion Name" << "Retention\ntime(min)" << "Retention\ntime tolerance(+/-s)"
            <<"Q1\nmass(Da)" << "Q3\nmass(Da)" << "Edit\ndwell time"<< "Dwell\ntime(ms)"
           <<"EP(V)"<<"CE(V)"<<"CXP(V)"<<"Q1\nresolution";
    ui->UI_TW_TABLE_EMRM->setHorizontalHeaderLabels(headers);
    ui->UI_TW_TABLE_EMRM->horizontalHeader()->setStyleSheet(
        "QHeaderView::section {"
        "   white-space: pre-wrap;"  // 允许换行
        "   text-align: center;"     // 文字居中
        "   padding: 4px;"           // 增加内边距
        "}"
    );
    ui->UI_TW_TABLE_EMRM->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->UI_TW_TABLE_EMRM->setColumnHidden(2, true);
    ui->UI_TW_TABLE_EMRM->setColumnHidden(3, true);
}

uiEditMRM::~uiEditMRM()
{
    delete ui;
}

void uiEditMRM::on_comboBox_2_activated(int index)
{
    if(0== index){
        QString filePath = QFileDialog::getOpenFileName(
            this,                   // 父窗口
            "选择配置文件",         // 对话框标题
            QDir::homePath(),       // 初始目录（默认用户目录）
            "csv文件 (*.csv);;所有文件 (*)" // 文件过滤器
        );

        if (!filePath.isEmpty()) {
            //qDebug() << "已选择文件：" << filePath;
        }
    }else{
        QString filePath = QFileDialog::getSaveFileName(
            this,
            "保存配置文件",
            QDir::homePath(),       // 初始目录（默认用户目录）
            "csv文件 (*.csv);;所有文件 (*)"
        );
        if (!filePath.isEmpty()) {
            //qDebug() << "已选择文件：" << filePath;
        }
    }
}

void uiEditMRM::on_UI_CB_SMRM_EMRM_stateChanged(int arg1)
{
    if(arg1){
        ui->UI_TW_TABLE_EMRM->setColumnHidden(2, false);
        ui->UI_TW_TABLE_EMRM->setColumnHidden(3, false);
    }else{
        ui->UI_TW_TABLE_EMRM->setColumnHidden(2, true);
        ui->UI_TW_TABLE_EMRM->setColumnHidden(3, true);
    }
}
