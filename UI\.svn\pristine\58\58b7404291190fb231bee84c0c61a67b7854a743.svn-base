/****************************************************************************
** Meta object code from reading C++ file 'sCoordinateMask.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../LibWidget/sCoordinateMask.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sCoordinateMask.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_sCoordinateMask_t {
    QByteArrayData data[11];
    char stringdata0[189];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_sCoordinateMask_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_sCoordinateMask_t qt_meta_stringdata_sCoordinateMask = {
    {
QT_MOC_LITERAL(0, 0, 15), // "sCoordinateMask"
QT_MOC_LITERAL(1, 16, 10), // "sCalculate"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 10), // "sClearMark"
QT_MOC_LITERAL(4, 39, 7), // "sCancel"
QT_MOC_LITERAL(5, 47, 19), // "on_UI_PB_OK_clicked"
QT_MOC_LITERAL(6, 67, 23), // "on_UI_PB_CANCEL_clicked"
QT_MOC_LITERAL(7, 91, 31), // "on_horizontalSlider_sliderMoved"
QT_MOC_LITERAL(8, 123, 8), // "position"
QT_MOC_LITERAL(9, 132, 33), // "on_horizontalSlider_2_sliderM..."
QT_MOC_LITERAL(10, 166, 22) // "on_UI_PB_CLEAR_clicked"

    },
    "sCoordinateMask\0sCalculate\0\0sClearMark\0"
    "sCancel\0on_UI_PB_OK_clicked\0"
    "on_UI_PB_CANCEL_clicked\0"
    "on_horizontalSlider_sliderMoved\0"
    "position\0on_horizontalSlider_2_sliderMoved\0"
    "on_UI_PB_CLEAR_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_sCoordinateMask[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x06 /* Public */,
       3,    0,   55,    2, 0x06 /* Public */,
       4,    0,   56,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   57,    2, 0x08 /* Private */,
       6,    0,   58,    2, 0x08 /* Private */,
       7,    1,   59,    2, 0x08 /* Private */,
       9,    1,   62,    2, 0x08 /* Private */,
      10,    0,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    8,
    QMetaType::Void, QMetaType::Int,    8,
    QMetaType::Void,

       0        // eod
};

void sCoordinateMask::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<sCoordinateMask *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sCalculate(); break;
        case 1: _t->sClearMark(); break;
        case 2: _t->sCancel(); break;
        case 3: _t->on_UI_PB_OK_clicked(); break;
        case 4: _t->on_UI_PB_CANCEL_clicked(); break;
        case 5: _t->on_horizontalSlider_sliderMoved((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 6: _t->on_horizontalSlider_2_sliderMoved((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 7: _t->on_UI_PB_CLEAR_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (sCoordinateMask::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sCoordinateMask::sCalculate)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (sCoordinateMask::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sCoordinateMask::sClearMark)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (sCoordinateMask::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sCoordinateMask::sCancel)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject sCoordinateMask::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_sCoordinateMask.data,
    qt_meta_data_sCoordinateMask,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *sCoordinateMask::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *sCoordinateMask::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_sCoordinateMask.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int sCoordinateMask::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void sCoordinateMask::sCalculate()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void sCoordinateMask::sClearMark()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void sCoordinateMask::sCancel()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
