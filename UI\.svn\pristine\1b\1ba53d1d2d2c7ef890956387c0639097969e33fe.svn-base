/********************************************************************************
** Form generated from reading UI file 'sTranslucentTemplate.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_STRANSLUCENTTEMPLATE_H
#define UI_STRANSLUCENTTEMPLATE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sTranslucentTemplate
{
public:
    QGridLayout *UI_LAYOUT_TTEMPLATE;
    QSpacerItem *UI_Spacer_UP_TTEMPLATE;
    QSpacerItem *UI_Spacer_LEFT_TTEMPLATE;
    QSpacerItem *UI_Spacer_RIGHT_TTEMPLATE;
    QSpacerItem *UI_Spacer_DOWN_TTEMPLATE;

    void setupUi(QWidget *sTranslucentTemplate)
    {
        if (sTranslucentTemplate->objectName().isEmpty())
            sTranslucentTemplate->setObjectName(QString::fromUtf8("sTranslucentTemplate"));
        sTranslucentTemplate->resize(664, 582);
        sTranslucentTemplate->setAutoFillBackground(false);
        UI_LAYOUT_TTEMPLATE = new QGridLayout(sTranslucentTemplate);
        UI_LAYOUT_TTEMPLATE->setObjectName(QString::fromUtf8("UI_LAYOUT_TTEMPLATE"));
        UI_Spacer_UP_TTEMPLATE = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_TTEMPLATE->addItem(UI_Spacer_UP_TTEMPLATE, 0, 1, 1, 1);

        UI_Spacer_LEFT_TTEMPLATE = new QSpacerItem(304, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        UI_LAYOUT_TTEMPLATE->addItem(UI_Spacer_LEFT_TTEMPLATE, 1, 0, 1, 1);

        UI_Spacer_RIGHT_TTEMPLATE = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        UI_LAYOUT_TTEMPLATE->addItem(UI_Spacer_RIGHT_TTEMPLATE, 1, 2, 1, 1);

        UI_Spacer_DOWN_TTEMPLATE = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_TTEMPLATE->addItem(UI_Spacer_DOWN_TTEMPLATE, 2, 1, 1, 1);


        retranslateUi(sTranslucentTemplate);

        QMetaObject::connectSlotsByName(sTranslucentTemplate);
    } // setupUi

    void retranslateUi(QWidget *sTranslucentTemplate)
    {
        sTranslucentTemplate->setWindowTitle(QApplication::translate("sTranslucentTemplate", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class sTranslucentTemplate: public Ui_sTranslucentTemplate {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_STRANSLUCENTTEMPLATE_H
