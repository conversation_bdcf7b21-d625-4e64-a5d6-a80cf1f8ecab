/****************************************************************************
** Meta object code from reading C++ file 'uiBaseParamEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiSingleAcquisition/uiBaseParamEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiBaseParamEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiBaseParamEditor_t {
    QByteArrayData data[7];
    char stringdata0[160];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiBaseParamEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiBaseParamEditor_t qt_meta_stringdata_uiBaseParamEditor = {
    {
QT_MOC_LITERAL(0, 0, 17), // "uiBaseParamEditor"
QT_MOC_LITERAL(1, 18, 25), // "on_UI_PB_Polarity_clicked"
QT_MOC_LITERAL(2, 44, 0), // ""
QT_MOC_LITERAL(3, 45, 32), // "on_lineEdit_pauseTime_textEdited"
QT_MOC_LITERAL(4, 78, 4), // "arg1"
QT_MOC_LITERAL(5, 83, 43), // "on_lineEdit_Polarity_switch_t..."
QT_MOC_LITERAL(6, 127, 32) // "on_lineEdit_eventTime_textEdited"

    },
    "uiBaseParamEditor\0on_UI_PB_Polarity_clicked\0"
    "\0on_lineEdit_pauseTime_textEdited\0"
    "arg1\0on_lineEdit_Polarity_switch_time_textEdited\0"
    "on_lineEdit_eventTime_textEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiBaseParamEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   34,    2, 0x08 /* Private */,
       3,    1,   35,    2, 0x08 /* Private */,
       5,    1,   38,    2, 0x08 /* Private */,
       6,    1,   41,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    4,
    QMetaType::Void, QMetaType::QString,    4,
    QMetaType::Void, QMetaType::QString,    4,

       0        // eod
};

void uiBaseParamEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiBaseParamEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_UI_PB_Polarity_clicked(); break;
        case 1: _t->on_lineEdit_pauseTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->on_lineEdit_Polarity_switch_time_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->on_lineEdit_eventTime_textEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiBaseParamEditor::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiBaseParamEditor.data,
    qt_meta_data_uiBaseParamEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiBaseParamEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiBaseParamEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiBaseParamEditor.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiBaseParamEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
