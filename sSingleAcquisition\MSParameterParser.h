﻿#pragma once

#include <QObject>
#include <QDomElement>
#include <QDomDocument>
#include <QList>
#include <QFile>

//#include "LabSolutionsRW_global.h"
#include "sMethod/parmeter_structs.h"


class MSParameterParser : public QObject
{
    Q_OBJECT
public:
    explicit MSParameterParser(const QString& xmlFile = QString(), QObject *parent = nullptr);
    ~MSParameterParser();

    QList<QDomElement>* getDomEvents(){
        return m_domEvents;
    }
    QDomDocument* getDomDocumentP(){
        return m_domDocument;
    }

    bool setFile(const QString& file);
    void saveMSParameter();

    QString getXmlFileName() const;
    QList<LabSolutionsRW::MassEvent *> getMassEventList() const;

signals:

public slots:

private:
    bool parseFile(QFile* dev);
    void readSegment(const QDomElement& element);
    void parseMassEventList();
    //parse dom element
    LabSolutionsRW::MassEvent *readSimEvent(const QDomElement& ele);
    LabSolutionsRW::MassEvent *readScanEvent(const QDomElement& ele);
    LabSolutionsRW::MassEvent *readMRMEvent(const QDomElement& ele);
    LabSolutionsRW::MassEvent *readIonScanEvent(const QDomElement& ele);

private:
    QDomDocument* m_domDocument;
    QDomElement m_rootElement;
    QString m_xmlFileName;
    QList<QDomElement> *m_domEvents;
    QList<LabSolutionsRW::MassEvent *> m_massEventList;
};
