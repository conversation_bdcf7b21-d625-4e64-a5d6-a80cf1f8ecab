#include "uiQueue.h"


uiQueue::uiQueue(QScriptEngine* pScriptEngine, QWidget *parent) :
    qUiWidget(parent)
{

}

uiQueue::~uiQueue()
{

}

void uiQueue::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiQueue::initUI(QString& filePath)
{
    ui.setupUi(this);
    createToolBar();
    return true;
}

void uiQueue::createToolBar()
{
    ui.UI_MB_START_QUEUE->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_START_QUEUE->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Start"));
    ui.UI_MB_START_QUEUE->setTextSize(64,64,40);
    connect(ui.UI_MB_START_QUEUE,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_START_QUEUE()));

    ui.UI_MB_STOP_QUEUE->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_STOP_QUEUE->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Stop"));
    ui.UI_MB_STOP_QUEUE->setTextSize(64,64,40);
    connect(ui.UI_MB_STOP_QUEUE,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_STOP_QUEUE()));

    ui.UI_MB_PRINT_QUEUE->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_PRINT_QUEUE->setPicture(QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/sMainWindow/picture/zhenkong_48_on.png"),
                                                tr("Print"));
    ui.UI_MB_PRINT_QUEUE->setTextSize(64,64,40);
    connect(ui.UI_MB_PRINT_QUEUE,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_PRINT_QUEUEH()));

}
/*
    QStringList headerText;
    headerText<<"Acquisition Status"//
<<QObject::tr("Sample Name")
<<"Est.Start Time"//
<<"Acquisition Time"//
          <<QObject::tr("MS Method")
         <<QObject::tr("LC Method")
         <<"Data File"//
        <<"Rack Code"//<<QObject::tr("Rack Type")
       //<<QObject::tr("Rack Position")
      <<"Plate Code"//<<QObject::tr("Plate Type")
     <<"Plate Position"//<<QObject::tr("Plate Position")
    <<"Vial Position"//<<QObject::tr("Vial Position")
    //<<QObject::tr("Injection Time");
*/
void uiQueue::onSubmit(QList<QMap<QString, QString>>& pList, QString date)
{
    if(pList.isEmpty()|| date.isEmpty())
        return;
    QStringList headerText;
    headerText<<QObject::tr("Acquisition Status")//0
             <<QObject::tr("Sample Name")//sBatch 1
            <<QObject::tr("Est.Start Time")//2
           <<QObject::tr("Acquisition Time")//3
          <<QObject::tr("Rack Type")//tr("Rack Code")4
            //<<QObject::tr("Rack Position")
         <<QObject::tr("Plate Type")//tr("Plate Code")5
        <<QObject::tr("Plate Position")//sBatch 6
       <<QObject::tr("Vial Position")//sBatch 7
      <<QObject::tr("MS Method")//sBatch 8
     <<QObject::tr("LC Method")//sBatch 9
    <<QObject::tr("Data File");//10
    //<<QObject::tr("Injection Time");
    date= date+ "/";
    int rowCount= pList.size();
    ui.UI_TW_QUEUE->clear();
    ui.UI_TW_QUEUE->setColumnCount(headerText.size());
    ui.UI_TW_QUEUE->setRowCount(rowCount);
    for(int indexRow=0; indexRow< rowCount; ++indexRow){
        ui.UI_TW_QUEUE->setItem(indexRow, 1, new QTableWidgetItem(pList[indexRow][headerText[1]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 4, new QTableWidgetItem(pList[indexRow][headerText[4]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 5, new QTableWidgetItem(pList[indexRow][headerText[5]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 6, new QTableWidgetItem(pList[indexRow][headerText[6]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 7, new QTableWidgetItem(pList[indexRow][headerText[7]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 8, new QTableWidgetItem(pList[indexRow][headerText[8]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 9, new QTableWidgetItem(pList[indexRow][headerText[9]]));
        ui.UI_TW_QUEUE->setItem(indexRow, 10, new QTableWidgetItem(date+headerText[1]));
    }
}

void uiQueue::onUI_MB_START_QUEUE()
{

}

void uiQueue::onUI_MB_STOP_QUEUE()
{

}

void uiQueue::onUI_MB_PRINT_QUEUEH()
{

}
