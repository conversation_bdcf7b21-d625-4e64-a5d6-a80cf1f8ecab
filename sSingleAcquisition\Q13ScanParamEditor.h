﻿#pragma once

#include <UI/uiSingleAcquisition/uiQ13ScanParamEditor.h>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <sTune/sMapSetMZ.h>

class Q13ScanParamEditor : public uiQ13ScanParamEditor
{
    Q_OBJECT

public:
    explicit Q13ScanParamEditor(QString Q,
                                sMapSetMZ* pMapSetMZ,//sTune* pTune,
                                QWidget* parent = nullptr);
    ~Q13ScanParamEditor() ;//override;
    void initClass(QString& filePath);
    bool getParamEvent(cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM,
                       cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM_LDAC,
                       int& Polarity);
//    sMapSetMZ* getMapSetMZ() {
//        return mMapSetMZ;
//    }
private:
    sMapSetMZ* mMapSetMZ= nullptr;
    bool calcUseMapMZ(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE&,
                            cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM&);
};

