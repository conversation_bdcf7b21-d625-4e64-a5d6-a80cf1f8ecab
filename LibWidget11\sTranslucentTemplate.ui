<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>sTranslucentTemplate</class>
 <widget class="QWidget" name="sTranslucentTemplate">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>664</width>
    <height>582</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <layout class="QGridLayout" name="UI_LAYOUT_TTEMPLATE">
   <item row="0" column="1">
    <spacer name="UI_Spacer_UP_TTEMPLATE">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <spacer name="UI_Spacer_LEFT_TTEMPLATE">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>304</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="2">
    <spacer name="UI_Spacer_RIGHT_TTEMPLATE">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="1">
    <spacer name="UI_Spacer_DOWN_TTEMPLATE">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
