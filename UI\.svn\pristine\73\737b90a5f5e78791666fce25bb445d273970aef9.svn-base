SOURCES += \
    $$PWD/uiManualMode/uiDataAcqManual.cpp \
    $$PWD/uiManualMode/uiDeviceControl.cpp \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlLC.cpp \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlMASS.cpp \
    $$PWD/uiManualMode/uiDeviceControl/uiEditMRM.cpp \
    $$PWD/uiManualMode/uiMassTuneManual.cpp \
    $$PWD/uiManualMode/uiOptimizationManual.cpp

HEADERS += \
    $$PWD/uiManualMode/uiDataAcqManual.h \
    $$PWD/uiManualMode/uiDeviceControl.h \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlLC.h \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlMASS.h \
    $$PWD/uiManualMode/uiDeviceControl/uiEditMRM.h \
    $$PWD/uiManualMode/uiMassTuneManual.h \
    $$PWD/uiManualMode/uiOptimizationManual.h

FORMS += \
    $$PWD/uiManualMode/uiDataAcqManual.ui \
    $$PWD/uiManualMode/uiDeviceControl.ui \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlLC.ui \
    $$PWD/uiManualMode/uiDeviceControl/uiDeviceControlMASS.ui \
    $$PWD/uiManualMode/uiDeviceControl/uiEditMRM.ui \
    $$PWD/uiManualMode/uiMassTuneManual.ui \
    $$PWD/uiManualMode/uiOptimizationManual.ui

INCLUDEPATH += \
    $$PWD/uiManualMode

DEPENDPATH += \
    $$PWD/uiManualMode
