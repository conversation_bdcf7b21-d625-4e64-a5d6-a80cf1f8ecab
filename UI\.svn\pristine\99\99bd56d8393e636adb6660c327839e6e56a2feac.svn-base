<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiSingleAcquisition</class>
 <widget class="QWidget" name="uiSingleAcquisition">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>819</width>
    <height>527</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QWidget" name="UI_W_TOOLBAR_SINGLEACQ" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MB_LOAD_SINGLEACQ" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MB_SAVE_SINGLEACQ" native="true"/>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MB_RUN_SINGLEACQ" native="true"/>
      </item>
      <item>
       <widget class="MyWidget::sMyButton" name="UI_MB_STOP_SINGLEACQ" native="true"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="UI_W_MENU_SINGLEACQ" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>320</width>
         <height>16777215</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>9</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QTabWidget" name="UI_TABWIDGET_PARAM_SINGLEACQ">
          <property name="maximumSize">
           <size>
            <width>320</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="UI_W_Q1SCAN_SINGLEACQ">
           <attribute name="title">
            <string>Q1 Scan</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_Q1SCAN_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_Q3SCAN_SINGLEACQ">
           <attribute name="title">
            <string>Q3 Scan</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_Q3SCAN_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_Q1SIM_SINGLEACQ">
           <attribute name="title">
            <string>Q1 SIM</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_Q1SIM_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_Q3SIM_SINGLEACQ">
           <attribute name="title">
            <string>Q3 SIM</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_Q3SIM_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_MRM_SINGLEACQ">
           <attribute name="title">
            <string>MRM</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_MRM_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_PREIONSCAN_SINGLEACQ">
           <attribute name="title">
            <string>Precursor ion scan</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_PREIONSCAN_SINGLEACQ"/>
          </widget>
          <widget class="QWidget" name="UI_W_PROIONSCAN_SINGLEACQ">
           <attribute name="title">
            <string>Product ion scan</string>
           </attribute>
           <layout class="QHBoxLayout" name="UI_LAYOUT_PROIONSCAN_SINGLEACQ"/>
          </widget>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_2" native="true">
          <property name="maximumSize">
           <size>
            <width>320</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="label">
               <property name="text">
                <string>AcquisitionMode:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="UI_CB_ACQ_MODE_SINGLEACQ">
               <item>
                <property name="text">
                 <string>ADC</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>TDC</string>
                </property>
               </item>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="UI_PB_ADVANCE_SINGLEACQ">
               <property name="text">
                <string>Advance</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QLabel" name="label_2">
               <property name="text">
                <string>AcqFreq(Hz):</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="UI_LE_AcqFreq_SINGLEACQ">
               <property name="text">
                <string>1000000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_3">
               <property name="text">
                <string>AcqACC:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="UI_LE_AcqACC_SINGLEACQ">
               <property name="text">
                <string>32</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="UI_LAYOUT_CHART_TIC_SINGLEACQ">
       <item>
        <widget class="QScrollArea" name="UI_SCROLLAREA_CHART_SINGLEACQ">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>464</width>
            <height>433</height>
           </rect>
          </property>
          <layout class="QHBoxLayout" name="UI_LAYOUT_PROCESS"/>
         </widget>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="widget_3" native="true"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyWidget::sMyButton</class>
   <extends>QWidget</extends>
   <header>LibWidget/sMyButton.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
