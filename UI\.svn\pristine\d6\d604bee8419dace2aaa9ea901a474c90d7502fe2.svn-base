﻿#pragma once

#include "uiBaseParamEditor.h"
#include "ui_uiQ13SIMParamEditor.h"
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <QTableWidgetItem>
#include <uiTune/uiMapSetMZ.h>

class uiQ13SIMParamEditor : public uiBaseParamEditor//AbstractParamEditor
{
    Q_OBJECT
public:
    explicit uiQ13SIMParamEditor(QString Q,
                               QWidget* parent = nullptr);
    ~uiQ13SIMParamEditor(){}
    virtual void initClass(QString& filePath);

protected:
    virtual bool initUI(QString& filePath);

public:
    //void saveParameter() override;
    bool getTableParam(QVector<float>& mass,
                       QVector<float>& DwellTimeMs);

private slots:
    void on_chanel_Changed(QTableWidgetItem *item);
    void on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1);
    void on_lineEdit_pauseTime_textEdited(const QString &arg1);
    void on_lineEdit_eventTime_textEdited(const QString &arg1);

private:
    Ui::uiQ13SIMParamEditor ui;
    bool errorParam= false;
    void initPage();
    QTableWidgetItem *getItem(int r, int c);
    bool getWaitTimeMs(QString strPN_SwitchTimeMs, QString strPauseTimeMs,
                       QString strEventTimeMs);

};

