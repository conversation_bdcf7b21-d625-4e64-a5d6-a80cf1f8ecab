/**
 * @brief The CommandUSB class
 * CCS,ASG,DAQ指令的封装，内含DAQ数据处理线程，getDataDAQ为数据提取接口
 */
#pragma once
#include <QtCore/qglobal.h>
#include "cCommunicationCMD.h"
#include <../../../GlobalStruct/cPublicCCS.h>
#include <QFile>
#include <QTextStream>

#if defined(CONTROLCCS_LIBRARY)
#  define CONTROLCCS_LIBRARY_EXPORT Q_DECL_EXPORT
#else
#  define CONTROLCCS_LIBRARY_EXPORT Q_DECL_IMPORT
#endif

class CONTROLCCS_LIBRARY_EXPORT CommandUSB: public QObject
{
    Q_OBJECT
public:
    struct SFileHead{
        quint32 vid = 0; /**< TODO */
        quint32 pid = 0; /**< TODO */
        quint32 paramSize = 0; /**< TODO */
    };
    struct SDaqFileHead{
        quint32 accumulate; /**< TODO */
        quint32 framePoint; /**< TODO */
        double frequency; /**< TODO */
        double dateTime; /**< TODO */
        double massStart; /**< TODO */
        double massEnd; /**< TODO */
    };
    struct _DAQ_DATA:QMutex{
        volatile quint32 nVersion= 0;
        //QMutex qLock;
        QByteArray pBuff;//QVector<double> pBuff;
    };
    CommandUSB(QObject *parent = 0);
    ~CommandUSB();
    void clearBuff(){
        mSampledData.lock();//qLock.
        mSampledData.pBuff.clear();
        mSampledData.nVersion++;
        if(mSampledData.nVersion>40000)
            mSampledData.nVersion=1;
        mSampledData.unlock();//qLock.
    }
    inline int setDataDAQ(const QVector<uint>& pSrcData, const int nDaqShiftAcc,
                          _StreamBody::Type_Data typeData, QByteArray& pStream);
    //int getDataDAQ(_DAQ_DATA* p_DAQ_DATA, bool always = false);
    int getDataDAQ(QByteArray& srcData, bool always = false);
    //int getDataDAQ(std::vector<double>& srcData, bool always = false);
//    int getDataDAQ(char** srcData, quint32& nLength, bool always){
//        int fReturn=getDataDAQ(mDaqData, always);
//        *srcData= mDaqData.data();
//        nLength= mDaqData.size()/sizeof(double);
//        return fReturn;
//    }
    void zeroVersion(){
        mSampledData.lock();//.qLock
        mSampledData.nVersion= 0;
        mSampledData.unlock();//.qLock
    }
//    void renameFile(char* pName,int lengthName);
//    void renameFile(QString fileName);

private:
    SThread mProcessThread;
    CommunicationCMD mCommunicationCMD;
    QString save2File(QString rFileName="");
    int method2File(QFile* pFile, QString rFileName= "./tempMethodParam.dat");
    quint32 mVersionGet=1;
    _DAQ_DATA mSampledData;
    ParamCCS::_DAQ_CONFIG m_DAQ_CONFIG;
    static int processDataThread(void *pParam, const bool &bRunning);
    QByteArray mDaqData;
    _StreamBody::Type_Data mTypeData;

template<typename T>
    double fillData(const QVector<uint>& pSrcData, const int nDaqShiftAcc, const uint offset);

public:
    bool isRunningDAQ(){
        return mProcessThread.isRunning();
    }

    int safeCCS(double* pDAC= nullptr){
        return mCommunicationCMD.safeCCS(pDAC);
    }

    int updateCCS(char* pData, uint nLength){
        return mCommunicationCMD.updateCCS(pData, nLength);
    }

    int scanCCS(){
        return mCommunicationCMD.scanCCS();
    }

    int scanCCS(char* pData, quint32 nLength){
        return mCommunicationCMD.scanCCS(pData, nLength);
    }
    int scanCCS(QByteArray pArrayCCS){
        return mCommunicationCMD.scanCCS(pArrayCCS);
    }
    int stopCCS(){
        return mCommunicationCMD.stopCCS();
    }
    int scanASG(char* pData, quint32 nLength){
        return mCommunicationCMD.scanASG(pData, nLength);
    }
    int scanASG(QByteArray pArrayASG){
        return mCommunicationCMD.scanASG(pArrayASG);
    }
    int stopASG(){
        return mCommunicationCMD.stopASG();
    }
    int startDAQ(ParamCCS::_DAQ_CONFIG& p_DAQ_CONFIG, _StreamBody::Type_Data typeData){
        if(!mProcessThread.isRunning()){
            zeroVersion();
            memcpy(&m_DAQ_CONFIG, &p_DAQ_CONFIG, sizeof(ParamCCS::_DAQ_CONFIG));
            if(mCommunicationCMD.startDAQ(m_DAQ_CONFIG)== 0){
                mTypeData= typeData;
                mProcessThread.start();
                return 0;
            }
        }
        return -1;
    }

    int stopDAQ(){
        if(mProcessThread.isRunning()){
            mProcessThread.stop();
            mProcessThread.wait();
        }
        if(mCommunicationCMD.stopDAQ()== 0)
            return 0;
        else
            return -1;
    }

    int startPWM(ParamCCS::PWM_GENETATE& pPWM_GENETATE){
        return mCommunicationCMD.startPWM(pPWM_GENETATE);
    }
    int sendCMD(const char* pW, const quint32 nW, char* const pR, const quint32 nR){
        return mCommunicationCMD.sendCMD(pW, nW, pR, nR);
    }
    int sendCMD(const QByteArray& pArrayCMD, QByteArray& pArrayBack, quint32 sizeBack= 512){
        return mCommunicationCMD.sendCMD(pArrayCMD, pArrayBack, sizeBack);
    }

    _StreamBody::Type_Data currentTypeData(){
        return mTypeData;
    }
};

class sCommandUSB{
public:
    static CommandUSB* getCommandUSB(){
        static sCommandUSB insmCommandUSB;
        return &(insmCommandUSB.mCommandUSB);
    }
private:
    CommandUSB mCommandUSB;
    sCommandUSB(){}
    virtual ~sCommandUSB(){}
    sCommandUSB(const sCommandUSB&){}
    sCommandUSB& operator=(const sCommandUSB&){
        static sCommandUSB insCommandUSB;
        return insCommandUSB;
    }
};
