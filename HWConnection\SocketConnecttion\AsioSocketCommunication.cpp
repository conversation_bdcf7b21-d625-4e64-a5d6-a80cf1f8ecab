﻿#include "AsioSocketCommunication.h"
#include <iostream>
#include <fstream>

#include <QtConcurrent>
//#include "SocketPacketAbstractParser.h"
#include <QDebug>
#include "Instruction/DigitizerInstruction.h"

AsioSocketCommunication::AsioSocketCommunication(QObject *parent) : QThread(parent),
    m_socket(m_service), m_eParsingAction(FindFirstFrame),
    m_eParsingState(None),
    m_bConnected(false),
    m_readUMAQueue(128),
    m_packetDataQueue(10240),
    m_commandBackDataQueue(2)
//    m_writer("serialization.dat")
{
    m_nCurrentFrameHead = 0;
    m_writeBuff.reserve(2048);
//    m_fhLog.open("log.txt", std::ios::out);
    m_nRetry = 0;
    m_DataFlow = TOF;
    m_bDAQRunning = false;
    m_bFindHead = false;
    m_pUMADataIO = UMADataIO::GetInstance();
    m_isCCSCalibration = false;
//    m_parseBuff.reserve(sizeof (char) * 1024 * 1024 *1024);
}

AsioSocketCommunication::~AsioSocketCommunication()
{
//    m_fhLog.close();
    disconnectFromHost();
//    stopDAQ();
    requestInterruption();
    terminate();
    wait(1000);

//    if (isRunning()) requestInterruption();

    ///< Clear the buff queue.
    clearBuff();
//    msleep(1000);
}

bool AsioSocketCommunication::isConnected() const
{
    return m_bConnected;
}

void AsioSocketCommunication::connectToHost(const QString &host, const QString &port)
{
    std::function<void()> func = [=](){
        if (isConnected())
            return;
        m_bDAQRunning = false;
        boost::asio::ip::tcp::endpoint endpoint(boost::asio::ip::address::from_string(host.toStdString()), port.toUShort());
        try
        {
//            m_socket.open(boost::asio::ip::tcp::v4());
            m_socket.connect(endpoint);
            m_bConnected = true;
            QtConcurrent::run(this, &AsioSocketCommunication::readHandler);
            emit(connectStateChanged(m_bConnected));
    //        QtConcurrent::run(this, &AsioSocketCommunication::readHandler);
    //        QtConcurrent::run(this, &AsioSocketCommunication::writeHandler);
    //        start();
    //        m_recv.open("D:/tmp/recv.dat", std::ios::out|std::ios::binary);

        }
        catch (std::exception const& e)
        {
            std::cout << e.what() << std::endl;
            m_bConnected = false;
            emit(connectStateChanged(m_bConnected));
            requestInterruption();
        }


    };

    QtConcurrent::run(func);
}

void AsioSocketCommunication::disconnectFromHost()
{
    if (!m_bConnected)  return;
    msleep(500);
    try {
        if(m_bDAQRunning)
        {
            stopDAQ();
        }
        m_socket.shutdown(boost::asio::ip::tcp::socket::shutdown_both);
        m_socket.cancel();
        m_socket.close();
    } catch (std::exception const& e) {
        qDebug()<< "std::exception:" << e.what();
    }
    m_bConnected = false;
    emit(connectStateChanged(m_bConnected));
    qDebug() << "disconnected from the host.";
//    requestInterruption();
    m_parseBuff.clear();
//    m_recv.close();
}

bool AsioSocketCommunication::send(const QList<QByteArray> &byteList)
{
    m_sendBuffList = byteList;
    QtConcurrent::run(this, &AsioSocketCommunication::writeHandler);
    return true;
}

bool AsioSocketCommunication::send(const QByteArray &byte, int nSize)
{
    try {
        boost::system::error_code errorSend;
        if (m_bConnected) {
            //Write Data;
            size_t nBuff = boost::asio::write(m_socket, boost::asio::buffer(byte.data(), static_cast<size_t>(nSize)), errorSend);
            qDebug() << "Send" << QByteArray(byte.data(), nSize).toHex() << "sended Size:" << nBuff;
            if (errorSend)
            {
                qCritical() << "write failed:" << QString::fromStdString(errorSend.message());
                disconnectFromHost();
                return false;
            }

            if (nBuff != static_cast<size_t>(nSize))
            {
                qCritical() << "Write data not match.";
                return false;
            }
            return true;
        }
    } catch (...) {
        disconnectFromHost();
        return false;
    }

    return false;
}

bool AsioSocketCommunication::sendForBack(const QByteArray &byte, int nSize, QByteArray &back, int nTimeout)
{
    try {
        while(!m_commandBackDataQueue.empty())
        {
            Packet * packet=nullptr;
            if(m_commandBackDataQueue.pop(packet)) {
                if (packet!=nullptr) {
                    delete packet;
                }
            }
        }
        boost::system::error_code errorSend;
        if (m_bConnected && !m_bDAQRunning) {
            QByteArray byte2;
            byte2.resize(128);
            int nSize = 0;
            DigitizerInstruction::SetState(byte2.data(), nSize);
            if (!send(byte2, nSize))  qDebug() << "Set State Error.";
            msleep(1);
            DigitizerInstruction::ResetState(byte2.data(), nSize);
            if (!send(byte2, nSize))  qDebug() << "Reset DAQ Error.";
            msleep(1);

            //Write Data;
            size_t nBuff = boost::asio::write(m_socket, boost::asio::buffer(byte.data(), static_cast<size_t>(nSize)), errorSend);
            qDebug() << "SendForBack" << QByteArray(byte.data(), nBuff).toHex();
            if (errorSend)
            {
                qCritical() << "write failed:" << QString::fromStdString(errorSend.message());
                disconnectFromHost();
                return false;
            }

            if (nBuff != static_cast<size_t>(nSize))
            {
                qCritical() << "Write data not match.";
                return false;
            }
            QTime time;
            time.start();
            while(time.elapsed() < nTimeout)
            {
                Packet * packet=nullptr;
                if (m_commandBackDataQueue.empty())
                {
                    continue;
                }
                if(m_commandBackDataQueue.pop(packet)) {
                    if (packet!=nullptr) {
                        back.setRawData(packet->data, packet->nSize);
                        delete packet;
                        return true;
                    }
                }
            }
            //        #if TOF_DEBUG
//            boost::system::error_code errorRecv;
            //            while(nBuff == 0)
            //                nBuff = boost::asio::read(m_socket, boost::asio::buffer(back.data(), static_cast<size_t>(PACKET_MAX_SIZE)), boost::asio::transfer_at_least(0), errorRecv);

            ////            nBuff = boost::asio::read(m_socket, boost::asio::buffer(buff.data(), static_cast<size_t>(buff.size())), boost::asio::transfer_all(), errorRecv);

            //            qDebug() << "Read Buff Size:" << nBuff;
            //            if (nBuff <= 0) return false;
            //            back.resize(nBuff);
            //            if (errorRecv) {
            //                qCritical() << "write failed:" << QString::fromStdString(errorRecv.message());
            //                if (errorRecv.value() == boost::asio::error::eof) {
            //                    back.resize(nBuff);
            //                    return true;
            //                }
            //                return false;
            //            }
            //        #endif
            return false;
        }
    } catch (...) {
        disconnectFromHost();
    }

    return false;

}

//bool AsioSocketCommunication::rename(const std::string &fileName)
//{
//    return m_writer.rename(fileName);
//}

bool AsioSocketCommunication::switchToTOF()
{
    int nSize = 0;
    QByteArray byte, retArray;
    byte.resize(128);
    retArray.resize(1024);
    DigitizerInstruction::SwitchToTOF(byte.data(), nSize);
    bool bRet = sendForBack(byte, nSize, retArray);
    qDebug() << "SendForBack" << bRet;
    if (bRet)   m_DataFlow = TOF;
    return bRet;
}

bool AsioSocketCommunication::switchToUMA()
{
    int nSize = 0;
    QByteArray byte, retArray;
    byte.resize(128);
    retArray.resize(1024);
    DigitizerInstruction::SwitchToUMA(byte.data(), nSize);
    bool bRet = sendForBack(byte, nSize, retArray);
    qDebug() << "SendForBack" << bRet;
    if (bRet)   m_DataFlow = UMA;
    return bRet;
}

bool AsioSocketCommunication::startDAQ()
{
    if (!stopDAQ()) return false;
    clearBuff();
    qDebug() << "Clear Buff";
    m_pUMADataIO->open("tmpData", std::ios::out);
    std::string head("DefualtHead");
    m_pUMADataIO->writeHeadInfo(head.c_str(), head.size());
    m_bDAQRunning = true;
//    QtConcurrent::run(this, &AsioSocketCommunication::readHandler);
    QtConcurrent::run(this, &AsioSocketCommunication::parseHandler);
//    m_buffWriter.open("Data.dat", std::fstream::out|std::fstream::binary);
    start();
    qDebug() << "Start Socket DAQ";
    ///< Start DAQ instruction to Digitizer
    QByteArray byte;
    byte.resize(128);
    int nSize = 0;
    DigitizerInstruction::StartDAQ(byte.data(), nSize);
    if (!send(byte, nSize)) {
        stopDAQ();
        return false;
    }
    return true;
}

bool AsioSocketCommunication::stopDAQ()
{
    QByteArray byte;
    byte.resize(128);
    int nSize = 0;
    m_bDAQRunning = false;
    DigitizerInstruction::StopDAQ(byte.data(), nSize);
    if (!send(byte, nSize)) qDebug() << "Stop DAQ Error.";
    DigitizerInstruction::SetState(byte.data(), nSize);
    if (!send(byte, nSize))  qDebug() << "Set State Error.";
    msleep(100);
    DigitizerInstruction::ResetState(byte.data(), nSize);
    if (!send(byte, nSize))  qDebug() << "Reset DAQ Error.";
    msleep(100);
    requestInterruption();
//    wait(1000);
    clearBuff();
//    m_pUMADataIO->close();
//    m_buffWriter.close();
    qDebug() << "Stop Socket DAQ";
    ///< Stop Digitizer and reset state
    return true;
}

bool AsioSocketCommunication::isCCSCalibration()
{
    return m_isCCSCalibration;
}

void AsioSocketCommunication::setCCSCalibration(bool b)
{
    m_isCCSCalibration = b;
}


//bool AsioSocketCommunication::getPacket(AsioSocketCommunication::Packet &p)
//{
//    if (m_readBuffQueue.empty())    return false;
//    m_readBuffQueue.pop(p);
//    return true;
//}


/**
 * @brief AsioSocketCommunication::parse
 * Overwrite this method to parse your own data
 */
void AsioSocketCommunication::parse(char *packet, const int &nSize)
{
#if 1
    m_parseBuff.append(packet, nSize);
    if (m_parseBuff.size() < sizeof (FrameHead))
        return;
    FrameHead * pFrameHead = nullptr;
    UMAHead * pUMAHead = nullptr;
    PeakHead * pPeakHead;
    PeakInfo * pPeakInfo = nullptr;
//    BlockData * pData = nullptr;
    int nOffset = 0;
    int nPeak = 0;// unused
    int nUMA = 0;
    int nPeakHead = 0;
    double dMobi = 0;
    double fTIC = 0;
    int * pDataX;
    int * pDataY;
    bool validFrame = false;

    switch (m_eParsingAction) {
    case FindFirstFrame:
        if (m_parseBuff.size() < sizeof (UMAHead)) {
            QThread::msleep(10);
            return;
        }
        pUMAHead = reinterpret_cast<UMAHead *>(m_parseBuff.data());
        if (pUMAHead->checkHead()) {
            m_eParsingState = HeadFound;
            nUMA = pUMAHead->nUMA;
            m_writeBuff.clear();
            m_writeBuff.reserve(4096);
            m_writeBuff.append(m_parseBuff.data(), sizeof (UMAHead));
            m_parseBuff.remove(0, sizeof (UMAHead));
            pFrameHead = reinterpret_cast<FrameHead*>(m_parseBuff.data());
            m_bFindHead = true;
            if(pFrameHead->firstFrame()) {
                m_eParsingState = FirstFrameFound;
                m_eParsingAction = WaitForFrameData;
                m_pFrame = new UMA::DataFrame(pFrameHead->frameNum(), pFrameHead->nPeak);
                nPeak = pFrameHead->nPeak;// unused
                m_pUMA = new UMA::UMAFrame(nUMA);
                qDebug() << "UMA Scan No:\t" << m_pUMA->Id() << "\t";
                m_writeBuff.append(m_parseBuff.data(), sizeof (FrameHead));
                m_parseBuff.remove(0, sizeof (FrameHead));
                m_pMobiY.clear();
            } else {
                //First frame error.
                m_eParsingState = DropData;
                m_eParsingAction = FindFirstFrame;
            }
        } else
        {
            //Could not find the packet head.
            while(m_nRetry++ < 1024000) {
                if (m_parseBuff.size() < sizeof (UMAHead)) {
                    return;
                }
                pUMAHead = reinterpret_cast<UMAHead *>(m_parseBuff.data());
                if (m_bFindHead)    qDebug() << "Recurse Parsing First Head";
                m_bFindHead = false;
                if (pUMAHead->checkHead()) {
                    return;
                } else {
                   m_parseBuff.remove(0, sizeof(char));
                }
            }
        }
        break;
    case WaitForFrameData:
        if (m_pFrame->m_nPeak * sizeof (unsigned short) * 2 + sizeof (PeakHead) * 10 > m_parseBuff.size()) break;
        if (m_pFrame->m_nPeak > 2)
        {
            validFrame = true;
        }
        //Parsing Data
        if (m_pUMA != nullptr) {
            nPeakHead = 0;
            for (int nCount = 0; nCount < m_pFrame->m_nPeak;) {
                pPeakHead = reinterpret_cast<PeakHead *>(m_parseBuff.data() + nPeakHead * sizeof (PeakHead) + nCount * sizeof (PeakInfo));
                nOffset = pPeakHead->offset() * 0x10000;
                nPeakHead++;
                if (pPeakHead->nPeak + nCount > m_pFrame->m_nPeak)
                    break;
                for (int nId = 0; nId < pPeakHead->nPeak; nId++) {
                    pPeakInfo = reinterpret_cast<PeakInfo *>(m_parseBuff.data() + sizeof (PeakHead) * nPeakHead + nCount * sizeof (PeakInfo));
                    DataPoint pt(pPeakInfo->nPos+nOffset, pPeakInfo->nInt);
                    if (validFrame)
                    {
                        dMobi += pt.y;
                    }
                    else
                    {
                        pt.y = 0;
                    }
                    m_pFrame->append(pt);
                    nCount++;
                }
            }
            m_eParsingAction = FindLastFrame;
            if (m_eParsingState == LastFrameFound) m_eParsingAction = PostAction;

            //Check The frame correction
            if (m_pFrame->m_nPeak == m_pFrame->size()) {
                m_writeBuff.append(m_parseBuff.data(), sizeof (PeakHead) * nPeakHead + sizeof (PeakInfo) * m_pFrame->size());
                m_parseBuff.remove(0, sizeof (PeakHead) * nPeakHead + sizeof (PeakInfo) * m_pFrame->size());

                m_pMobiY.append(dMobi);
                m_pUMA->append(m_pFrame);

                break;   ///< Check frame ok.
            }else {
                emit(lostFrame());
                qDebug() << "Lost frame data" << m_pFrame->m_nPeak << m_pFrame->size();
                m_parseBuff.clear();
            }
            delete m_pUMA;
            m_pUMA = nullptr;
            m_eParsingAction = FindFirstFrame;
            m_eParsingState = DropData;
        } else {
            //Drop data
            for (int nCount = 0; nCount < m_pFrame->m_nPeak;) {
                pPeakHead = reinterpret_cast<PeakHead *>(m_parseBuff.data());
                nCount += pPeakHead->nPeak;
                m_parseBuff.remove(0, sizeof (PeakHead) + sizeof (PeakInfo) * pPeakHead->nPeak);
            }
            delete m_pFrame;
            m_eParsingAction = FindFirstFrame;
            //Drop to Last Frame
        }
        break;
    case FindLastFrame:
        if (m_parseBuff.size() < sizeof (FrameHead))
            return;
        pFrameHead = reinterpret_cast<FrameHead *>(m_parseBuff.data());
        m_eParsingAction = WaitForFrameData;
        m_pFrame = new UMA::DataFrame(pFrameHead->frameNum(), pFrameHead->nPeak);
        if(pFrameHead->lastFrame()) {
            m_eParsingState = LastFrameFound;
        }
        m_writeBuff.append(m_parseBuff.data(), sizeof (FrameHead));
        m_parseBuff.remove(0, sizeof (FrameHead));
        break;
    case PostAction:
        m_eParsingAction = FindFirstFrame;
        m_eParsingState = None;

        fTIC = std::accumulate(m_pMobiY.begin(), m_pMobiY.end(), 0.0);
        m_dChroTime += m_chroTimer.elapsed() / 1000.0 / 60.0; //convert to minute
        qDebug() << "push one UMA Frame, use" << m_chroTimer.elapsed() << "ms";
        m_chroTimer.restart();

        m_pUMADataIO->writeData(fTIC, m_dChroTime, m_pUMA->Id(), m_writeBuff.data(), m_writeBuff.size());
        m_pUMA->ticValue = fTIC;
        m_pUMA->ticTime = m_dChroTime;
        m_readUMAQueue.push(m_pUMA);
        qDebug() << "push one Frame, clock" << m_dChroTime << "min";
        break;
    default:
        break;
    }
#endif
}

/**
 * @brief AsioSocketCommunication::run
 *
 * Default display processing thread function
 * Not enabled (default).
 */
void AsioSocketCommunication::run()
{
#if 0
    qDebug() << "Thread running";
//    m_writer.open();
    while(!isInterruptionRequested()) {
        while(!m_readUMAQueue.empty()) {
            UMA::UMAFrame * pFrame = nullptr;
            m_readUMAQueue.pop(pFrame);
            if (pFrame == nullptr) break;
            qDebug() << "Read MS Data" << "Frame Size" << pFrame->size();
            UMA::MSData msData;
            UMADisplayBuff * pDispBuff = new UMADisplayBuff;
            pDispBuff->MobiBuff.nSize = pFrame->size();
            pDispBuff->MobiBuff.pX = new double[pFrame->size()];
            pDispBuff->MobiBuff.pY = new double[pFrame->size()];

            const float fSize = pFrame->size();
            for (size_t i = 0; i < pFrame->size(); ++i) {
                pDispBuff->MobiBuff.pX[i] = i;
                pDispBuff->MobiBuff.pY[i] = 0;
                UMA::DataFrame * pData = pFrame->data()[i];
                for (size_t j = 0; j < pData->size(); ++j) {

                    msData.insert(pData->data()[j]);
                    pDispBuff->MobiBuff.pY[i] += pData->data()[j].y;
                }
//                delete pData;
            }
            msData.sort();
            double * pX = new double[msData.size()];
            double * pY = new double[msData.size()];
            int * pDataX = msData.dataX();
            int * pDataY = msData.dataY();
            double fTIC = 0;
            for (size_t i = 0; i < msData.size(); ++i) {
                pX[i] = pDataX[i];
                pY[i] = pDataY[i]/fSize;
                fTIC += pY[i];
            }
            pDispBuff->nUMA = pFrame->Id();
            pDispBuff->nTIC = fTIC;
//            BlockData * pBlockData = nullptr;
//            m_blockDataQueue.pop(pBlockData);
//            if (pBlockData != nullptr) {
//                m_writer.writeData(static_cast<int>(fTIC), pFrame->Id(), pBlockData->pData, pBlockData->nSize);
//                delete [] pBlockData->pData;
//                delete pBlockData;
//            }
            delete pFrame;
            qDebug() << msData.size();
            pDispBuff->MassBuff.pX = pX;
            pDispBuff->MassBuff.pY = pY;
            pDispBuff->MassBuff.nSize = msData.size();
            m_drawQueue.push(pDispBuff);
            qDebug() << "push one Frame";
        }
        msleep(100);
    }
//    m_writer.close();
#endif
}

bool AsioSocketCommunication::writeHandler()
{
    boost::system::error_code error;
    QTime time;
    time.start();
    if (m_bConnected) {
        //Write Data;
        for (int i = 0; i < m_sendBuffList.size(); ++i) {
            //Check Wheather the server is disconnected.
            QByteArray data = m_sendBuffList[i];
            size_t nBuff = boost::asio::write(m_socket, boost::asio::buffer(data.data(), static_cast<size_t>(data.size())), error);
            if (nBuff != static_cast<size_t>(data.size()))
                qCritical() << "Write data not match.";
            if (error)
            {
                qCritical() << "write failed:" << QString::fromStdString(error.message());
                disconnectFromHost();
                break;
            }
        }
    }

    return true;
}

bool AsioSocketCommunication::readHandler()
{
    boost::system::error_code error;
    Packet * packet;
    m_parseBuff.reserve(MAX_FRAME_SIZE);
//    qDebug() << "DAQ Start" << std::endl;
    while (m_bConnected)
    {
        if (!m_bConnected)  return false;
        packet = new Packet;
        size_t nBuff = boost::asio::read(m_socket, boost::asio::buffer(packet->data, static_cast<size_t>(PACKET_MAX_SIZE)), boost::asio::transfer_at_least(1), error);
        if (error)
        {
            if (error.value() == boost::asio::error::eof)
            {
                ///< Read the end of the data
                if (m_bDAQRunning) {
                    if (nBuff > 0)
                    {
                        packet->nSize = nBuff;
                        m_packetDataQueue.push(packet);
                        usleep(10);
                        continue;
                    }
                }

            }
            delete packet;
//            qCritical() << "read failed:" << QString::fromStdString(error.message());
            if (error.message() == "An existing connection was forcibly closed by the remote host")
                disconnectFromHost();
            continue;
        }
        packet->nSize = nBuff;
        if (m_bDAQRunning) {
            m_packetDataQueue.push(packet);
            continue;
        } else
        {
            m_commandBackDataQueue.push(packet);
            continue;
        }
        delete packet;
    }
    qDebug() << "DAQ Stop";
    return true;
}

bool AsioSocketCommunication::parseHandler()
{
    m_chroTimer.restart();
    m_dChroTime = 0;
    while (m_bDAQRunning)
    {
        if (!m_bConnected)  return false;
        Packet * packet = nullptr;
        if (!m_packetDataQueue.empty()) {
            if (m_packetDataQueue.pop(packet)) {
                if (packet != nullptr)  {
//                    m_buffWriter.write(packet->data, packet->nSize);
                    parse(packet->data, packet->nSize);
                    delete packet;
                }
            }
        } else {
            parse(nullptr, 0);
        }
    }
//    if (m_pUMA != nullptr) delete m_pUMA;
//    m_pUMA = nullptr;
    return true;
}

void AsioSocketCommunication::clearBuff()
{
    m_parseBuff.clear();
    while(!m_readUMAQueue.empty()) {
        UMA::UMAFrame * pUMA;
        m_readUMAQueue.pop(pUMA);
        delete pUMA;
    }

    while (!m_packetDataQueue.empty()) {
        Packet * packet = nullptr;
        if (m_packetDataQueue.pop(packet)) {
            if (packet != nullptr)  {
                delete packet;
            }
        }
    }

    while(!m_commandBackDataQueue.empty())
    {
        Packet * packet=nullptr;
        if(m_commandBackDataQueue.pop(packet)) {
            if (packet!=nullptr) {
                delete packet;
            }
        }
    }
}


