<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiQ13SIMParamEditor</class>
 <widget class="QWidget" name="uiQ13SIMParamEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>457</width>
    <height>470</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="uiQ13SIMParamEditorWidget" native="true">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>260</y>
     <width>278</width>
     <height>214</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QTableWidget" name="tableWidget_chanel">
      <property name="alternatingRowColors">
       <bool>true</bool>
      </property>
      <property name="selectionMode">
       <enum>QAbstractItemView::SingleSelection</enum>
      </property>
      <property name="selectionBehavior">
       <enum>QAbstractItemView::SelectItems</enum>
      </property>
      <attribute name="horizontalHeaderMinimumSectionSize">
       <number>40</number>
      </attribute>
      <attribute name="horizontalHeaderStretchLastSection">
       <bool>true</bool>
      </attribute>
      <attribute name="verticalHeaderDefaultSectionSize">
       <number>28</number>
      </attribute>
      <column>
       <property name="text">
        <string>m/z</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Dwell Time (msec)</string>
       </property>
      </column>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
