/********************************************************************************
** Form generated from reading UI file 'uiTune.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UITUNE_H
#define UI_UITUNE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiTune
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *UI_W_TOOL_BAR_TUNE;
    QHBoxLayout *horizontalLayout;
    QTabWidget *tabWidget;
    QWidget *UI_TAB_VOLTAGETABLE_TUNE;
    QVBoxLayout *UI_LAYOUT_VOLTAGETABLE_TUNE;
    QWidget *UI_TAB_CAL_TUNE;
    QVBoxLayout *UI_LAYOUT_CAL_TUNE;

    void setupUi(QWidget *uiTune)
    {
        if (uiTune->objectName().isEmpty())
            uiTune->setObjectName(QString::fromUtf8("uiTune"));
        uiTune->resize(1097, 601);
        verticalLayout = new QVBoxLayout(uiTune);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        UI_W_TOOL_BAR_TUNE = new QWidget(uiTune);
        UI_W_TOOL_BAR_TUNE->setObjectName(QString::fromUtf8("UI_W_TOOL_BAR_TUNE"));
        UI_W_TOOL_BAR_TUNE->setMinimumSize(QSize(0, 50));
        horizontalLayout = new QHBoxLayout(UI_W_TOOL_BAR_TUNE);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));

        verticalLayout->addWidget(UI_W_TOOL_BAR_TUNE);

        tabWidget = new QTabWidget(uiTune);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        UI_TAB_VOLTAGETABLE_TUNE = new QWidget();
        UI_TAB_VOLTAGETABLE_TUNE->setObjectName(QString::fromUtf8("UI_TAB_VOLTAGETABLE_TUNE"));
        UI_LAYOUT_VOLTAGETABLE_TUNE = new QVBoxLayout(UI_TAB_VOLTAGETABLE_TUNE);
        UI_LAYOUT_VOLTAGETABLE_TUNE->setObjectName(QString::fromUtf8("UI_LAYOUT_VOLTAGETABLE_TUNE"));
        UI_LAYOUT_VOLTAGETABLE_TUNE->setContentsMargins(0, 0, 0, 0);
        tabWidget->addTab(UI_TAB_VOLTAGETABLE_TUNE, QString());
        UI_TAB_CAL_TUNE = new QWidget();
        UI_TAB_CAL_TUNE->setObjectName(QString::fromUtf8("UI_TAB_CAL_TUNE"));
        UI_LAYOUT_CAL_TUNE = new QVBoxLayout(UI_TAB_CAL_TUNE);
        UI_LAYOUT_CAL_TUNE->setObjectName(QString::fromUtf8("UI_LAYOUT_CAL_TUNE"));
        UI_LAYOUT_CAL_TUNE->setContentsMargins(0, 0, 0, 0);
        tabWidget->addTab(UI_TAB_CAL_TUNE, QString());

        verticalLayout->addWidget(tabWidget);


        retranslateUi(uiTune);

        tabWidget->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(uiTune);
    } // setupUi

    void retranslateUi(QWidget *uiTune)
    {
        uiTune->setWindowTitle(QApplication::translate("uiTune", "Form", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(UI_TAB_VOLTAGETABLE_TUNE), QApplication::translate("uiTune", "Voltage Table", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(UI_TAB_CAL_TUNE), QApplication::translate("uiTune", "Calibration", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiTune: public Ui_uiTune {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UITUNE_H
