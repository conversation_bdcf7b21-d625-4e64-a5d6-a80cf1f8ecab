<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>sTICList</class>
 <widget class="QWidget" name="sTICList">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>122</width>
    <height>300</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>150</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>sTICList</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QListWidget" name="listWidget">
     <property name="autoFillBackground">
      <bool>false</bool>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color:transparent</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLineEdit" name="lineEdit"/>
     </item>
     <item>
      <widget class="QPushButton" name="BtnAdd">
       <property name="text">
        <string>Add</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
