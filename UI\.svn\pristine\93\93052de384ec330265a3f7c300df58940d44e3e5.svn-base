<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiIonScanParamEditor</class>
 <widget class="QWidget" name="uiIonScanParamEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>590</width>
    <height>435</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="uiIonScanParamEditorWidget" native="true">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>90</y>
     <width>340</width>
     <height>179</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <layout class="QGridLayout" name="gridLayout" columnstretch="3,0">
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="horizontalSpacing">
       <number>5</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="1" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>End m/z:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_IonMZ">
        <property name="text">
         <string>Precursor Ion m/z:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="lineEdit_ionMZ"/>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="lineEdit_scanSpeed_rOnly">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEdit_startMZ"/>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>Start m/z:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="lineEdit_endMZ">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_scanSpeed">
        <property name="text">
         <string>Scan Speed(u/sec):</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
