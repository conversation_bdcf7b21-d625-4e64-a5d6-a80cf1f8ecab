/****************************************************************************
** Meta object code from reading C++ file 'uiInitMainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiMainWindow/uiInitMainWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiInitMainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiInitMainWindow_t {
    QByteArrayData data[9];
    char stringdata0[204];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiInitMainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiInitMainWindow_t qt_meta_stringdata_uiInitMainWindow = {
    {
QT_MOC_LITERAL(0, 0, 16), // "uiInitMainWindow"
QT_MOC_LITERAL(1, 17, 24), // "onUI_MPB_MENU_MAIN_MAINW"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 26), // "onUI_MPB_MENU_SYSTEM_MAINW"
QT_MOC_LITERAL(4, 70, 19), // "onUI_MPB_LIST_MAINW"
QT_MOC_LITERAL(5, 90, 31), // "onUI_MPB_MENU_CREATE_LIST_MAINW"
QT_MOC_LITERAL(6, 122, 30), // "onUI_MPB_MENU_SINAL_SCAN_MAINW"
QT_MOC_LITERAL(7, 153, 24), // "onUI_MPB_MENU_TUNE_MAINW"
QT_MOC_LITERAL(8, 178, 25) // "onUI_MPB_MENU_STATE_MAINW"

    },
    "uiInitMainWindow\0onUI_MPB_MENU_MAIN_MAINW\0"
    "\0onUI_MPB_MENU_SYSTEM_MAINW\0"
    "onUI_MPB_LIST_MAINW\0onUI_MPB_MENU_CREATE_LIST_MAINW\0"
    "onUI_MPB_MENU_SINAL_SCAN_MAINW\0"
    "onUI_MPB_MENU_TUNE_MAINW\0"
    "onUI_MPB_MENU_STATE_MAINW"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiInitMainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x08 /* Private */,
       3,    0,   50,    2, 0x08 /* Private */,
       4,    0,   51,    2, 0x08 /* Private */,
       5,    0,   52,    2, 0x08 /* Private */,
       6,    0,   53,    2, 0x08 /* Private */,
       7,    0,   54,    2, 0x08 /* Private */,
       8,    0,   55,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiInitMainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiInitMainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onUI_MPB_MENU_MAIN_MAINW(); break;
        case 1: _t->onUI_MPB_MENU_SYSTEM_MAINW(); break;
        case 2: _t->onUI_MPB_LIST_MAINW(); break;
        case 3: _t->onUI_MPB_MENU_CREATE_LIST_MAINW(); break;
        case 4: _t->onUI_MPB_MENU_SINAL_SCAN_MAINW(); break;
        case 5: _t->onUI_MPB_MENU_TUNE_MAINW(); break;
        case 6: _t->onUI_MPB_MENU_STATE_MAINW(); break;
        default: ;
        }
    }
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject uiInitMainWindow::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_uiInitMainWindow.data,
    qt_meta_data_uiInitMainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiInitMainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiInitMainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiInitMainWindow.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int uiInitMainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
