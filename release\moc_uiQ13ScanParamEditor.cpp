/****************************************************************************
** Meta object code from reading C++ file 'uiQ13ScanParamEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiSingleAcquisition/uiQ13ScanParamEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiQ13ScanParamEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiQ13ScanParamEditor_t {
    QByteArrayData data[10];
    char stringdata0[208];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiQ13ScanParamEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiQ13ScanParamEditor_t qt_meta_stringdata_uiQ13ScanParamEditor = {
    {
QT_MOC_LITERAL(0, 0, 20), // "uiQ13ScanParamEditor"
QT_MOC_LITERAL(1, 21, 12), // "updateFreAcq"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 5), // "speed"
QT_MOC_LITERAL(4, 41, 27), // "onLineEdit_endMZ_TextEdited"
QT_MOC_LITERAL(5, 69, 4), // "arg1"
QT_MOC_LITERAL(6, 74, 29), // "onLineEdit_startMZ_TextEdited"
QT_MOC_LITERAL(7, 104, 30), // "onLineEdit_pauseTimeTextEdited"
QT_MOC_LITERAL(8, 135, 41), // "onLineEdit_Polarity_switch_ti..."
QT_MOC_LITERAL(9, 177, 30) // "onLineEdit_eventTimeTextEdited"

    },
    "uiQ13ScanParamEditor\0updateFreAcq\0\0"
    "speed\0onLineEdit_endMZ_TextEdited\0"
    "arg1\0onLineEdit_startMZ_TextEdited\0"
    "onLineEdit_pauseTimeTextEdited\0"
    "onLineEdit_Polarity_switch_timeTextEdited\0"
    "onLineEdit_eventTimeTextEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiQ13ScanParamEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    1,   47,    2, 0x08 /* Private */,
       6,    1,   50,    2, 0x08 /* Private */,
       7,    1,   53,    2, 0x08 /* Private */,
       8,    1,   56,    2, 0x08 /* Private */,
       9,    1,   59,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,

       0        // eod
};

void uiQ13ScanParamEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiQ13ScanParamEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->updateFreAcq((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->onLineEdit_endMZ_TextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->onLineEdit_startMZ_TextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->onLineEdit_pauseTimeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->onLineEdit_Polarity_switch_timeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->onLineEdit_eventTimeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiQ13ScanParamEditor::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiQ13ScanParamEditor::updateFreAcq)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiQ13ScanParamEditor::staticMetaObject = { {
    &uiBaseParamEditor::staticMetaObject,
    qt_meta_stringdata_uiQ13ScanParamEditor.data,
    qt_meta_data_uiQ13ScanParamEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiQ13ScanParamEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiQ13ScanParamEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiQ13ScanParamEditor.stringdata0))
        return static_cast<void*>(this);
    return uiBaseParamEditor::qt_metacast(_clname);
}

int uiQ13ScanParamEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = uiBaseParamEditor::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void uiQ13ScanParamEditor::updateFreAcq(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
