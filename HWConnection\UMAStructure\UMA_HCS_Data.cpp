﻿#include "UMA_HCS_Data.h"

using namespace UMA;
using namespace UMA_HCS;

//HCSDataFileMeta::HCSDataFileMeta() : MetaInfoBase()
//{

//}

//HCSDataFileMeta::~HCSDataFileMeta()
//{

//}

//uint64_t HCSDataFileMeta::size() const
//{
//    return mBuffer.size();
//}

//const void *HCSDataFileMeta::data() const
//{
//    return &mBuffer[0];
//}

//bool HCSDataFileMeta::deserialize(char *data, uint64_t size)
//{
//    if (size < sizeof(uint64_t) * 5ULL)
//    {
//        return false;
//    }
//    uint64_t lOffset = sizeof(uint64_t) * 6ULL;
//    const uint64_t lEvents = *reinterpret_cast<uint64_t*>(&data[sizeof(uint64_t) * 4ULL]);
//    const uint64_t lEventBodySize = *reinterpret_cast<uint64_t*>(&data[sizeof(uint64_t) * 5ULL]);
//    if (lOffset + lEventBodySize != size)
//    {
//        return false;
//    }
//    mData.events.resize(lEvents);
//    memcpy(&mData, data, sizeof(uint64_t) * 4ULL);
//    for (uint64_t eventNo=0;eventNo < lEvents; ++eventNo)
//    {
//        mData.events[eventNo].deserialize(data + lOffset, size - lOffset);
//        lOffset += *reinterpret_cast<uint64_t*>(data + lOffset);
//    }
//    prepareBuffer(mData);
//    return true;
//}

//uint64_t HCSDataFileMeta::magicId() const
//{
//    return MAGIC_ID_UMA_HCS_DATA_FILE_META;
//}

//void HCSDataFileMeta::setData(const HCSDataFileMeta::HCSFileMetaData &_data)
//{
//    mData = _data;
//    prepareBuffer(mData);
//}

//const HCSDataFileMeta::HCSFileMetaData& HCSDataFileMeta::innerData() const
//{
//    return mData;
//}

//void HCSDataFileMeta::prepareBuffer(const HCSDataFileMeta::HCSFileMetaData &_data)
//{
//    const uint64_t lEvents = _data.events.size();
//    mBuffer.resize(sizeof(uint64_t) * 6ULL);
//    memcpy(&mBuffer[0], &_data, sizeof(uint64_t) * 4ULL);
//    *reinterpret_cast<uint64_t*>(&mBuffer[sizeof(uint64_t) * 4ULL]) = lEvents;
//    uint64_t lOffset = mBuffer.size();
//    uint64_t lSize;
//    uint64_t lEventBodySize = 0;
//    std::vector<char> tmpBuffer;
//    for(uint64_t eventNo = 0; eventNo < lEvents; ++eventNo)
//    {
//        auto pEvent = &_data.events[eventNo];
//        tmpBuffer.resize(sizeof(uint64_t) * 5
//                         + pEvent->channels.size() * sizeof(Event_Channel));
//        if (pEvent->serialize(&tmpBuffer[0], tmpBuffer.size(), lSize))
//        {
//            lOffset = mBuffer.size();
//            mBuffer.resize(lOffset + lSize);
//            memcpy(&mBuffer[lOffset], &tmpBuffer[0], lSize);
//            lEventBodySize += lSize;
//        }else
//        {
//            std::cerr << "Serialize the UMA_Event error" << std::endl;
//        }
//    }
//    *reinterpret_cast<uint64_t*>(&mBuffer[sizeof(uint64_t) * 5ULL]) = lEventBodySize;
//}

//HCSSegHead::HCSSegHead() : MetaInfoBase()
//{

//}

//HCSSegHead::~HCSSegHead()
//{

//}

//uint64_t HCSSegHead::size() const
//{
//    return sizeof(HCSSegHeadData);
//}

//const void *HCSSegHead::data() const
//{
//    return &mData;
//}

//bool HCSSegHead::deserialize(char *data, uint64_t size)
//{
//    if (size != this->size())
//    {
//        return false;
//    }
//    memcpy(&mData, data, size);
//    return true;
//}

//uint64_t HCSSegHead::magicId() const
//{
//    return MAGIC_ID_UMA_HCS_SEG_HEAD_META;
//}

//void HCSSegHead::setData(const HCSSegHead::HCSSegHeadData &head)
//{
//    mData = head;
//}

//const HCSSegHead::HCSSegHeadData &HCSSegHead::innerData() const
//{
//    return mData;
//}

HCSDataFrame::HCSDataFrame() : //DataFrameBase(),
    mEvSplitterPtr(nullptr)
{
    memset(&mData, 0x00, sizeof(HCSData));
    mData.lpRawData = nullptr;
    //mData.lpMassChannelIndex = nullptr;
}

//HCSDataFrame::~HCSDataFrame()
//{

//}

//uint64_t HCSDataFrame::size() const
//{
//    return sizeof(HCSData)
//            + sizeof(uint64_t) * mData.lMassChannelCount
//            + sizeof(uint32_t) * mData.lDataLen;
//}

//const void *HCSDataFrame::data() const
//{
//    return mBuffer.data();
//}

//bool HCSDataFrame::deserialize(char *data, uint64_t size)
//{
//    uint64_t* lpBuffer = reinterpret_cast<uint64_t*>(data);
//    mData.lDataLen = lpBuffer[3];
//    mData.lMassChannelCount = lpBuffer[4];
//    if(this->size() != size)
//    {
//        return false;
//    }
//    memcpy(&mData, data, sizeof(HCSData));
//    const uint64_t lChannelCountSize = sizeof(uint64_t) * mData.lMassChannelCount;
//    const uint64_t lRawDataSize = sizeof(uint32_t) * mData.lDataLen;
//    const uint64_t lChannelOffset = sizeof(HCSData);
//    const uint64_t lRawDataOffset = lChannelOffset + lChannelCountSize;
//    mRawDataContainer.resize(mData.lDataLen);
//    mChannelIndexContainer.resize(mData.lMassChannelCount);
//    memcpy(mChannelIndexContainer.data(), data + lChannelOffset, lChannelCountSize);
//    memcpy(mRawDataContainer.data(), data + lRawDataOffset, lRawDataSize);
//    mData.lpRawData = mRawDataContainer.data();
//    mData.lpMassChannelIndex = mChannelIndexContainer.data();
//    mBuffer.resize(size);
//    memcpy(mBuffer.data(), data, size);
//    return true;
//}

//uint64_t HCSDataFrame::magicId() const
//{
//    return MAGIC_ID_UMA_HCS_DATA_FRAME;
//}

//bool HCSDataFrame::getChannelData(uint64_t massChannelId, std::vector<uint32_t> *pData)
//{
//    if (massChannelId > mData.lMassChannelCount)
//    {
//        return false;
//    }

//    try {
//        uint64_t lStartId = mData.lpMassChannelIndex[massChannelId];
//        uint64_t lEndId = massChannelId + 1 < mData.lMassChannelCount ?
//                    mData.lpMassChannelIndex[massChannelId + 1] : mData.lDataLen - 1;
//        const uint64_t lSize = lEndId - lStartId + 1;
//        pData->resize(lSize);
//        std::copy(mData.lpRawData + lStartId, mData.lpRawData + lEndId, pData->data());
//    } catch (std::exception& e) {
//        std::cerr << e.what() << std::endl;
//        return false;
//    }
//    return true;
//}

void HCSDataFrame::setData(uint64_t channel, double timeMs, double dTIC, /*uint64_t triggerIndex,*/
                           const std::vector<uint32_t> &data/*, const std::vector<uint64_t> &index*/)
{
    mData.dTimeMs= timeMs;
    mData.dTIC= dTIC;
    mData.lUMAChannelId = channel;///< uma channel index (DAQ package)
    //mData.lMassTriggerId = triggerIndex;
    //mData.lMassChannelCount = index.size();
    mData.lDataLen = data.size();
    mRawDataContainer = data;
    //mChannelIndexContainer = index;
    mData.lpRawData = mRawDataContainer.data();
    //mData.lpMassChannelIndex = mChannelIndexContainer.data();

    // update the channel and event id;
    uint64_t lSegNo = 0;
    uint64_t lEvNo = 0;
    uint64_t lChNo = 0;
    uint64_t lChTime = 0;
    if (mEvSplitterPtr)
    {
        mEvSplitterPtr->getUMASegmentNo(channel, &lSegNo);
        mEvSplitterPtr->getUMAEventNo(channel, &lEvNo);
        mEvSplitterPtr->getUMAChannelNo(channel, &lChNo);
        mEvSplitterPtr->getUMATime(channel, &lChTime);
    }
    mData.lUMASegmentNo = lSegNo;
    mData.lUMAEventNo = lEvNo;
    mData.lUMAChannelNo = lChNo;

//    HCSSegHead::HCSSegHeadData segHeadData;
//    auto head = std::make_shared<HCSSegHead>();
//    setMetaInfo(head);
//    segHeadData.dTIC = dTIC;
//    segHeadData.dTime = lChTime / 1000.0 / 60.0; ///< ms -> min
//    segHeadData.lUMAEventNo = lEvNo;
//    segHeadData.lUMAChannelNo = lChNo;
//    segHeadData.lUMAChannelId = channel;
//    // prepare the serialization buffer
//    prepareBuffer(&mBuffer);
//    segHeadData.lDataBodySize = mBuffer.size();
//    // write metainfo
//    head->setData(segHeadData);
}

void HCSDataFrame::copy(HCSDataFrame *other)
{
//    HCSSegHead* pHead = reinterpret_cast<HCSSegHead*>(other->metaInfo().get());
//    double dTIC = pHead == nullptr ? 0.0 : pHead->innerData().dTIC;
    setEvSplitter(other->mEvSplitterPtr);
    setData(other->mData.lUMAChannelId, other->mData.dTimeMs, other->mData.dTIC, /*other->mData.lMassTriggerId,*/
            other->mRawDataContainer/*, other->mChannelIndexContainer*/);
}

void HCSDataFrame::setEvSplitter(const std::shared_ptr<UMAEventSegmentSplitter> &splitter)
{
    mEvSplitterPtr = splitter;
}

const HCSDataFrame::HCSData &HCSDataFrame::innerData() const
{
    return mData;
}

//void HCSDataFrame::prepareBuffer(std::vector<char>* buffer)
//{
//    buffer->resize(size());
//    char* pBuffer = buffer->data();
//    memcpy(pBuffer, &mData, sizeof(HCSData));
//    const uint64_t lChannelCountSize = sizeof(uint64_t) * mData.lMassChannelCount;
//    const uint64_t lRawDataSize = sizeof(uint32_t) * mData.lDataLen;
//    const uint64_t lChannelOffset = sizeof(HCSData);
//    const uint64_t lRawDataOffset = lChannelOffset + lChannelCountSize;
//    memcpy(pBuffer + lChannelOffset, mData.lpMassChannelIndex, lChannelCountSize);
//    memcpy(pBuffer + lRawDataOffset, mData.lpRawData, lRawDataSize);
//}
