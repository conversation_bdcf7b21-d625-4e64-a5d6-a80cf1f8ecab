<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiCalibrationMassItem</class>
 <widget class="QWidget" name="uiCalibrationMassItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>981</width>
    <height>141</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="sClickWidget" name="UI_W_TITLE_CAL" native="true">
       <property name="autoFillBackground">
        <bool>true</bool>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="UI_L_Exponent_CAL">
          <property name="text">
           <string>  Exponent:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="UI_CB_Exponent_CAL"/>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QTableWidget" name="UI_TW_TABLE_CAL">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>116</height>
        </size>
       </property>
       <row>
        <property name="text">
         <string>Target Mass</string>
        </property>
       </row>
       <row>
        <property name="text">
         <string>Current Mass</string>
        </property>
       </row>
       <row>
        <property name="text">
         <string>Correction</string>
        </property>
       </row>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="UI_W_RIGHT_CAL" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="UI_PB_Calculate_CAL">
        <property name="text">
         <string>Calculate</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="UI_PB_APPLY_CAL">
        <property name="text">
         <string>Apply</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="UI_PB_CANCEL_CAL">
        <property name="text">
         <string>Cancel</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>sClickWidget</class>
   <extends>QWidget</extends>
   <header>LibWidget/sclickwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
