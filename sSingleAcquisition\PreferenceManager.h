﻿#ifndef PREFERENCEMANAGER_H
#define PREFERENCEMANAGER_H

#include <QObject>

#include "PreferenceDeveloper.h"
#include "UMAStructure/UMA_Public.h"
#include "PreferenceEditor.h"

#include "MSParameterParser.h"
#include "parmeter_structs.h" //LabSolutions

class PreferenceEditor;

class PreferenceManager : public QObject {
    Q_OBJECT

public:
    static PreferenceManager *getInstance();

    PreferenceDeveloper* getPrfsDeveloper() const { return m_prfsDeveloper; }
    PreferenceEditor *getPrfsEditor() const {return m_prfsEditor;}
    uma_project::ParamNormal* getPrfsNormalPointer();
    uma_project::ParamNormal getParamNormal() const;
    void setParamNormal(const uma_project::ParamNormal &paramNormal);

    int openFile(const QString& fileName);
    bool writeFile(const QString& fileName);
    uma_project::paramMZ_Voltages& mzVoltages();
    LabSolutionsRW::Event* getLabEventParamPtr() { return m_eventParamPtr; }

    uma_project::ParamAdvanceBase getMzPreference(double mz);
    void saveMzPreference(double mz, const uma_project::ParamAdvanceBase& param);

    void setPrfsDeveloper(PreferenceDeveloper *prfsDeveloper);

public slots:
    void setLabEventParamPtr(LabSolutionsRW::Event* eventParamPtr);
    void onQqQDetectorSwitch(bool uma);

signals:
    void modified(bool modify = true);
    void sig_labEventParamChanged(LabSolutionsRW::Event* eventParamPtr);

private slots:
    void onNormalParamEdited(const uma_project::ParamNormal& param);
    void onMzVoltagesTableChanged();

private:
    explicit PreferenceManager(QObject* parent = nullptr);
    void signalSlotManager();

private:
    PreferenceEditor* m_prfsEditor;
    PreferenceDeveloper* m_prfsDeveloper;
    uma_project::ParamNormal m_paramNormal;

    MSParameterParser m_MSParamParser;
    LabSolutionsRW::Event* m_eventParamPtr;
    QVector<uma_project::paramMZ_Voltages> m_mzVoltages;
};

#endif // PREFERENCEMANAGER_H
