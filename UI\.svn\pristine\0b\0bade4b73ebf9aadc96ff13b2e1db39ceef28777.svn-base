/********************************************************************************
** Form generated from reading UI file 'uiQ13SIMParamEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIQ13SIMPARAMEDITOR_H
#define UI_UIQ13SIMPARAMEDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiQ13SIMParamEditor
{
public:
    QWidget *uiQ13SIMParamEditorWidget;
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget_chanel;

    void setupUi(QWidget *uiQ13SIMParamEditor)
    {
        if (uiQ13SIMParamEditor->objectName().isEmpty())
            uiQ13SIMParamEditor->setObjectName(QString::fromUtf8("uiQ13SIMParamEditor"));
        uiQ13SIMParamEditor->resize(457, 470);
        uiQ13SIMParamEditorWidget = new QWidget(uiQ13SIMParamEditor);
        uiQ13SIMParamEditorWidget->setObjectName(QString::fromUtf8("uiQ13SIMParamEditorWidget"));
        uiQ13SIMParamEditorWidget->setGeometry(QRect(110, 260, 278, 214));
        verticalLayout = new QVBoxLayout(uiQ13SIMParamEditorWidget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        tableWidget_chanel = new QTableWidget(uiQ13SIMParamEditorWidget);
        if (tableWidget_chanel->columnCount() < 2)
            tableWidget_chanel->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        tableWidget_chanel->setObjectName(QString::fromUtf8("tableWidget_chanel"));
        tableWidget_chanel->setAlternatingRowColors(true);
        tableWidget_chanel->setSelectionMode(QAbstractItemView::SingleSelection);
        tableWidget_chanel->setSelectionBehavior(QAbstractItemView::SelectItems);
        tableWidget_chanel->horizontalHeader()->setMinimumSectionSize(40);
        tableWidget_chanel->horizontalHeader()->setStretchLastSection(true);
        tableWidget_chanel->verticalHeader()->setDefaultSectionSize(28);

        verticalLayout->addWidget(tableWidget_chanel);


        retranslateUi(uiQ13SIMParamEditor);

        QMetaObject::connectSlotsByName(uiQ13SIMParamEditor);
    } // setupUi

    void retranslateUi(QWidget *uiQ13SIMParamEditor)
    {
        uiQ13SIMParamEditor->setWindowTitle(QApplication::translate("uiQ13SIMParamEditor", "Form", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_chanel->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("uiQ13SIMParamEditor", "m/z", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_chanel->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("uiQ13SIMParamEditor", "Dwell Time (msec)", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiQ13SIMParamEditor: public Ui_uiQ13SIMParamEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIQ13SIMPARAMEDITOR_H
