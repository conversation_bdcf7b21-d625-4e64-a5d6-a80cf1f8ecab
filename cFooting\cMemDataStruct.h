#pragma once

#include <QVector>
#include<vector>
typedef unsigned int UINT;
#define FPGABUFFER 0x4000000///< TODO 64M ?

struct _FRAME_BUFF_CCS{
public:
    static QByteArray* resize(int nLength= 16 * 1024 * 1024){
        static _FRAME_BUFF_CCS insFRAME_BUFF_CCS;
        insFRAME_BUFF_CCS.mBuff.resize(nLength);
        return &(insFRAME_BUFF_CCS.mBuff);
    }
    static QByteArray* get(){
        static _FRAME_BUFF_CCS insFRAME_BUFF_CCS;
        return &(insFRAME_BUFF_CCS.mBuff);
    }
private:
    QByteArray mBuff;
    _FRAME_BUFF_CCS(){}
    virtual ~_FRAME_BUFF_CCS(){}
    _FRAME_BUFF_CCS(const _FRAME_BUFF_CCS&){}
    _FRAME_BUFF_CCS& operator=(const _FRAME_BUFF_CCS&){
        static _FRAME_BUFF_CCS insFRAME_BUFF_CCS;
        return insFRAME_BUFF_CCS;
    }
};
//ACQUISITION
struct _DAQ_BUFF_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _DAQ_BUFF_ACQUIS insDAQ_BUFF_ACQUIS;
        insDAQ_BUFF_ACQUIS.mBuff.resize(nLength);
        return &(insDAQ_BUFF_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _DAQ_BUFF_ACQUIS insDAQ_BUFF_ACQUIS;
        return &(insDAQ_BUFF_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;               //size-allPoint
    _DAQ_BUFF_ACQUIS(){}
    virtual ~_DAQ_BUFF_ACQUIS(){}
    _DAQ_BUFF_ACQUIS(const _DAQ_BUFF_ACQUIS&){}
    _DAQ_BUFF_ACQUIS& operator=(const _DAQ_BUFF_ACQUIS&){
        static _DAQ_BUFF_ACQUIS ins_DAQ_BUFF_ACQUIS;
        return ins_DAQ_BUFF_ACQUIS;
    }
};

struct _DAQ_X_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _DAQ_X_ACQUIS insDAQ_X_ACQUIS;
        insDAQ_X_ACQUIS.mBuff.resize(nLength);
        return &(insDAQ_X_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _DAQ_X_ACQUIS insDAQ_X_ACQUIS;
        return &(insDAQ_X_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;         //size-allPoint
    _DAQ_X_ACQUIS(){}
    virtual ~_DAQ_X_ACQUIS(){}
    _DAQ_X_ACQUIS(const _DAQ_X_ACQUIS&){}
    _DAQ_X_ACQUIS& operator=(const _DAQ_X_ACQUIS&){
        static _DAQ_X_ACQUIS insDAQ_X_ACQUIS;
        return insDAQ_X_ACQUIS;
    }
};

struct _DAQ_Y_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _DAQ_Y_ACQUIS insDAQ_Y_ACQUIS;
        insDAQ_Y_ACQUIS.mBuff.resize(nLength);
        return &(insDAQ_Y_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _DAQ_Y_ACQUIS insDAQ_Y_ACQUIS;
        return &(insDAQ_Y_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;         //size-allPoint
    _DAQ_Y_ACQUIS(){}
    virtual ~_DAQ_Y_ACQUIS(){}
    _DAQ_Y_ACQUIS(const _DAQ_Y_ACQUIS&){}
    _DAQ_Y_ACQUIS& operator=(const _DAQ_Y_ACQUIS&){
        static _DAQ_Y_ACQUIS insDAQ_Y_ACQUIS;
        return insDAQ_Y_ACQUIS;
    }
};
struct _CHART_X_ACQUIS{
public:
    static QList<std::vector<double>>* get(){
        static _CHART_X_ACQUIS insCHART_X_ACQUIS;
        return &(insCHART_X_ACQUIS.mBuff);
    }
private:
    QList<std::vector<double>> mBuff;//std::vector<double> mBuff;         //size-allPoint
    _CHART_X_ACQUIS(){}
    virtual ~_CHART_X_ACQUIS(){}
    _CHART_X_ACQUIS(const _CHART_X_ACQUIS&){}
    _CHART_X_ACQUIS& operator=(const _CHART_X_ACQUIS&){
        static _CHART_X_ACQUIS insCHART_X_ACQUIS;
        return insCHART_X_ACQUIS;
    }
};
//struct _CHART_X_ACQUIS{
//public:
//    static std::vector<double>* resize(int nLength){
//        static _CHART_X_ACQUIS insCHART_X_ACQUIS;
//        insCHART_X_ACQUIS.mBuff.resize(nLength);
//        return &(insCHART_X_ACQUIS.mBuff);
//    }
//    static std::vector<double>* get(){
//        static _CHART_X_ACQUIS insCHART_X_ACQUIS;
//        return &(insCHART_X_ACQUIS.mBuff);
//    }
//private:
//    std::vector<double> mBuff;         //size-allPoint
//    _CHART_X_ACQUIS(){}
//    virtual ~_CHART_X_ACQUIS(){}
//    _CHART_X_ACQUIS(const _CHART_X_ACQUIS&){}
//    _CHART_X_ACQUIS& operator=(const _CHART_X_ACQUIS&){
//        static _CHART_X_ACQUIS insCHART_X_ACQUIS;
//        return insCHART_X_ACQUIS;
//    }
//};
struct _CHART_Y_ACQUIS{
public:
    static QList<std::vector<double>>* get(){
        static _CHART_Y_ACQUIS insCHART_Y_ACQUIS;
        return &(insCHART_Y_ACQUIS.mBuff);
    }
private:
    QList<std::vector<double>> mBuff;         //size-allPoint
    _CHART_Y_ACQUIS(){}
    virtual ~_CHART_Y_ACQUIS(){}
    _CHART_Y_ACQUIS(const _CHART_Y_ACQUIS&){}
    _CHART_Y_ACQUIS& operator=(const _CHART_Y_ACQUIS&){
        static _CHART_Y_ACQUIS insCHART_Y_ACQUIS;
        return insCHART_Y_ACQUIS;
    }
};
//struct _CHART_Y_ACQUIS{
//public:
//    static std::vector<double>* resize(int nLength){
//        static _CHART_Y_ACQUIS insCHART_Y_ACQUIS;
//        insCHART_Y_ACQUIS.mBuff.resize(nLength);
//        return &(insCHART_Y_ACQUIS.mBuff);
//    }
//    static std::vector<double>* get(){
//        static _CHART_Y_ACQUIS insCHART_Y_ACQUIS;
//        return &(insCHART_Y_ACQUIS.mBuff);
//    }
//private:
//    std::vector<double> mBuff;         //size-allPoint
//    _CHART_Y_ACQUIS(){}
//    virtual ~_CHART_Y_ACQUIS(){}
//    _CHART_Y_ACQUIS(const _CHART_Y_ACQUIS&){}
//    _CHART_Y_ACQUIS& operator=(const _CHART_Y_ACQUIS&){
//        static _CHART_Y_ACQUIS insCHART_Y_ACQUIS;
//        return insCHART_Y_ACQUIS;
//    }
//};

struct _CHART_ABSC_ACQUIS{
public:
    static QList<std::vector<double>>* get(){
        static _CHART_ABSC_ACQUIS insCHART_ABSC_ACQUIS;
        return &(insCHART_ABSC_ACQUIS.mBuff);
    }
private:
    QList<std::vector<double>> mBuff;         //size-allPoint
    _CHART_ABSC_ACQUIS(){}
    virtual ~_CHART_ABSC_ACQUIS(){}
    _CHART_ABSC_ACQUIS(const _CHART_ABSC_ACQUIS&){}
    _CHART_ABSC_ACQUIS& operator=(const _CHART_ABSC_ACQUIS&){
        static _CHART_ABSC_ACQUIS insCHART_ABSC_ACQUIS;
        return insCHART_ABSC_ACQUIS;
    }
};

struct _CHART_ORD_ACQUIS{
public:
    static QList<std::vector<double>>* get(){
        static _CHART_ORD_ACQUIS insCHART_ORD_ACQUIS;
        return &(insCHART_ORD_ACQUIS.mBuff);
    }
private:
    QList<std::vector<double>> mBuff;         //size-allPoint
    _CHART_ORD_ACQUIS(){}
    virtual ~_CHART_ORD_ACQUIS(){}
    _CHART_ORD_ACQUIS(const _CHART_ORD_ACQUIS&){}
    _CHART_ORD_ACQUIS& operator=(const _CHART_ORD_ACQUIS&){
        static _CHART_ORD_ACQUIS insCHART_ORD_ACQUIS;
        return insCHART_ORD_ACQUIS;
    }
};

struct _STRUCT_PEAK_TUNING{
public:
    std::vector<double> Absc;
    std::vector<double> Ord;
    std::vector<double> HHPW;
    _STRUCT_PEAK_TUNING(int size=0){
        Absc.resize(size);
        Ord.resize(size);
        HHPW.resize(size);
    }
};

//struct _PEAK_MANUAL_TUNING:QMutex{
//public:
//    int eventCount=0;
//    QList<_STRUCT_PEAK_TUNING> data;
//    int size(){return data.length();}
//};

struct _STRUCT_GRAPH{
public:
    double baseLine= 0;
    std::vector<double> Absc;
    std::vector<double> Ord;
    QVector<QString> Marker;
    _STRUCT_GRAPH(int size=0){
        Absc.resize(size);
        Ord.resize(size);
        Marker.resize(size);
    }
};

struct _CHART_PEAK_ACQUIS{
public:
    static QList<_STRUCT_GRAPH>* get(){
        static _CHART_PEAK_ACQUIS insCHART_PEAK_ACQUIS;
        return &(insCHART_PEAK_ACQUIS.mBuff);
    }
private:
    QList<_STRUCT_GRAPH> mBuff;         //size-allPoint
    _CHART_PEAK_ACQUIS(){}
    virtual ~_CHART_PEAK_ACQUIS(){}
    _CHART_PEAK_ACQUIS(const _CHART_PEAK_ACQUIS&){}
    _CHART_PEAK_ACQUIS& operator=(const _CHART_PEAK_ACQUIS&){
        static _CHART_PEAK_ACQUIS insCHART_PEAK_ACQUIS;
        return insCHART_PEAK_ACQUIS;
    }
};
struct _BASELINE_Y_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _BASELINE_Y_ACQUIS insBASELINE_Y_ACQUIS;
        insBASELINE_Y_ACQUIS.mBuff.resize(nLength);
        return &(insBASELINE_Y_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _BASELINE_Y_ACQUIS insBASELINE_Y_ACQUIS;
        return &(insBASELINE_Y_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;              //size-allPoint
    _BASELINE_Y_ACQUIS(){}
    virtual ~_BASELINE_Y_ACQUIS(){}
    _BASELINE_Y_ACQUIS(const _BASELINE_Y_ACQUIS&){}
    _BASELINE_Y_ACQUIS& operator=(const _BASELINE_Y_ACQUIS&){
        static _BASELINE_Y_ACQUIS insBASELINE_Y_ACQUIS;
        return insBASELINE_Y_ACQUIS;
    }
};
struct _STRUCT_PEAK{
public:
    std::vector<double> Absc;
    std::vector<double> Ord;
    std::vector<double> Area;
    std::vector<int> Start;
    std::vector<int> End;
    _STRUCT_PEAK(int size=0){
        Absc.resize(size);
        Ord.resize(size);
        Area.resize(size);
        Start.resize(size);
        End.resize(size);
    }
    void resize(int size){
        Absc.resize(size);
        Ord.resize(size);
        Area.resize(size);
        Start.resize(size);
        End.resize(size);
    }
};
struct _PEAK_ABSC_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _PEAK_ABSC_ACQUIS insPEAK_ABSC_ACQUIS;
        insPEAK_ABSC_ACQUIS.mBuff.resize(nLength);
        return &(insPEAK_ABSC_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _PEAK_ABSC_ACQUIS insPEAK_ABSC_ACQUIS;
        return &(insPEAK_ABSC_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;         //size-nPeak
    _PEAK_ABSC_ACQUIS(){}
    virtual ~_PEAK_ABSC_ACQUIS(){}
    _PEAK_ABSC_ACQUIS(const _PEAK_ABSC_ACQUIS&){}
    _PEAK_ABSC_ACQUIS& operator=(const _PEAK_ABSC_ACQUIS&){
        static _PEAK_ABSC_ACQUIS insPEAK_ABSC_ACQUIS;
        return insPEAK_ABSC_ACQUIS;
    }
};

struct _PEAK_ORD_ACQUIS{
public:
    static std::vector<double>* resize(int nLength){
        static _PEAK_ORD_ACQUIS insPEAK_ORD_ACQUIS;
        insPEAK_ORD_ACQUIS.mBuff.resize(nLength);
        return &(insPEAK_ORD_ACQUIS.mBuff);
    }
    static std::vector<double>* get(){
        static _PEAK_ORD_ACQUIS insPEAK_ORD_ACQUIS;
        return &(insPEAK_ORD_ACQUIS.mBuff);
    }
private:
    std::vector<double> mBuff;         //size-nPeak
    _PEAK_ORD_ACQUIS(){}
    virtual ~_PEAK_ORD_ACQUIS(){}
    _PEAK_ORD_ACQUIS(const _PEAK_ORD_ACQUIS&){}
    _PEAK_ORD_ACQUIS& operator=(const _PEAK_ORD_ACQUIS&){
        static _PEAK_ORD_ACQUIS insPEAK_ORD_ACQUIS;
        return insPEAK_ORD_ACQUIS;
    }
};

struct _PEAK_START_ACQUIS{
public:
    static std::vector<int>* resize(int nLength){
        static _PEAK_START_ACQUIS insPEAK_START_ACQUIS;
        insPEAK_START_ACQUIS.mBuff.resize(nLength);
        return &(insPEAK_START_ACQUIS.mBuff);
    }
    static std::vector<int>* get(){
        static _PEAK_START_ACQUIS insPEAK_START_ACQUIS;
        return &(insPEAK_START_ACQUIS.mBuff);
    }
private:
    std::vector<int> mBuff;         //size-nPeak
    _PEAK_START_ACQUIS(){}
    virtual ~_PEAK_START_ACQUIS(){}
    _PEAK_START_ACQUIS(const _PEAK_START_ACQUIS&){}
    _PEAK_START_ACQUIS& operator=(const _PEAK_START_ACQUIS&){
        static _PEAK_START_ACQUIS insPEAK_START_ACQUIS;
        return insPEAK_START_ACQUIS;
    }
};

struct _PEAK_END_ACQUIS{
public:
    static std::vector<int>* resize(int nLength){
        static _PEAK_END_ACQUIS insPEAK_END_ACQUIS;
        insPEAK_END_ACQUIS.mBuff.resize(nLength);
        return &(insPEAK_END_ACQUIS.mBuff);
    }
    static std::vector<int>* get(){
        static _PEAK_END_ACQUIS insPEAK_END_ACQUIS;
        return &(insPEAK_END_ACQUIS.mBuff);
    }
private:
    std::vector<int> mBuff;         //size-nPeak
    _PEAK_END_ACQUIS(){}
    virtual ~_PEAK_END_ACQUIS(){}
    _PEAK_END_ACQUIS(const _PEAK_END_ACQUIS&){}
    _PEAK_END_ACQUIS& operator=(const _PEAK_END_ACQUIS&){
        static _PEAK_END_ACQUIS insPEAK_END_ACQUIS;
        return insPEAK_END_ACQUIS;
    }
};

struct _PEAK_MARK_ACQUIS{
public:
    static std::vector<int>* resize(int nLength){
        static _PEAK_MARK_ACQUIS insPEAK_MARK_ACQUIS;
        insPEAK_MARK_ACQUIS.mBuff.resize(nLength);
        return &(insPEAK_MARK_ACQUIS.mBuff);
    }
    static std::vector<int>* get(){
        static _PEAK_MARK_ACQUIS insPEAK_MARK_ACQUIS;
        return &(insPEAK_MARK_ACQUIS.mBuff);
    }
private:
    std::vector<int> mBuff;         //size-nPeak
    _PEAK_MARK_ACQUIS(){}
    virtual ~_PEAK_MARK_ACQUIS(){}
    _PEAK_MARK_ACQUIS(const _PEAK_MARK_ACQUIS&){}
    _PEAK_MARK_ACQUIS& operator=(const _PEAK_MARK_ACQUIS&){
        static _PEAK_MARK_ACQUIS insPEAK_MARK_ACQUIS;
        return insPEAK_MARK_ACQUIS;
    }
};

