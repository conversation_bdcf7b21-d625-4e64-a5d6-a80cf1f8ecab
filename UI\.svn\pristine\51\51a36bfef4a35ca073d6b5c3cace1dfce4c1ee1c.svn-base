#pragma once

#include <QtGlobal>
#include <QByteArray>
#include <QList>
#include "cTQ_StructCMD_HZH.h"
#define NUM_LDAC 36
class cTQ_StructCMD_AD81416
{
public:
    enum CMD_AD81416_ID_HEAD: quint32{
        CMD_AD81416_Start= 0xFFFFF111,
        CMD_AD81416_Halt= 0xFFFFF112,
        CMD_AD81416_Configure= 0xFFFFF113,
        CMD_AD81416_Event_Param_Set= 0xFFFFF114,
        CMD_AD81416_MZ_Param_Set= 0xFFFFF115
    };

    struct _CHANNEL_EVENT_PARAM{
        //quint32 FrameNo;//指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
        //quint32 Reserved;//保留字段
        //quint32 ChannelNo;//指示当前通道的序号(从0开始编号,最大编号为 <通道总数-1> )
        //quint32 EventNo;//指示当前通道参数属于哪一个event
        quint32 VariableMask= 0;//指示当前通道参数可变，每一位代表一个参数
        //quint32 Reserved;//保留字段
        float Q1_Mz_Start= 1;
        float Q1_Mz_End= 1;
        float Q2_Mz_Start= 1;
        float Q2_Mz_End= 1;
        float Q3_Mz_Start= 1;
        float Q3_Mz_End= 1;
        float PN_SwitchTimeMs= 0;
        float PauseTimeMs= 0;
        //float SettlingTimeMs=0;
        float WaitTimeMs= 0;
        float ScanTimeMs= 0;
        float DC_CE= 0;//DC_CE= 65535*(CE+10)/20
        float TDC_DAC= 0;
        //_EVENT_PARAM* parent = nullptr;


        QString toString(){
            QStringList list;
            list<<"Mz_Start:"<<QString::number(Q1_Mz_Start);
            list<<"Mz_End:"<<QString::number(Q1_Mz_End);
            list<<"Q2_Mz_Start:"<<QString::number(Q2_Mz_Start);
            list<<"Q2_Mz_End:"<<QString::number(Q2_Mz_End);
            list<<"Q3_Mz_Start:"<<QString::number(Q3_Mz_Start);
            list<<"Q3_Mz_End:"<<QString::number(Q3_Mz_End);
            list<<"PN_SwitchTimeMs:"<<QString::number(PN_SwitchTimeMs);
            list<<"PauseTimeMs:"<<QString::number(PauseTimeMs);
            list<<"WaitTimeMs:"<<QString::number(WaitTimeMs);
            list<<"ScanTimeMs:"<<QString::number(ScanTimeMs);
            list<<"DC_CE:"<<QString::number(DC_CE);
            list<<"TDC_DAC:"<<QString::number(TDC_DAC);
            return list.join(",");
        }

        static _CHANNEL_EVENT_PARAM fromString(QString& str, bool& ok){
            QStringList list= str.split(",");
            _CHANNEL_EVENT_PARAM tmp_CHANNEL_EVENT_PARAM;
            do{
                if(list.size()!= 12)
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[0], QLatin1String("Mz_Start"),tmp_CHANNEL_EVENT_PARAM.Q1_Mz_Start))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[1], QLatin1String("Mz_End:"),tmp_CHANNEL_EVENT_PARAM.Q1_Mz_End))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[2], QLatin1String("Q2_Mz_Start:"),tmp_CHANNEL_EVENT_PARAM.Q2_Mz_Start))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[3], QLatin1String("Q2_Mz_End:"),tmp_CHANNEL_EVENT_PARAM.Q2_Mz_End))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[4], QLatin1String("Q3_Mz_Start:"),tmp_CHANNEL_EVENT_PARAM.Q3_Mz_Start))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[5], QLatin1String("Q3_Mz_End:"),tmp_CHANNEL_EVENT_PARAM.Q3_Mz_End))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[6], QLatin1String("PN_SwitchTimeMs:"),tmp_CHANNEL_EVENT_PARAM.PN_SwitchTimeMs))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[7], QLatin1String("PauseTimeMs:"),tmp_CHANNEL_EVENT_PARAM.PauseTimeMs))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[8], QLatin1String("WaitTimeMs:"),tmp_CHANNEL_EVENT_PARAM.WaitTimeMs))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[9], QLatin1String("ScanTimeMs:"),tmp_CHANNEL_EVENT_PARAM.ScanTimeMs))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[10], QLatin1String("DC_CE:"),tmp_CHANNEL_EVENT_PARAM.DC_CE))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[11], QLatin1String("TDC_DAC:"),tmp_CHANNEL_EVENT_PARAM.DC_CE))
                    break;
                ok= true;
                return tmp_CHANNEL_EVENT_PARAM;
            }while(0);
            ok= false;
            return tmp_CHANNEL_EVENT_PARAM;
        }
        static float getUsableTimeMs(float timeMs){
            int numStepUs= (static_cast<int>(timeMs* 1000)+ SCAN_STEP_MIN_US- 1)
                    / SCAN_STEP_MIN_US;
            return static_cast<float>(numStepUs/ 1000.0* SCAN_STEP_MIN_US);
        }
        inline float getScanSpeedQ1(){
            if(ScanTimeMs== 0)
                return 0;
            return qAbs((Q1_Mz_End- Q1_Mz_Start)/ (ScanTimeMs/1000));
        }
        inline float getScanSpeedQ3(){
            if(ScanTimeMs== 0)
                return 0;
            return qAbs((Q3_Mz_End- Q3_Mz_Start)/ (ScanTimeMs/1000));
        }
        inline float getScanTimeMs(float eventTimeMs){
            return eventTimeMs- PN_SwitchTimeMs- PauseTimeMs- WaitTimeMs/*SettlingTimeMs*/;
        }
//        inline float getWaitTimeMs(float eventTimeMs, float& scanTimeMs){
//            float tmpTimeMs= eventTimeMs- PN_SwitchTimeMs- PauseTimeMs- WAIT_TIME_MIN_MS;
//            scanTimeMs= getUsableTimeMs(tmpTimeMs);
//            return getUsableTimeMs(eventTimeMs-
//                                        scanTimeMs-
//                                        PN_SwitchTimeMs-
//                                        PauseTimeMs);
//        }
        inline float getWaitTimeMs(float eventTimeMs){
            return eventTimeMs- PN_SwitchTimeMs- PauseTimeMs- ScanTimeMs;
        }
//        inline float setScanTimeMsQ1(float Speed){
//            if(Speed== 0)
//                return 0;
//            ScanTimeMs= getUsableTimeMs(qAbs((Q1_Mz_End- Q1_Mz_Start)/Speed)*1000);
//            return qAbs(Q1_Mz_End- Q1_Mz_Start)/(ScanTimeMs/1000);
//        }
//        inline float setScanTimeMsQ3(float Speed){
//            if(Speed== 0)
//                return 0;
//            ScanTimeMs= getUsableTimeMs(qAbs((Q3_Mz_End- Q3_Mz_Start)/Speed)*1000);
//            return qAbs(Q3_Mz_End- Q3_Mz_Start)/(ScanTimeMs/1000);
//        }
//        inline float eventTimeScan_ms(){
//            return PN_SwitchTimeMs+ SettlingTimeMs+ ScanTimeMs;
//        }
//        inline float eventTimeSIM_ms(){
//            return PN_SwitchTimeMs+ PauseTimeMs+ ScanTimeMs;
//        }
        inline float eventTime_ms() const{
            return PN_SwitchTimeMs+ PauseTimeMs+ WaitTimeMs/*SettlingTimeMs*/+ ScanTimeMs;
        }
        inline double eventTime_us() const{
            return eventTime_ms()* 1000;
        }
    };

    struct _EVENT_PARAM{
        //int iNum;//事件序号
        //quint32 FrameNo=0;          //指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
        //quint32 EventNo=0;          //指示当前事件的序号(从0开始编号,最大编号为 <事件总数-1> )
        cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_NULL;//指示当前事件的类型
        float TimeStartMin=0;        //指示当前事件的起始时间(时间单位为分钟)
        float TimeEndMin=0;          //指示当前事件的结束时间(时间单位为分钟)
        //quint32 ChannelQuantity=0;  //指示当前事件包含通道数量
        QList<_CHANNEL_EVENT_PARAM> listChannel;
    public:
        QString toString(){
            QStringList list;
            list<<"Type:"<<QString::number(Type);
            list<<"TimeStartMin:"<<QString::number(TimeStartMin);
            list<<"TimeEndMin:"<<QString::number(TimeEndMin);
            for (auto& channel : listChannel){
                list<< channel.toString();
            }
            return list.join(";");
        }
        static _EVENT_PARAM fromString(QString& str, bool& ok){
            QStringList list= str.split(";");
            _EVENT_PARAM tmp_EVENT_PARAM;
            do{
                quint32 type= 0;
                if(!cTQ_StructCMD_HZH::getProperty(list[0], QLatin1String("Type"),type))
                    break;
                tmp_EVENT_PARAM.Type= static_cast<cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE>(type);
                if(!cTQ_StructCMD_HZH::getProperty(list[1], QLatin1String("TimeStartMin"),tmp_EVENT_PARAM.TimeStartMin))
                    break;
                if(!cTQ_StructCMD_HZH::getProperty(list[2], QLatin1String("TimeEndMin"),tmp_EVENT_PARAM.TimeEndMin))
                    break;
                for(int i= 4; i< list.size(); ++i){
                    _CHANNEL_EVENT_PARAM tmp_CHANNEL_EVENT_PARAM;
                    tmp_CHANNEL_EVENT_PARAM= tmp_CHANNEL_EVENT_PARAM.fromString(list[i], ok);
                    if(!ok)
                        break;
                    tmp_EVENT_PARAM.listChannel<< tmp_CHANNEL_EVENT_PARAM;
                }
                ok= true;
                return tmp_EVENT_PARAM;
            }while(0);
            ok= false;
            return tmp_EVENT_PARAM;
        }
        int addChannel(_CHANNEL_EVENT_PARAM& pCHANNEL_EVENT_PARAM){
            listChannel.append(pCHANNEL_EVENT_PARAM);
            return listChannel.size();
        }
        int addChannels(QList<_CHANNEL_EVENT_PARAM>& pCHANNEL_EVENT_PARAM){
            listChannel.append(pCHANNEL_EVENT_PARAM);
            return listChannel.size();
        }
        void clearChannel(){
            listChannel.clear();
        }
        float eventTime_ms() const{
            float t = 0;
            for (auto ch : listChannel)
                t += static_cast<float>(ch.eventTime_ms());
            return t;
        }
        _EVENT_PARAM& operator=(const _EVENT_PARAM& other){
            if(this== &other)
            return *this;
            Type= other.Type;
            TimeStartMin= other.TimeStartMin;
            TimeEndMin= other.TimeEndMin;
            listChannel.clear();
            for (const auto& channel : other.listChannel){
                listChannel.append(channel);
            }
            return *this;
        }
    };

    struct _EVENT_PARAM_SET{
        //quint32 CMD_ID= 0xFFFFF804;//指令ID 指示当前指令事件配置指令
        //quint32 NODF=0;//指示当前指令后面DataFrame的个数
        //quint32 EventQuantity=0;    //指示当前扫描周期中事件的数量
        quint32 MinimumPeriod=0;    //时钟最小周期数
        QList<_EVENT_PARAM> listEvent;
        QString toString(){
            QStringList list;
            list<<"MinimumPeriod:"<<QString::number(MinimumPeriod);
            for(auto& p_EVENT_PARAM: listEvent){
                list<< p_EVENT_PARAM.toString();
            }
            return list.join("@");
        }
        static _EVENT_PARAM_SET fromString(QString& str, bool& ok){
            QStringList list= str.split("@");
            _EVENT_PARAM_SET tmp_EVENT_PARAM_SET;
            do{
                if(!cTQ_StructCMD_HZH::getProperty(list[0], QLatin1String("MinimumPeriod"),tmp_EVENT_PARAM_SET.MinimumPeriod))
                    break;
                for(int i= 1; i< list.size(); ++i){
                    _EVENT_PARAM tmp_EVENT_PARAM;
                    tmp_EVENT_PARAM= tmp_EVENT_PARAM.fromString(list[i], ok);
                    if(!ok)
                        break;
                    tmp_EVENT_PARAM_SET.listEvent<< tmp_EVENT_PARAM;
                }
                ok= true;
                return tmp_EVENT_PARAM_SET;
            }while(0);
            ok= false;
            return tmp_EVENT_PARAM_SET;
        }
        int addEvent(_EVENT_PARAM& pEVENT_PARAM){
            listEvent.append(pEVENT_PARAM);
            return listEvent.size();
        }
        void clearEvent(){
            listEvent.clear();
        }
//        float eventTime(){

//        }
        _EVENT_PARAM_SET& operator=(const _EVENT_PARAM_SET& other){
            if(this== &other)
            return *this;
            MinimumPeriod= other.MinimumPeriod;
            listEvent.clear();
            for(auto& p_EVENT_PARAM: other.listEvent){
                listEvent.append(p_EVENT_PARAM);
            }
            return *this;
        }
    };

    struct _MZ_PARAM_FRAME_DATA{
        //quint32 FrameNo=0;          //指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
        //EVENT_PARAM_HEAD_TYPE Type= EVENT_PARAM_HEAD_TYPE_POS;//指示当前事件的类型
        float mz=0;        //第一个M/Z 的参数总表m/z[0]
        float U[NUM_LDAC];          //
        //quint32 ChannelQuantity=0;  //指示当前事件包含通道数量
        //QList<_CHANNEL_EVENT_PARAM> listChannel;
//        int addChannel(_MZ_PARAM& pMZ_PARAM){
//            pMZ_PARAM.append(pCHANNEL_EVENT_PARAM);
//            return listChannel.size();
//        }
//        void clearChannel(){
//            listChannel.clear();
//        }
        _MZ_PARAM_FRAME_DATA(float _mz):mz(_mz){}
        bool operator==(const _MZ_PARAM_FRAME_DATA &p) const{
            if (this == &p)
                return true;
            else{
                if (memcmp(this, &p, sizeof(_MZ_PARAM_FRAME_DATA)) == 0)
                    return true;
            }
                return false;
        }
        _MZ_PARAM_FRAME_DATA& operator=(const _MZ_PARAM_FRAME_DATA& other){
            if(this== &other)
            return *this;
            memcpy(this, &other, sizeof(_MZ_PARAM_FRAME_DATA));
            return *this;
        }
    };
    /**
     * @brief The _MZ_PARAM_SET struct
     *
     */
    struct _MZ_PARAM_SET{
        //quint32 FrameNo=0;          //指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
        cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_NULL;//指示当前事件的类型
        //float mz=0;        //第一个M/Z 的参数总表m/z[0]
        //float U[12];          //
        //quint32 ChannelQuantity=0;  //指示当前事件包含通道数量
        QList<_MZ_PARAM_FRAME_DATA> listMZ_PARAM;
        _MZ_PARAM_SET& operator=(const _MZ_PARAM_SET& other){
            if(this== &other)
            return *this;
            Type= other.Type;
            listMZ_PARAM= other.listMZ_PARAM;
            return *this;
        }
        //_MZ_PARAM_SET(EVENT_PARAM_HEAD_TYPE _Type):Type(_Type){}
        _MZ_PARAM_SET(){}
        int addMZ(_MZ_PARAM_FRAME_DATA& pMZ_PARAM){
            listMZ_PARAM.append(pMZ_PARAM);
            return listMZ_PARAM.size();
        }
//        int addMZ(float mz, float U[NUM_LDAC]){
//            _MZ_PARAM_FRAME_DATA pMZ_PARAM(mz);
//            memcpy(pMZ_PARAM.U, U, sizeof(pMZ_PARAM.U));
//            listMZ_PARAM.append(pMZ_PARAM);
//            return listMZ_PARAM.size();
//        }
        int addMZ(double mz, const double* U){
            _MZ_PARAM_FRAME_DATA pMZ_PARAM(static_cast<float>(mz));
            //memcpy(pMZ_PARAM.U, U, sizeof(pMZ_PARAM.U));
            for(int i=0; i< NUM_LDAC; ++i){
                pMZ_PARAM.U[i]= static_cast<float>(U[i]);
            }
            listMZ_PARAM.append(pMZ_PARAM);
            return listMZ_PARAM.size();
        }
        int sizeMZ(){
            return listMZ_PARAM.size();
        }
        bool getRangeDC(float startMass, float endMass,
                        quint32& startDC, quint32& endDC){
//for(listMZ_PARAM)
        }
        /**
         * @brief getMZParameters
         * 返回列表中mz对应的参数值，如果不存在则返回线性插值的结果。
         * 如果节点长度为0 ， 则返回空的paramAdvanceBase(全为0)；
         * 如果节点长度为1 ， 则返回仅有的节点值。
         * @param mz
         * @return mz对应的参数值
         */
        _MZ_PARAM_FRAME_DATA getMZParameters(float mz, bool *ok = nullptr) const{
            if (ok)
                *ok = true;
            for(auto& tmpList: listMZ_PARAM){
                if(qFuzzyCompare(tmpList.mz, mz))
                    return tmpList;
            }
            _MZ_PARAM_FRAME_DATA param(mz);
            if (listMZ_PARAM.length() == 0)
                return param;
            if (listMZ_PARAM.length() == 1){
                param= listMZ_PARAM.first();
                return param;
            }

            int index = 0;
            for (auto& tmpList: listMZ_PARAM) {
                if (tmpList.mz > mz)
                    break;
                index += 1;
            }
            //double startMz, endMz;
            int startMzIndex, endMzIndex;
            if (index == listMZ_PARAM.length()){ //取最后两个节点向后插值
                startMzIndex= index - 2;//startMz = listMZ_PARAM[index - 2].mz;
                endMzIndex= index - 1;//endMz = listMZ_PARAM[index - 1].mz;
            }else if (index == 0){ //取前两个节点向前插值
                startMzIndex= 0;//startMz = listMZ_PARAM[0].mz;
                endMzIndex= 1;//endMz = listMZ_PARAM[1].mz;
            }else{//在第 index-1 和 第index 个节点间插值
                startMzIndex= index - 1;//startMz = listMZ_PARAM[index - 1].mz;
                endMzIndex= index;//endMz = listMZ_PARAM[index].mz;
            }
            //线性插值
            bool bOk = linearInterpolation(param,
                                listMZ_PARAM[startMzIndex],
                                listMZ_PARAM[endMzIndex]);
            if (nullptr != ok)
                *ok = bOk;
            return param;
        }

        /**
         * @brief linearInterpolation
         * 将从mzStart到mzEnd的参数插值成count个节点，并通过params返回各节点的值
         * @param count
         * @param params
         * @param mzStart
         * @param paramStart
         * @param mzEnd
         * @param paramEnd
         * @return none
         */
//        bool linearInterpolation(int count, QMap<double, ParamAdvanceBase>& params,
//                                 const double mzStart, const double mzEnd) const
//        {
//            if (count < 1)
//                return false;
//            params.clear();
//            double step = (mzEnd - mzStart) / (count - 1);
//            bool ok = true;
//            for (int i = 0; i < count; ++i) {
//                double mz = mzStart + i * step;
//                params.insert(mz, getMZParameters(mz, &ok));
//                if (!ok)
//                    return false;
//            }
//            return true;
//        }
        /**
         * @brief linearInterpolation
         * 线性插值，求取targetMz对应的targetParam.
         * 如果mzStart == mzEnd, 则令targetParam = paramStart ,并返回false;
         * @param targetMz 期望获取mz对应的ParamAdvanceBase
         * @param targetParam 存储获取的targetMz对应的ParamAdvanceBase
         * @param mzStart
         * @param paramStart mzStart对应的参数
         * @param mzEnd
         * @param paramEnd mzEnd对应的参数
         * @return 0
         */
        bool linearInterpolation(_MZ_PARAM_FRAME_DATA& targetParam,// const double targetMz, ParamAdvanceBase& targetParam,
                                 const _MZ_PARAM_FRAME_DATA& paramStart,//const double mzStart, const ParamAdvanceBase& paramStart,
                                 const _MZ_PARAM_FRAME_DATA& paramEnd//const double mzEnd, const ParamAdvanceBase& paramEnd
                                 ) const
        {
            if (qFuzzyCompare(paramStart.mz, paramEnd.mz)){
                targetParam = paramStart;
                return false;
            }
            for (int i = 0; i < 12; i++) {
                targetParam.U[i] = (paramEnd.U[i] - paramStart.U[i]) /
                        (paramEnd.mz - paramStart.mz) *
                        (targetParam.mz- paramStart.mz)
                        + paramStart.U[i];
            }
            return true;
        }
//        void clearChannel(){
//            listChannel.clear();
//        }
    };
    cTQ_StructCMD_AD81416();
//    static int cCMD_AD81416_Start(QByteArray& pBuffer, bool extTrigger= false);
//    static int cCMD_AD81416_Start(QVector<quint32>& pBuffer, bool extTrigger= false);
//    static int cTQ_CMD_MS_Halt(QByteArray& pBuffer);
//    static int cTQ_CMD_MS_Halt(QVector<quint32>& pBuffer);
//    static int cTQ_CMD_MS_Configure(QVector<quint32>& pBuffer);
//    static int cTQ_CMD_MS_Event_Param_Set(QByteArray& pBuffer,
//                                          _EVENT_PARAM_SET& pEVENT_PARAM_SET);
//    static int cTQ_CMD_MS_MZ_Param_Set(QByteArray& pBuffer,
//                                       QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET);
////    static int linearInterpolation(_EVENT_PARAM& p_EVENT_PARAM,
////                                   const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET);
////    static int linearInterpolation(const cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type,
////                                   _CHANNEL_EVENT_PARAM& pChannel,
////                                   const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET);

//private:
//    static QByteArray createEvent(_EVENT_PARAM& pEVENT_PARAM,
//                                  quint32& currentFrame,
//                                  quint32& currentEvent);
//    static QByteArray createParamFrameMZ(_MZ_PARAM_SET& p_MZ_PARAM_SET,
//                                  quint32& currentFrame);
};

