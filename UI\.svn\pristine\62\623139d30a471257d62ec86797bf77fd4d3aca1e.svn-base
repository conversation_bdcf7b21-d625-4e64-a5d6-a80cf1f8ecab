﻿#pragma once

#include "uiBaseParamEditor.h"
#include "ui_uiIonScanParamEditor.h"

class uiIonScanParamEditor : public uiBaseParamEditor
{
public:
    uiIonScanParamEditor(QString type, QWidget* parent = nullptr);
    ~uiIonScanParamEditor();
    //void saveParameter() override;
    virtual void initClass(QString& filePath);
    float getStartMZ(bool *ok=nullptr){
        return ui.lineEdit_startMZ->text().toFloat(ok);
    }
    float getEndMZ(bool *ok=nullptr){
        return ui.lineEdit_endMZ->text().toFloat(ok);
    }
    float getIonMZ(bool *ok=nullptr){
        return ui.lineEdit_ionMZ->text().toFloat(ok);
    }
    void setScanSpeed(float speed){
        ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
    }

protected:
    Ui::uiIonScanParamEditor ui;
    const QString m_strEventType;
    virtual bool initUI(QString& filePath);
    virtual bool calcUseMapMZ(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE&,
                              cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM&){
        return false;
    }

private slots:
    void on_lineEdit_startMZ_textEdited(const QString &arg1);
    void on_lineEdit_endMZ_textEdited(const QString &arg1);
    void on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1);
    void on_lineEdit_pauseTime_textEdited(const QString &arg1);
    void on_lineEdit_eventTime_textEdited(const QString &arg1);

private:
    bool errorParam= false;
    void initPage(const QString& eventType);

};

