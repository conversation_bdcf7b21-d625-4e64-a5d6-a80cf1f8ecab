#pragma once
#include "sclist.h"
#include <list>
#include <vector>
namespace libDataFilter{
class cFrameAverage
{
private:
    enum BufferT{B_TEMP, B_SUM, B_LENGTH};
    SCList m_dataList; /**< TODO */
    std::vector<double> m_buffer[B_LENGTH]; /**< TODO */
    volatile bool bLock=false;
public:
    cFrameAverage(){}
    void ClearCounts(){
        if(!bLock){
            bLock=true;
            m_dataList.release();
            bLock=false;
        }
    }
    std::vector<double> frameAverage(std::vector<double>& pIn, uint uCount);
};
}
//#include "FrameAverage.cpp"

