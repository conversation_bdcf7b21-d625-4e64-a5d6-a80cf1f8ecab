/********************************************************************************
** Form generated from reading UI file 'sTransparentWindow.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_STRANSPARENTWINDOW_H
#define UI_STRANSPARENTWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sTransparentWindow
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer;
    QWidget *UI_W_CENTER;
    QVBoxLayout *UI_LAYOUT_CENTER;
    QSpacerItem *verticalSpacer_2;
    QWidget *UI_W_TOP;
    QHBoxLayout *UI_LAYOUT_TOP;
    QSpacerItem *horizontalSpacer_2;
    QWidget *UI_W_BUTTOM;
    QHBoxLayout *UI_LAYOUT_BUTTOM;
    QWidget *UI_W_LEFT;
    QVBoxLayout *UI_LAYOUT_LEFT;
    QWidget *UI_W_RIGHT;
    QVBoxLayout *UI_LAYOUT_RIGHT;

    void setupUi(QWidget *sTransparentWindow)
    {
        if (sTransparentWindow->objectName().isEmpty())
            sTransparentWindow->setObjectName(QString::fromUtf8("sTransparentWindow"));
        sTransparentWindow->resize(848, 640);
        gridLayout = new QGridLayout(sTransparentWindow);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer, 2, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout->addItem(verticalSpacer, 4, 2, 1, 1);

        UI_W_CENTER = new QWidget(sTransparentWindow);
        UI_W_CENTER->setObjectName(QString::fromUtf8("UI_W_CENTER"));
        QPalette palette;
        QBrush brush(QColor(0, 0, 0, 255));
        brush.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::WindowText, brush);
        QBrush brush1(QColor(240, 240, 240, 255));
        brush1.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Button, brush1);
        QBrush brush2(QColor(255, 255, 255, 255));
        brush2.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Light, brush2);
        QBrush brush3(QColor(247, 247, 247, 255));
        brush3.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Midlight, brush3);
        QBrush brush4(QColor(120, 120, 120, 255));
        brush4.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Dark, brush4);
        QBrush brush5(QColor(160, 160, 160, 255));
        brush5.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::Mid, brush5);
        palette.setBrush(QPalette::Active, QPalette::Text, brush);
        palette.setBrush(QPalette::Active, QPalette::BrightText, brush2);
        palette.setBrush(QPalette::Active, QPalette::ButtonText, brush);
        palette.setBrush(QPalette::Active, QPalette::Base, brush2);
        palette.setBrush(QPalette::Active, QPalette::Window, brush1);
        palette.setBrush(QPalette::Active, QPalette::Shadow, brush);
        palette.setBrush(QPalette::Active, QPalette::AlternateBase, brush3);
        QBrush brush6(QColor(255, 255, 220, 255));
        brush6.setStyle(Qt::SolidPattern);
        palette.setBrush(QPalette::Active, QPalette::ToolTipBase, brush6);
        palette.setBrush(QPalette::Active, QPalette::ToolTipText, brush);
        QBrush brush7(QColor(0, 0, 0, 128));
        brush7.setStyle(Qt::SolidPattern);
#if QT_VERSION >= QT_VERSION_CHECK(5, 12, 0)
        palette.setBrush(QPalette::Active, QPalette::PlaceholderText, brush7);
#endif
        palette.setBrush(QPalette::Inactive, QPalette::WindowText, brush);
        palette.setBrush(QPalette::Inactive, QPalette::Button, brush1);
        palette.setBrush(QPalette::Inactive, QPalette::Light, brush2);
        palette.setBrush(QPalette::Inactive, QPalette::Midlight, brush3);
        palette.setBrush(QPalette::Inactive, QPalette::Dark, brush4);
        palette.setBrush(QPalette::Inactive, QPalette::Mid, brush5);
        palette.setBrush(QPalette::Inactive, QPalette::Text, brush);
        palette.setBrush(QPalette::Inactive, QPalette::BrightText, brush2);
        palette.setBrush(QPalette::Inactive, QPalette::ButtonText, brush);
        palette.setBrush(QPalette::Inactive, QPalette::Base, brush2);
        palette.setBrush(QPalette::Inactive, QPalette::Window, brush1);
        palette.setBrush(QPalette::Inactive, QPalette::Shadow, brush);
        palette.setBrush(QPalette::Inactive, QPalette::AlternateBase, brush3);
        palette.setBrush(QPalette::Inactive, QPalette::ToolTipBase, brush6);
        palette.setBrush(QPalette::Inactive, QPalette::ToolTipText, brush);
#if QT_VERSION >= QT_VERSION_CHECK(5, 12, 0)
        palette.setBrush(QPalette::Inactive, QPalette::PlaceholderText, brush7);
#endif
        palette.setBrush(QPalette::Disabled, QPalette::WindowText, brush4);
        palette.setBrush(QPalette::Disabled, QPalette::Button, brush1);
        palette.setBrush(QPalette::Disabled, QPalette::Light, brush2);
        palette.setBrush(QPalette::Disabled, QPalette::Midlight, brush3);
        palette.setBrush(QPalette::Disabled, QPalette::Dark, brush4);
        palette.setBrush(QPalette::Disabled, QPalette::Mid, brush5);
        palette.setBrush(QPalette::Disabled, QPalette::Text, brush4);
        palette.setBrush(QPalette::Disabled, QPalette::BrightText, brush2);
        palette.setBrush(QPalette::Disabled, QPalette::ButtonText, brush4);
        palette.setBrush(QPalette::Disabled, QPalette::Base, brush1);
        palette.setBrush(QPalette::Disabled, QPalette::Window, brush1);
        palette.setBrush(QPalette::Disabled, QPalette::Shadow, brush);
        palette.setBrush(QPalette::Disabled, QPalette::AlternateBase, brush1);
        palette.setBrush(QPalette::Disabled, QPalette::ToolTipBase, brush6);
        palette.setBrush(QPalette::Disabled, QPalette::ToolTipText, brush);
#if QT_VERSION >= QT_VERSION_CHECK(5, 12, 0)
        palette.setBrush(QPalette::Disabled, QPalette::PlaceholderText, brush7);
#endif
        UI_W_CENTER->setPalette(palette);
        UI_W_CENTER->setAutoFillBackground(true);
        UI_LAYOUT_CENTER = new QVBoxLayout(UI_W_CENTER);
        UI_LAYOUT_CENTER->setObjectName(QString::fromUtf8("UI_LAYOUT_CENTER"));

        gridLayout->addWidget(UI_W_CENTER, 2, 2, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 0, 2, 1, 1);

        UI_W_TOP = new QWidget(sTransparentWindow);
        UI_W_TOP->setObjectName(QString::fromUtf8("UI_W_TOP"));
        UI_W_TOP->setMinimumSize(QSize(0, 0));
        UI_LAYOUT_TOP = new QHBoxLayout(UI_W_TOP);
        UI_LAYOUT_TOP->setObjectName(QString::fromUtf8("UI_LAYOUT_TOP"));

        gridLayout->addWidget(UI_W_TOP, 1, 0, 1, 5);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 2, 4, 1, 1);

        UI_W_BUTTOM = new QWidget(sTransparentWindow);
        UI_W_BUTTOM->setObjectName(QString::fromUtf8("UI_W_BUTTOM"));
        UI_W_BUTTOM->setMinimumSize(QSize(0, 0));
        UI_LAYOUT_BUTTOM = new QHBoxLayout(UI_W_BUTTOM);
        UI_LAYOUT_BUTTOM->setObjectName(QString::fromUtf8("UI_LAYOUT_BUTTOM"));

        gridLayout->addWidget(UI_W_BUTTOM, 3, 0, 1, 5);

        UI_W_LEFT = new QWidget(sTransparentWindow);
        UI_W_LEFT->setObjectName(QString::fromUtf8("UI_W_LEFT"));
        UI_W_LEFT->setMinimumSize(QSize(0, 0));
        UI_LAYOUT_LEFT = new QVBoxLayout(UI_W_LEFT);
        UI_LAYOUT_LEFT->setObjectName(QString::fromUtf8("UI_LAYOUT_LEFT"));

        gridLayout->addWidget(UI_W_LEFT, 2, 1, 1, 1);

        UI_W_RIGHT = new QWidget(sTransparentWindow);
        UI_W_RIGHT->setObjectName(QString::fromUtf8("UI_W_RIGHT"));
        UI_W_RIGHT->setMinimumSize(QSize(0, 0));
        UI_LAYOUT_RIGHT = new QVBoxLayout(UI_W_RIGHT);
        UI_LAYOUT_RIGHT->setObjectName(QString::fromUtf8("UI_LAYOUT_RIGHT"));

        gridLayout->addWidget(UI_W_RIGHT, 2, 3, 1, 1);


        retranslateUi(sTransparentWindow);

        QMetaObject::connectSlotsByName(sTransparentWindow);
    } // setupUi

    void retranslateUi(QWidget *sTransparentWindow)
    {
        sTransparentWindow->setWindowTitle(QApplication::translate("sTransparentWindow", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class sTransparentWindow: public Ui_sTransparentWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_STRANSPARENTWINDOW_H
