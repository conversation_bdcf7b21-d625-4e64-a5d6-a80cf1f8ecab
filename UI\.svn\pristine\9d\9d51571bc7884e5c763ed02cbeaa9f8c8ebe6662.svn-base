/* const/gsl_const_mksa.h
 * 
 * Copyright (C) 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005,
 * 2006 <PERSON>
 * 
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 */

#ifndef __GSL_CONST_MKSA__
#define __GSL_CONST_MKSA__

#define GSL_CONST_MKSA_SPEED_OF_LIGHT (2.99792458e8) /* m / s */
#define GSL_CONST_MKSA_GRAVITATIONAL_CONSTANT (6.673e-11) /* m^3 / kg s^2 */
#define GSL_CONST_MKSA_PLANCKS_CONSTANT_H (6.62606876e-34) /* kg m^2 / s */
#define GSL_CONST_MKSA_PLANCKS_CONSTANT_HBAR (1.05457159642e-34) /* kg m^2 / s */
#define GSL_CONST_MKSA_ASTRONOMICAL_UNIT (1.49597870691e11) /* m */
#define GSL_CONST_MKSA_LIGHT_YEAR (9.46053620707e15) /* m */
#define GSL_CONST_MKSA_PARSEC (3.08567758135e16) /* m */
#define GSL_CONST_MKSA_GRAV_ACCEL (9.80665e0) /* m / s^2 */
#define GSL_CONST_MKSA_ELECTRON_VOLT (1.602176462e-19) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_MASS_ELECTRON (9.10938188e-31) /* kg */
#define GSL_CONST_MKSA_MASS_MUON (1.88353109e-28) /* kg */
#define GSL_CONST_MKSA_MASS_PROTON (1.67262158e-27) /* kg */
#define GSL_CONST_MKSA_MASS_NEUTRON (1.67492716e-27) /* kg */
#define GSL_CONST_MKSA_RYDBERG (2.17987190389e-18) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_BOLTZMANN (1.3806503e-23) /* kg m^2 / K s^2 */
#define GSL_CONST_MKSA_BOHR_MAGNETON (9.27400899e-24) /* A m^2 */
#define GSL_CONST_MKSA_NUCLEAR_MAGNETON (5.05078317e-27) /* A m^2 */
#define GSL_CONST_MKSA_ELECTRON_MAGNETIC_MOMENT (9.28476362e-24) /* A m^2 */
#define GSL_CONST_MKSA_PROTON_MAGNETIC_MOMENT (1.410606633e-26) /* A m^2 */
#define GSL_CONST_MKSA_MOLAR_GAS (8.314472e0) /* kg m^2 / K mol s^2 */
#define GSL_CONST_MKSA_STANDARD_GAS_VOLUME (2.2710981e-2) /* m^3 / mol */
#define GSL_CONST_MKSA_MINUTE (6e1) /* s */
#define GSL_CONST_MKSA_HOUR (3.6e3) /* s */
#define GSL_CONST_MKSA_DAY (8.64e4) /* s */
#define GSL_CONST_MKSA_WEEK (6.048e5) /* s */
#define GSL_CONST_MKSA_INCH (2.54e-2) /* m */
#define GSL_CONST_MKSA_FOOT (3.048e-1) /* m */
#define GSL_CONST_MKSA_YARD (9.144e-1) /* m */
#define GSL_CONST_MKSA_MILE (1.609344e3) /* m */
#define GSL_CONST_MKSA_NAUTICAL_MILE (1.852e3) /* m */
#define GSL_CONST_MKSA_FATHOM (1.8288e0) /* m */
#define GSL_CONST_MKSA_MIL (2.54e-5) /* m */
#define GSL_CONST_MKSA_POINT (3.52777777778e-4) /* m */
#define GSL_CONST_MKSA_TEXPOINT (3.51459803515e-4) /* m */
#define GSL_CONST_MKSA_MICRON (1e-6) /* m */
#define GSL_CONST_MKSA_ANGSTROM (1e-10) /* m */
#define GSL_CONST_MKSA_HECTARE (1e4) /* m^2 */
#define GSL_CONST_MKSA_ACRE (4.04685642241e3) /* m^2 */
#define GSL_CONST_MKSA_BARN (1e-28) /* m^2 */
#define GSL_CONST_MKSA_LITER (1e-3) /* m^3 */
#define GSL_CONST_MKSA_US_GALLON (3.78541178402e-3) /* m^3 */
#define GSL_CONST_MKSA_QUART (9.46352946004e-4) /* m^3 */
#define GSL_CONST_MKSA_PINT (4.73176473002e-4) /* m^3 */
#define GSL_CONST_MKSA_CUP (2.36588236501e-4) /* m^3 */
#define GSL_CONST_MKSA_FLUID_OUNCE (2.95735295626e-5) /* m^3 */
#define GSL_CONST_MKSA_TABLESPOON (1.47867647813e-5) /* m^3 */
#define GSL_CONST_MKSA_TEASPOON (4.92892159375e-6) /* m^3 */
#define GSL_CONST_MKSA_CANADIAN_GALLON (4.54609e-3) /* m^3 */
#define GSL_CONST_MKSA_UK_GALLON (4.546092e-3) /* m^3 */
#define GSL_CONST_MKSA_MILES_PER_HOUR (4.4704e-1) /* m / s */
#define GSL_CONST_MKSA_KILOMETERS_PER_HOUR (2.77777777778e-1) /* m / s */
#define GSL_CONST_MKSA_KNOT (5.14444444444e-1) /* m / s */
#define GSL_CONST_MKSA_POUND_MASS (4.5359237e-1) /* kg */
#define GSL_CONST_MKSA_OUNCE_MASS (2.8349523125e-2) /* kg */
#define GSL_CONST_MKSA_TON (9.0718474e2) /* kg */
#define GSL_CONST_MKSA_METRIC_TON (1e3) /* kg */
#define GSL_CONST_MKSA_UK_TON (1.0160469088e3) /* kg */
#define GSL_CONST_MKSA_TROY_OUNCE (3.1103475e-2) /* kg */
#define GSL_CONST_MKSA_CARAT (2e-4) /* kg */
#define GSL_CONST_MKSA_UNIFIED_ATOMIC_MASS (1.66053873e-27) /* kg */
#define GSL_CONST_MKSA_GRAM_FORCE (9.80665e-3) /* kg m / s^2 */
#define GSL_CONST_MKSA_POUND_FORCE (4.44822161526e0) /* kg m / s^2 */
#define GSL_CONST_MKSA_KILOPOUND_FORCE (4.44822161526e3) /* kg m / s^2 */
#define GSL_CONST_MKSA_POUNDAL (1.38255e-1) /* kg m / s^2 */
#define GSL_CONST_MKSA_CALORIE (4.1868e0) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_BTU (1.05505585262e3) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_THERM (1.05506e8) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_HORSEPOWER (7.457e2) /* kg m^2 / s^3 */
#define GSL_CONST_MKSA_BAR (1e5) /* kg / m s^2 */
#define GSL_CONST_MKSA_STD_ATMOSPHERE (1.01325e5) /* kg / m s^2 */
#define GSL_CONST_MKSA_TORR (1.33322368421e2) /* kg / m s^2 */
#define GSL_CONST_MKSA_METER_OF_MERCURY (1.33322368421e5) /* kg / m s^2 */
#define GSL_CONST_MKSA_INCH_OF_MERCURY (3.38638815789e3) /* kg / m s^2 */
#define GSL_CONST_MKSA_INCH_OF_WATER (2.490889e2) /* kg / m s^2 */
#define GSL_CONST_MKSA_PSI (6.89475729317e3) /* kg / m s^2 */
#define GSL_CONST_MKSA_POISE (1e-1) /* kg m^-1 s^-1 */
#define GSL_CONST_MKSA_STOKES (1e-4) /* m^2 / s */
#define GSL_CONST_MKSA_FARADAY (9.6485341472e4) /* A s / mol */
#define GSL_CONST_MKSA_ELECTRON_CHARGE (1.602176462e-19) /* A s */
#define GSL_CONST_MKSA_GAUSS (1e-4) /* kg / A s^2 */
#define GSL_CONST_MKSA_STILB (1e4) /* cd / m^2 */
#define GSL_CONST_MKSA_LUMEN (1e0) /* cd sr */
#define GSL_CONST_MKSA_LUX (1e0) /* cd sr / m^2 */
#define GSL_CONST_MKSA_PHOT (1e4) /* cd sr / m^2 */
#define GSL_CONST_MKSA_FOOTCANDLE (1.076e1) /* cd sr / m^2 */
#define GSL_CONST_MKSA_LAMBERT (1e4) /* cd sr / m^2 */
#define GSL_CONST_MKSA_FOOTLAMBERT (1.07639104e1) /* cd sr / m^2 */
#define GSL_CONST_MKSA_CURIE (3.7e10) /* 1 / s */
#define GSL_CONST_MKSA_ROENTGEN (2.58e-4) /* A s / kg */
#define GSL_CONST_MKSA_RAD (1e-2) /* m^2 / s^2 */
#define GSL_CONST_MKSA_SOLAR_MASS (1.98892e30) /* kg */
#define GSL_CONST_MKSA_BOHR_RADIUS (5.291772083e-11) /* m */
#define GSL_CONST_MKSA_NEWTON (1e0) /* kg m / s^2 */
#define GSL_CONST_MKSA_DYNE (1e-5) /* kg m / s^2 */
#define GSL_CONST_MKSA_JOULE (1e0) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_ERG (1e-7) /* kg m^2 / s^2 */
#define GSL_CONST_MKSA_STEFAN_BOLTZMANN_CONSTANT (5.67039934436e-8) /* kg / K^4 s^3 */
#define GSL_CONST_MKSA_THOMSON_CROSS_SECTION (6.65245853542e-29) /* m^2 */
#define GSL_CONST_MKSA_VACUUM_PERMITTIVITY (8.854187817e-12) /* A^2 s^4 / kg m^3 */
#define GSL_CONST_MKSA_VACUUM_PERMEABILITY (1.25663706144e-6) /* kg m / A^2 s^2 */
#define GSL_CONST_MKSA_DEBYE (3.33564095198e-30) /* A s^2 / m^2 */

#endif /* __GSL_CONST_MKSA__ */
