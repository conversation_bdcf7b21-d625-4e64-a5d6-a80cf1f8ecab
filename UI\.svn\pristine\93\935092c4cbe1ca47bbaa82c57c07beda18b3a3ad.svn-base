﻿#include "uiIonScanParamEditor.h"

/**
 * @brief
 * 该类支持Product <PERSON>、Precursor <PERSON>、Neutral Los Scan 三种Event类型的参数显示与编辑
 * @param element
 * @param parent
 */
uiIonScanParamEditor::uiIonScanParamEditor(QString type, QWidget* parent)
    : uiBaseParamEditor(parent)
    , m_strEventType(type)
{


}

uiIonScanParamEditor::~uiIonScanParamEditor()
{

}

void uiIonScanParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiIonScanParamEditor::initUI(QString& filePath)
{
    ui.setupUi(this);
    uiBase.uiBaseParamEditorLayout->insertWidget(0, ui.uiIonScanParamEditorWidget);
    initPage(m_strEventType);
    return true;
}
/**
 * @brief
 *
 */
//void uiIonScanParamEditor::saveParameter()
//{
//    //compound name
//    QDomElement tmp = m_domElement.firstChildElement(tagCompoundName());
//    if (!tmp.firstChild().isText()) {
//        QDomText text = m_pDoc->createTextNode("");
//        tmp.appendChild(text);
//    }
//    tmp.firstChild().setNodeValue(ui->lineEdit_compName->text().trimmed());
//    //start / End time
//    tmp = m_domElement.firstChildElement(tagStartTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_startTime->text().toDouble() * 60000));
//    tmp = m_domElement.firstChildElement(tagEndTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_endTime->text().toDouble() * 60000));
//    //EventTime
//    tmp = m_domElement.firstChildElement(tagEventTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_eventTime->text().toDouble()));
//    //Q1 Q3 resolution
//    tmp = m_domElement.firstChildElement(tagQ1Resolution());
//    tmp.firstChild().setNodeValue(QString::number(ui->comboBox_Q1->currentIndex() + 1));
//    tmp = m_domElement.firstChildElement(tagQ3Resolution());
//    tmp.firstChild().setNodeValue(QString::number(ui->comboBox_Q3->currentIndex() + 1));
//    //Channel
//    tmp = m_domElement.firstChildElement(tagChannel());
//    if (!tmp.isNull()) {
//        QDomElement temp1 = tmp.firstChildElement(tagStartMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_startMZ->text());
//        temp1 = tmp.firstChildElement(tagEndMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_endMZ->text());
//        temp1 = tmp.firstChildElement(tagAcqModeMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_ionMZ->text());
//        temp1 = tmp.firstChildElement(tagCE());
//        temp1.firstChild().setNodeValue(ui->lineEdit_CE->text());
//    }
//}
/**
 * @brief
 *
 */
void uiIonScanParamEditor::initPage(const QString& eventType)
{
    //QString eventType = m_domElement.firstChildElement(tagAcqMode()).text();
    if (eventType == "Product Ion Scan")
        ui.label_IonMZ->setText("Precursor Ion m/z:");
    else if (eventType == "Precursor Ion Scan")
        ui.label_IonMZ->setText("Product Ion m/z:");
    else if (eventType == "Neutral Los Scan")
        ui.label_IonMZ->setText("Losses of:");
}

void uiIonScanParamEditor::on_lineEdit_startMZ_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if (m_strEventType == "Product Ion Scan"){
        tmpPARAM.Q3_Mz_Start= arg1.toFloat(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
        }
    }else if (m_strEventType == "Precursor Ion Scan"){
        tmpPARAM.Q1_Mz_Start= arg1.toFloat(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
        }
    }else if (m_strEventType == "Neutral Los Scan"){
        tmpPARAM.Q1_Mz_Start= arg1.toFloat(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_Start= tmpPARAM.Q1_Mz_Start+ getIonMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= tmpPARAM.Q1_Mz_End+ getIonMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
        }
    }

    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(getEventTime(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0))
        return;
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

void uiIonScanParamEditor::on_lineEdit_endMZ_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if (m_strEventType == "Product Ion Scan"){
        tmpPARAM.Q3_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= arg1.toFloat(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
        }
    }else if (m_strEventType == "Precursor Ion Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= arg1.toFloat(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
        }
    }else if (m_strEventType == "Neutral Los Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= arg1.toFloat(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_Start= tmpPARAM.Q1_Mz_Start+ getIonMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= tmpPARAM.Q1_Mz_End+ getIonMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
        }
    }

    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(getEventTime(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0))
        return;
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

void uiIonScanParamEditor::on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(arg1.toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if (m_strEventType == "Product Ion Scan"){
        tmpPARAM.Q3_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
        }
    }else if (m_strEventType == "Precursor Ion Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
        }
    }else if (m_strEventType == "Neutral Los Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_Start= tmpPARAM.Q1_Mz_Start+ getIonMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= tmpPARAM.Q1_Mz_End+ getIonMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
        }
    }

    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(getEventTime(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0))
        return;
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

void uiIonScanParamEditor::on_lineEdit_pauseTime_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(arg1.toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if (m_strEventType == "Product Ion Scan"){
        tmpPARAM.Q3_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
        }
    }else if (m_strEventType == "Precursor Ion Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
        }
    }else if (m_strEventType == "Neutral Los Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_Start= tmpPARAM.Q1_Mz_Start+ getIonMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= tmpPARAM.Q1_Mz_End+ getIonMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
        }
    }

    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(getEventTime(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0))
        return;
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

void uiIonScanParamEditor::on_lineEdit_eventTime_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if (m_strEventType == "Product Ion Scan"){
        tmpPARAM.Q3_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_NEG;
        }
    }else if (m_strEventType == "Precursor Ion Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_NEG;
        }
    }else if (m_strEventType == "Neutral Los Scan"){
        tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q1_Mz_End= getEndMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_Start= tmpPARAM.Q1_Mz_Start+ getIonMZ(&ok);
        if(!ok) return;
        tmpPARAM.Q3_Mz_End= tmpPARAM.Q1_Mz_End+ getIonMZ(&ok);
        if(!ok) return;
        if(uiBase.UI_PB_Polarity->text()== "+"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_NEG;
        }
    }

    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(arg1.toFloat(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0))
        return;
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

