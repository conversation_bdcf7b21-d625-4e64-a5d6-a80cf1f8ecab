/********************************************************************************
** Form generated from reading UI file 'ParameterEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PARAMETEREDITOR_H
#define UI_PARAMETEREDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ParameterEditor
{
public:
    QHBoxLayout *horizontalLayout;
    QLabel *labelName;
    QVBoxLayout *verticalLayout;
    QLineEdit *lineEdit;
    QFrame *line;
    QLabel *labelUnit;

    void setupUi(QWidget *ParameterEditor)
    {
        if (ParameterEditor->objectName().isEmpty())
            ParameterEditor->setObjectName(QString::fromUtf8("ParameterEditor"));
        ParameterEditor->resize(169, 19);
        horizontalLayout = new QHBoxLayout(ParameterEditor);
        horizontalLayout->setSpacing(4);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        labelName = new QLabel(ParameterEditor);
        labelName->setObjectName(QString::fromUtf8("labelName"));
        QFont font;
        font.setFamily(QString::fromUtf8("Arial"));
        font.setBold(true);
        font.setWeight(75);
        labelName->setFont(font);

        horizontalLayout->addWidget(labelName);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        lineEdit = new QLineEdit(ParameterEditor);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setMinimumSize(QSize(0, 16));
        lineEdit->setMaximumSize(QSize(16777215, 24));
        QFont font1;
        font1.setFamily(QString::fromUtf8("Arial Narrow"));
        font1.setPointSize(10);
        lineEdit->setFont(font1);
        lineEdit->setStyleSheet(QString::fromUtf8("background:transparent;border-width:0;border-style:outset;color: rgb(55, 75, 255)"));
        lineEdit->setAlignment(Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing);

        verticalLayout->addWidget(lineEdit);

        line = new QFrame(ParameterEditor);
        line->setObjectName(QString::fromUtf8("line"));
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line);


        horizontalLayout->addLayout(verticalLayout);

        labelUnit = new QLabel(ParameterEditor);
        labelUnit->setObjectName(QString::fromUtf8("labelUnit"));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(labelUnit->sizePolicy().hasHeightForWidth());
        labelUnit->setSizePolicy(sizePolicy1);
        QFont font2;
        font2.setFamily(QString::fromUtf8("Arial"));
        font2.setPointSize(8);
        font2.setItalic(true);
        labelUnit->setFont(font2);
        labelUnit->setScaledContents(false);
        labelUnit->setWordWrap(false);

        horizontalLayout->addWidget(labelUnit);


        retranslateUi(ParameterEditor);

        QMetaObject::connectSlotsByName(ParameterEditor);
    } // setupUi

    void retranslateUi(QWidget *ParameterEditor)
    {
        ParameterEditor->setWindowTitle(QApplication::translate("ParameterEditor", "Form", nullptr));
        labelName->setText(QApplication::translate("ParameterEditor", "Name", nullptr));
        lineEdit->setText(QApplication::translate("ParameterEditor", "value", nullptr));
        lineEdit->setPlaceholderText(QApplication::translate("ParameterEditor", "0", nullptr));
        labelUnit->setText(QApplication::translate("ParameterEditor", "Unit", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ParameterEditor: public Ui_ParameterEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PARAMETEREDITOR_H
