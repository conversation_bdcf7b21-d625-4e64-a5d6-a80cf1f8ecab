﻿#include "ParameterEditor.h"
#include "ui_ParameterEditor.h"

#include <LibWidget/SValidator.h>

ParameterEditor::ParameterEditor(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ParameterEditor),
    m_min(-9999.0),
    m_max(9999.0),
    m_decimals(2)
{
    ui->setupUi(this);
    m_rx.setPattern("(\\.){0,1}0+$");
    m_text = ui->lineEdit->text();
    m_label = ui->labelName->text();
    m_unit = ui->labelUnit->text();
    m_value = m_text.toDouble();
// input double only
    ui->lineEdit->setValidator(nullptr);//clear old range
    SDoubleValidator* validator = new SDoubleValidator(m_min, m_max, m_decimals, this);
    ui->lineEdit->setValidator(validator);

    setText("");
    setLabel("");
    setUnit("");
    connect(ui->lineEdit, SIGNAL(textEdited(QString)), this ,SLOT(onTextEdited(QString)));
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SIGNAL(sig_editingFinished()));
}

ParameterEditor::~ParameterEditor()
{
    delete ui;
}

void ParameterEditor::setModified(bool modify)
{
    if (modify)
        ui->lineEdit->setStyleSheet(QString("background:transparent;border-width:0;border-style:outset;color: red"));
    else
        ui->lineEdit->setStyleSheet(QString("background:transparent;border-width:0;border-style:outset;color: rgb(55, 75, 255)"));
}

void ParameterEditor::setText(const QString& text)
{
    if (m_text == text)
        return;
/**
QFontMetrics elidfont(ui->lineEdit->font());
ui->lineEdit->setText (elidfont.elidedText ("textwangjianwangjianwangjianwangjian", Qt::ElideRight, ui->lineEdit->width()));
*/
    ui->lineEdit->setText(text);
    m_text = text;
    setValue(m_text.toDouble());
    emit textChanged(m_text);
}

void ParameterEditor::setLabel(const QString& label)
{
    if (m_label == label)
        return;

    ui->labelName->setText(label);
    m_label = label;
    emit labelChanged(m_label);
}

void ParameterEditor::setUnit(const QString& unit)
{
    if (m_unit == unit)
        return;

    ui->labelUnit->setText(unit);
    m_unit = unit;
    emit unitChanged(m_unit);
}

void ParameterEditor::setValue(double value)
{
    if (qFuzzyCompare(m_value, value))
        return;

    m_value = value;
    //省略多余的0
    setText(QString::number(m_value, 'f', 2).replace(m_rx, ""));
    emit valueChanged(m_value);
}
/*!
 * \brief ParameterEditor::setRange
 * 设置编辑器的输入范围， 并将范围使用[min,max]显示在单位后
 * \param min
 * \param max
 */
void ParameterEditor::setRange(double min, double max)
{
    if (qFuzzyCompare(m_min, min) && qFuzzyCompare(m_max, max))
        return;
    m_max = max;
    m_min = min;
    ui->lineEdit->setValidator(nullptr);//clear old range

    SDoubleValidator* validator = new SDoubleValidator(min, max, m_decimals, ui->lineEdit);
    ui->lineEdit->setValidator(validator);
    ui->labelUnit->setText(QString("%1[%2,%3]").arg(m_unit).arg(m_min).arg(m_max));
}

void ParameterEditor::setDecimals(int decimals)
{
    if (m_decimals == decimals)
        return ;
    ui->lineEdit->setValidator(nullptr);//clear old range

    SDoubleValidator* validator = new SDoubleValidator(m_min, m_max, decimals, ui->lineEdit);
    ui->lineEdit->setValidator(validator);
}

void ParameterEditor::onTextEdited(const QString &text)
{
    if (text == m_text)
        return;
    m_text = text;

    setValue(text.toDouble());
    emit textEdited();
}
