#include "uiStaticASG.h"
#include <QDebug>
#include <QFile>
#include <QSettings>
#include <cGlobalStruct.h>

#define ASG_DAC_COUNTS 10
uiStaticASG::uiStaticASG( QWidget *parent):
    qUiWidget(parent)
{
ui.setupUi(this);
}

uiStaticASG::~uiStaticASG()
{

}

void uiStaticASG::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiStaticASG::initUI(QString& filePath)
{

    for(int i=0;i<8;i++){
        if(!ui.UI_TABLE_ASG1_F->verticalHeaderItem(i))
            ui.UI_TABLE_ASG1_F->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG2_F->verticalHeaderItem(i))
            ui.UI_TABLE_ASG2_F->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG3_F->verticalHeaderItem(i))
            ui.UI_TABLE_ASG3_F->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG4_F->verticalHeaderItem(i))
            ui.UI_TABLE_ASG4_F->setVerticalHeaderItem(i,new QTableWidgetItem(""));

        if(!ui.UI_TABLE_ASG1_B->verticalHeaderItem(i))
            ui.UI_TABLE_ASG1_B->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG2_B->verticalHeaderItem(i))
            ui.UI_TABLE_ASG2_B->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG3_B->verticalHeaderItem(i))
            ui.UI_TABLE_ASG3_B->setVerticalHeaderItem(i,new QTableWidgetItem(""));
        if(!ui.UI_TABLE_ASG4_B->verticalHeaderItem(i))
            ui.UI_TABLE_ASG4_B->setVerticalHeaderItem(i,new QTableWidgetItem(""));
    }
    return loadIniFromFile(filePath);
}

bool uiStaticASG::saveIniToFile(QString& filePath)
{
    if(filePath.isEmpty()|| !QFile::exists(filePath))
        return false;
    QSettings configIniRead(filePath, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");
    configIniRead.setValue("/ASG/str", getParam());
    return true;
}

bool uiStaticASG::loadIniFromFile(QString& filePath, bool updateUI)
{
    if(filePath.isEmpty()|| !QFile::exists(filePath))
        return false;
    QSettings configIniRead(filePath, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");
    QString tempStr= configIniRead.value("/ASG/str").toString();
    if(updateUI)
        return setParam(tempStr);
    return true;
}

void uiStaticASG::titleUpdate(QString strTitle)
{
    if(!strTitle.isEmpty()){
        QStringList tmpList=strTitle.split(";");
        if(tmpList.size()==40){
            for(int i=0;i<8;i++){
                ui.UI_TABLE_ASG1_F->verticalHeaderItem(i)->setText(tmpList[i]);
                ui.UI_TABLE_ASG2_F->verticalHeaderItem(i)->setText(tmpList[i]);
                ui.UI_TABLE_ASG3_F->verticalHeaderItem(i)->setText(tmpList[i]);
                ui.UI_TABLE_ASG4_F->verticalHeaderItem(i)->setText(tmpList[i]);
            }
            for(int i=8;i<16;i++){
                ui.UI_TABLE_ASG1_B->verticalHeaderItem(i-8)->setText(tmpList[i]);
                ui.UI_TABLE_ASG2_B->verticalHeaderItem(i-8)->setText(tmpList[i]);
                ui.UI_TABLE_ASG3_B->verticalHeaderItem(i-8)->setText(tmpList[i]);
                ui.UI_TABLE_ASG4_B->verticalHeaderItem(i-8)->setText(tmpList[i]);
            }
            return;
        }
    }
    for(int i=0;i<8;i++){
        ui.UI_TABLE_ASG1_F->verticalHeaderItem(i)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG2_F->verticalHeaderItem(i)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG3_F->verticalHeaderItem(i)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG4_F->verticalHeaderItem(i)->setText(QString("DA%1").arg(i));
    }
    for(int i=8;i<16;i++){
        ui.UI_TABLE_ASG1_B->verticalHeaderItem(i-8)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG2_B->verticalHeaderItem(i-8)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG3_B->verticalHeaderItem(i-8)->setText(QString("DA%1").arg(i));
        ui.UI_TABLE_ASG4_B->verticalHeaderItem(i-8)->setText(QString("DA%1").arg(i));
    }
}

void uiStaticASG::getParam(QString& strParam, QTableWidget* pTableWidget)
{
    for(int row= 0; row< ASG_DAC_COUNTS; row++){
        if(row!= ASG_DAC_COUNTS- 1)
        strParam+=pTableWidget->item(row, 0)->text()+";"
                +pTableWidget->item(row, 1)->text()+";"
                +pTableWidget->item(row, 2)->text()+"@";
        else
            strParam+=pTableWidget->item(row, 0)->text()+";"
                    +pTableWidget->item(row, 1)->text()+";"
                    +pTableWidget->item(row, 2)->text()+"&";
    }
}

QString uiStaticASG::getParam()
{
    QString tempStr;
    getParam(tempStr, ui.UI_TABLE_ASG1_F);
    getParam(tempStr, ui.UI_TABLE_ASG1_B);
    getParam(tempStr, ui.UI_TABLE_ASG2_F);
    getParam(tempStr, ui.UI_TABLE_ASG2_B);
    getParam(tempStr, ui.UI_TABLE_ASG3_F);
    getParam(tempStr, ui.UI_TABLE_ASG3_B);
    getParam(tempStr, ui.UI_TABLE_ASG4_F);
    getParam(tempStr, ui.UI_TABLE_ASG4_B);
    return tempStr;
}

bool uiStaticASG::setParam(QString& strParam, QTableWidget* pTableWidget)
{
    if(strParam.isEmpty()||!pTableWidget)
        return false;
    QStringList paramList=strParam.split("@");
    if(paramList.size()< ASG_DAC_COUNTS)
        return false;
    for(int nRow= 0; nRow< ASG_DAC_COUNTS; nRow++){
        QStringList tmpList=paramList[nRow].split(";");
        if(tmpList.size()<3)
            return false;
        for(int nColumn=0;nColumn<3;nColumn++){
            pTableWidget->item(nRow, nColumn)->setText(tmpList[nColumn]);
        }
    }
    return true;
}

bool uiStaticASG::setParam(QString strParam)
{
    if(strParam.isEmpty())
        return false;
    QStringList widgetList=strParam.split("&");
    if(widgetList.size()< 8)//table sum
        return false;
    setParam(widgetList[0], ui.UI_TABLE_ASG1_F);
    setParam(widgetList[1], ui.UI_TABLE_ASG1_B);
    setParam(widgetList[2], ui.UI_TABLE_ASG2_F);
    setParam(widgetList[3], ui.UI_TABLE_ASG2_B);
    setParam(widgetList[4], ui.UI_TABLE_ASG3_F);
    setParam(widgetList[5], ui.UI_TABLE_ASG3_B);
    setParam(widgetList[6], ui.UI_TABLE_ASG4_F);
    setParam(widgetList[7], ui.UI_TABLE_ASG4_B);
    return true;
}

//bool uiStaticASG::getParamASG1(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT)
//{
//    if(pDMSIT_STRUCT.pStructDAC.size()<= 0)
//        return false;
//    for(int i= 0; i< 40; i++){
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[i]= 0.0;
//        pDMSIT_STRUCT.DacGain[i]=1.0;
//        pDMSIT_STRUCT.DacOffset[i]=0.0;
//    }
//    for(int index = 0; index < ASG_DAC_COUNTS; index++){
//        if((ui.UI_TABLE_ASG1_F->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG1_F->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG1_F->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index]=ui.UI_TABLE_ASG1_F->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index]=ui.UI_TABLE_ASG1_F->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index]=ui.UI_TABLE_ASG1_F->item(index, 2)->text().toDouble();

//        if((ui.UI_TABLE_ASG1_B->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG1_B->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG1_B->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+ ASG_DAC_COUNTS]=ui.UI_TABLE_ASG1_B->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+ ASG_DAC_COUNTS]=ui.UI_TABLE_ASG1_B->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+ ASG_DAC_COUNTS]=ui.UI_TABLE_ASG1_B->item(index, 2)->text().toDouble();
//    }
//    return true;
//}

//bool uiStaticASG::getParamASG2(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT)
//{
//    if(pDMSIT_STRUCT.pStructDAC.size()<= 0)
//        return false;
//    for(int i=40;i<80;i++){
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[i]= 0.0;
//        pDMSIT_STRUCT.DacGain[i]=1.0;
//        pDMSIT_STRUCT.DacOffset[i]=0.0;
//    }
//    for(int index = 0; index < ASG_DAC_COUNTS; index++){
//        if((ui.UI_TABLE_ASG2_F->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG2_F->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG2_F->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+ 40]=ui.UI_TABLE_ASG2_F->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+ 40]=ui.UI_TABLE_ASG2_F->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+ 40]=ui.UI_TABLE_ASG2_F->item(index, 2)->text().toDouble();

//        if((ui.UI_TABLE_ASG2_B->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG2_B->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG2_B->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+ 50]=ui.UI_TABLE_ASG2_B->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+ 50]=ui.UI_TABLE_ASG2_B->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+ 50]=ui.UI_TABLE_ASG2_B->item(index, 2)->text().toDouble();
//    }
//    return true;
//}

//bool uiStaticASG::getParamASG3(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT)
//{
//    if(pDMSIT_STRUCT.pStructDAC.size()<= 0)
//        return false;
//    for(int i=80;i<120;i++){
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[i]= 0.0;
//        pDMSIT_STRUCT.DacGain[i]=1.0;
//        pDMSIT_STRUCT.DacOffset[i]=0.0;
//    }
//    for(int index = 0; index < ASG_DAC_COUNTS; index++){
//        if((ui.UI_TABLE_ASG3_F->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG3_F->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG3_F->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+80]=ui.UI_TABLE_ASG3_F->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+80]=ui.UI_TABLE_ASG3_F->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+80]=ui.UI_TABLE_ASG3_F->item(index, 2)->text().toDouble();

//        if((ui.UI_TABLE_ASG3_B->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG3_B->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG3_B->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+90]=ui.UI_TABLE_ASG3_B->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+90]=ui.UI_TABLE_ASG3_B->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+90]=ui.UI_TABLE_ASG3_B->item(index, 2)->text().toDouble();
//    }
//    return true;
//}

//bool uiStaticASG::getParamASG4(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT)
//{
//    if(pDMSIT_STRUCT.pStructDAC.size()<= 0)
//        return false;
//    for(int i=120;i<160;i++){
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[i]= 0.0;
//        pDMSIT_STRUCT.DacGain[i]=1.0;
//        pDMSIT_STRUCT.DacOffset[i]=0.0;
//    }
//    for(int index = 0; index < ASG_DAC_COUNTS; index++){
//        if((ui.UI_TABLE_ASG4_F->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG4_F->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG4_F->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+120]=ui.UI_TABLE_ASG4_F->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+120]=ui.UI_TABLE_ASG4_F->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+ 120]=ui.UI_TABLE_ASG4_F->item(index, 2)->text().toDouble();

//        if((ui.UI_TABLE_ASG4_B->item(index, 0) == nullptr)||
//                (ui.UI_TABLE_ASG4_B->item(index, 1) == nullptr)||
//                (ui.UI_TABLE_ASG4_B->item(index, 2) == nullptr))
//            return false;
//        pDMSIT_STRUCT.DacGain[index+130]=ui.UI_TABLE_ASG4_B->item(index, 0)->text().toDouble();
//        pDMSIT_STRUCT.DacOffset[index+130]=ui.UI_TABLE_ASG4_B->item(index, 1)->text().toDouble();
//        (pDMSIT_STRUCT.pStructDAC[0]).DacValue[index+ 130]=ui.UI_TABLE_ASG4_B->item(index, 2)->text().toDouble();
//    }
//    return true;
//}

