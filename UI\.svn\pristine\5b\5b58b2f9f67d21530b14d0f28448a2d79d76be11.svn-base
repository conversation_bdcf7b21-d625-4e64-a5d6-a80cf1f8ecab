﻿#include "uiIonScanParamEditor.h"

/**
 * @brief
 * 该类支持ProductIonScan、PrecursorIonScan、NeutralLosScan 三种Event类型的参数显示与编辑
 * @param element
 * @param parent
 */
uiIonScanParamEditor::uiIonScanParamEditor(const QString& type, QWidget* parent)
    : uiBaseParamEditor(parent)
    , m_strEventType(type)
{
    ui.setupUi(this);

}

uiIonScanParamEditor::~uiIonScanParamEditor()
{

}

void uiIonScanParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiIonScanParamEditor::initUI(QString& filePath)
{
    uiBase.uiBaseParamEditorLayout->insertWidget(0, ui.uiIonScanParamEditorWidget);
    initPage(m_strEventType);
    return true;
}
/**
 * @brief
 *
 */
//void uiIonScanParamEditor::saveParameter()
//{
//    //compound name
//    QDomElement tmp = m_domElement.firstChildElement(tagCompoundName());
//    if (!tmp.firstChild().isText()) {
//        QDomText text = m_pDoc->createTextNode("");
//        tmp.appendChild(text);
//    }
//    tmp.firstChild().setNodeValue(ui->lineEdit_compName->text().trimmed());
//    //start / End time
//    tmp = m_domElement.firstChildElement(tagStartTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_startTime->text().toDouble() * 60000));
//    tmp = m_domElement.firstChildElement(tagEndTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_endTime->text().toDouble() * 60000));
//    //EventTime
//    tmp = m_domElement.firstChildElement(tagEventTime());
//    tmp.firstChild().setNodeValue(QString::number(ui->lineEdit_eventTime->text().toDouble()));
//    //Q1 Q3 resolution
//    tmp = m_domElement.firstChildElement(tagQ1Resolution());
//    tmp.firstChild().setNodeValue(QString::number(ui->comboBox_Q1->currentIndex() + 1));
//    tmp = m_domElement.firstChildElement(tagQ3Resolution());
//    tmp.firstChild().setNodeValue(QString::number(ui->comboBox_Q3->currentIndex() + 1));
//    //Channel
//    tmp = m_domElement.firstChildElement(tagChannel());
//    if (!tmp.isNull()) {
//        QDomElement temp1 = tmp.firstChildElement(tagStartMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_startMZ->text());
//        temp1 = tmp.firstChildElement(tagEndMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_endMZ->text());
//        temp1 = tmp.firstChildElement(tagAcqModeMz());
//        temp1.firstChild().setNodeValue(ui->lineEdit_ionMZ->text());
//        temp1 = tmp.firstChildElement(tagCE());
//        temp1.firstChild().setNodeValue(ui->lineEdit_CE->text());
//    }
//}
/**
 * @brief
 *
 */
void uiIonScanParamEditor::initPage(const QString& eventType)
{
    //QString eventType = m_domElement.firstChildElement(tagAcqMode()).text();
    if (eventType == "ProductIonScan")
        ui.label_IonMZ->setText("Precursor Ion m/z:");
    else if (eventType == "PrecursorIonScan")
        ui.label_IonMZ->setText("Product Ion m/z:");
    else if (eventType == "NeutralLosScan")
        ui.label_IonMZ->setText("Losses of:");
}
