#include "uiMapSetMZ.h"
#include <LibWidget/SpreadSheet/spreadsheet.h>
#include <LibWidget/SpreadSheet/cell.h>
#include <QHeaderView>
#include <QMetaEnum>
#include <QSettings>


uiMapSetMZ::uiMapSetMZ(QWidget *parent) :
    qUiWidget(parent)
{
    ui.setupUi(this);
}

uiMapSetMZ::~uiMapSetMZ()
{

}


/**
 * @brief PreferenceDeveloper::getMzVoltageTablesContent
 * 按行获取表中的存储的mz对应参数表， 遇到mz参数为空时跳过，获取下一行
 * @return
 */
bool uiMapSetMZ::getMzHDACFromTable(HZH::paramMZ_Voltages& paramMZ_Voltages,
                                    mzVoltageTableEditor* table)
{
    HZH::paramMZ_Voltages mzVoltages;
    for (int row = 0; row < table->rowCount(); ++row) {
        if (table->item(row, 0) == nullptr
                || table->item(row, 0) ->text().isEmpty())
            continue;
        bool ok;
        double mz = table->item(row, 0)->text().toDouble(&ok);
        if (!ok)
            return false;
        double paramBuffer[12+ 36];
        for (int col = 0; col < 12+ 36; ++col) {
            paramBuffer[col] = table->text(row, col+1).toDouble(&ok);
            if (!ok)
                return false;
        }
        HZH::ParamAdvanceBase param;
        memcpy(param.voltages, paramBuffer, (12+ 36)* sizeof (double));
        mzVoltages.mzVoltages.insert(mz, param);
    }
    paramMZ_Voltages= mzVoltages;
    return true;
}

bool uiMapSetMZ::getMzLDACFromTable(HZH::paramMZ_Voltages& paramMZ_Voltages,
                                    mzVoltageTableEditor* table)
{
    HZH::paramMZ_Voltages mzVoltages;
    for (int row = 0; row < table->rowCount(); ++row) {
        if (table->item(row, 0) == nullptr
                || table->item(row, 0) ->text().isEmpty())
            continue;
        bool ok;
        double mz = table->item(row, 0)->text().toDouble(&ok);
        if (!ok)
            return false;
        double paramBuffer[12+ 36];
        for (int col = 0; col < 12+ 36; ++col) {
            paramBuffer[col] = table->text(row, col+1).toDouble(&ok);
            if (!ok)
                return false;
        }
        HZH::ParamAdvanceBase param;
        memcpy(param.voltages, paramBuffer, (12+ 36)* sizeof (double));
        mzVoltages.mzVoltages.insert(mz, param);
    }
    paramMZ_Voltages= mzVoltages;
    return true;
}

bool uiMapSetMZ::initUI(QString& filePath)
{

    mlistVoltageTableEditor<< ui.UI_W_Q1Scan_SETMZ;
    ui.UI_W_Q1Scan_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS);

    mlistVoltageTableEditor<< ui.UI_W_Q3Scan_SETMZ;
    ui.UI_W_Q3Scan_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS);

    mlistVoltageTableEditor<< ui.UI_W_ProductIonScan_SETMZ;
    ui.UI_W_ProductIonScan_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_ProductIonScan_POS);

    mlistVoltageTableEditor<< ui.UI_W_PrecursorIonScan_SETMZ;
    ui.UI_W_PrecursorIonScan_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_PrecursorIonScan_POS);

    mlistVoltageTableEditor<< ui.UI_W_NeutralLossScan_SETMZ;
    ui.UI_W_NeutralLossScan_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_NeutralLossScan_POS);

    mlistVoltageTableEditor<< ui.UI_W_Q1SIM_SETMZ;
    ui.UI_W_Q1SIM_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS);

    mlistVoltageTableEditor<< ui.UI_W_Q3SIM_SETMZ;
    ui.UI_W_Q3SIM_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS);

    mlistVoltageTableEditor<< ui.UI_W_MRM_SETMZ;
    ui.UI_W_MRM_SETMZ->setProperty("type", cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_MRM_POS);

    for(int i= 0; i< 8; ++i){
        mlistVoltageTableEditor[i]->resetTableSize(21, 49);//mlistVoltageTableEditor[i]->resetTableSize(99, 16);
        mlistVoltageTableEditor[i]->horizontalHeader()->setDefaultSectionSize(60);
        //        connect(mlistVoltageTableEditor[i], &mzVoltageTableEditor::curMzVoltageMapChanged,
        //                this, &uiMapSetMZ::mzVoltageMapChanged);
    }
    if(!loadIniFromFile(filePath, mlistVoltageTableEditor))
        return false;
    on_UI_PB_UPDATE_SETMZ_clicked();
    return true;
}

void uiMapSetMZ::updateMZVoltageTablesHeader(/*mzVoltageTableEditor *pVoltageTableEditor*/)
{
    QStringList horLabels;
    horLabels << tr("m/z");
    for(int i= 0; i< ui.UI_TW_SET_SETMZ->rowCount(); ++i){
        horLabels<< ui.UI_TW_SET_SETMZ->item(i, 0)->text();
    }
    for(int i= 0; i< ui.UI_TW_SET_SETMZ36->rowCount(); ++i){
        horLabels<< ui.UI_TW_SET_SETMZ36->item(i, 0)->text();
    }
    foreach (auto& pVoltageTableEditor, mlistVoltageTableEditor) {
        if(pVoltageTableEditor)
            pVoltageTableEditor->setHorizontalHeaderLabels(horLabels);
    }
}

QString uiMapSetMZ::toStringHDAC()
{
    QStringList tmpList;
    for(int i= 0; i< ui.UI_TW_SET_SETMZ->rowCount(); ++i){
        tmpList<< ui.UI_TW_SET_SETMZ->item(i, 0)->text()+":"
                  + ui.UI_TW_SET_SETMZ->item(i, 1)->text()+":"
                  + ui.UI_TW_SET_SETMZ->item(i, 2)->text();
    }
    for(int i= 0; i< ui.UI_TW_SET_SETMZ36->rowCount(); ++i){
        tmpList<< ui.UI_TW_SET_SETMZ36->item(i, 0)->text()+":"
                  + ui.UI_TW_SET_SETMZ36->item(i, 1)->text()+":"
                  + ui.UI_TW_SET_SETMZ36->item(i, 2)->text();
    }
    return tmpList.join(";");
}

bool uiMapSetMZ::fromString(QString& str)
{
    if(str.isEmpty())
        return false;
    QStringList tmpList= str.split(";");
    if(tmpList.size()!=NUM_HDAC+ NUM_LDAC)
        return false;
    for(int i= 0; i< tmpList.size(); ++i){
        QStringList tmpList1= tmpList[i].split(":");
        if(tmpList1.size()!= 3)
            continue;
        if(i< NUM_HDAC){
            ui.UI_TW_SET_SETMZ->item(i, 0)->setText(tmpList1[0]);
            ui.UI_TW_SET_SETMZ->item(i, 1)->setText(tmpList1[1]);
            ui.UI_TW_SET_SETMZ->item(i, 2)->setText(tmpList1[2]);
        }else{
            ui.UI_TW_SET_SETMZ36->item(i-NUM_HDAC, 0)->setText(tmpList1[0]);
            ui.UI_TW_SET_SETMZ36->item(i-NUM_HDAC, 1)->setText(tmpList1[1]);
            ui.UI_TW_SET_SETMZ36->item(i-NUM_HDAC, 2)->setText(tmpList1[2]);
        }
    }
    return true;
}

bool uiMapSetMZ::getGainOffsetHDAC(_STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC)
{
    int rowCount= ui.UI_TW_SET_SETMZ->rowCount();
    p_STRUCT_ADJUST_DAC.resize(rowCount);
    bool ok= true;
    for(int i= 0; i< rowCount; ++i){
        p_STRUCT_ADJUST_DAC.gainDAC[i]= ui.UI_TW_SET_SETMZ->item(i, 1)->text().toDouble(&ok);
        if(!ok)
            return false;
        p_STRUCT_ADJUST_DAC.offsetDAC[i]= ui.UI_TW_SET_SETMZ->item(i, 2)->text().toDouble(&ok);
        if(!ok)
            return false;
        if(p_STRUCT_ADJUST_DAC.gainDAC[i]==0)
            p_STRUCT_ADJUST_DAC.gainDAC[i]=1;
    }
    return true;
}

bool uiMapSetMZ::getGainOffsetLDAC(_STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC)
{
    int rowCount36= ui.UI_TW_SET_SETMZ36->rowCount();
    p_STRUCT_ADJUST_DAC.resize(rowCount36);
    bool ok= true;
    for(int i= 0; i< rowCount36; ++i){
        p_STRUCT_ADJUST_DAC.gainDAC[i]= ui.UI_TW_SET_SETMZ36->item(i, 1)->text().toDouble(&ok);
        if(!ok)
            return false;
        p_STRUCT_ADJUST_DAC.offsetDAC[i]= ui.UI_TW_SET_SETMZ36->item(i, 2)->text().toDouble(&ok);
        if(!ok)
            return false;
        if(p_STRUCT_ADJUST_DAC.gainDAC[i]==0)
            p_STRUCT_ADJUST_DAC.gainDAC[i]=1;
    }
    return true;
}
/**
 * @brief PreferenceDeveloper::setValue
 * 向tb的第r行，第c列插入值value。 r和c的起始索引为 1
 * @param tb
 * @param r
 * @param c
 * @param value
 */
void uiMapSetMZ::setValue(QTableWidget *tb, int r, int c, double value)
{
    if (tb == nullptr || r < 1 || c < 1)
        return;
    Cell* item = static_cast<Cell*>(tb->item(r-1, c-1));
    if (nullptr == item){
        item = new Cell;
        tb->setItem(r-1, c-1, item);
    }
    item->setFormula(QString::number(value));
}

bool uiMapSetMZ::saveIniToFile(QString& filePath)
{
    if(filePath.isEmpty()|| !QFile::exists(filePath))
        return false;
    QSettings configIniRead(filePath, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");
    QString str;
    QStringList listString;
    listString<<"/Q1Scan/MZ_Voltages";
    listString<<"/Q3Scan/MZ_Voltages";
    listString<<"/ProductIonScan/MZ_Voltages";
    listString<<"/PrecursorIonScan/MZ_Voltages";
    listString<<"/NeutralLossScan/MZ_Voltages";
    listString<<"/Q1SIM/MZ_Voltages";
    listString<<"/Q3SIM/MZ_Voltages";
    listString<<"/MRM/MZ_Voltages";
    for(int i= 0; i< 8; ++i){
        HZH::paramMZ_Voltages tmpParamMZ_Voltages;
        if(!getMzHDACFromTable(tmpParamMZ_Voltages, mlistVoltageTableEditor[i]))
            return false;
        str= tmpParamMZ_Voltages.toString();

        configIniRead.setValue(listString[i], str);
        //updateMZVoltageTablesHeader(mlistVoltageTableEditor[i]);
    }
    str= toStringHDAC();
    configIniRead.setValue("/Q13Scan/HDAC_SET", str);
    return true;
}

bool uiMapSetMZ::loadIniFromFile(QString& filePath, QList<mzVoltageTableEditor*>& listVoltageTableEditor)
{
    if(filePath.isEmpty()|| !QFile::exists(filePath))
        return false;
    QStringList listString;
    listString<<"/Q1Scan/MZ_Voltages";
    listString<<"/Q3Scan/MZ_Voltages";
    listString<<"/ProductIonScan/MZ_Voltages";
    listString<<"/PrecursorIonScan/MZ_Voltages";
    listString<<"/NeutralLossScan/MZ_Voltages";
    listString<<"/Q1SIM/MZ_Voltages";
    listString<<"/Q3SIM/MZ_Voltages";
    listString<<"/MRM/MZ_Voltages";

    HZH::paramMZ_Voltages tmpParamMZ_Voltages;
    QSettings configIniRead(filePath, QSettings::IniFormat);
    configIniRead.setIniCodec("utf-8");
    QString str;
    for(int i= 0; i< 8; ++i){
        str= configIniRead.value(listString[i], "").toString();
        if(tmpParamMZ_Voltages.fromString(str)){
            listVoltageTableEditor[i]->setCurMzVoltageMap(tmpParamMZ_Voltages);
        }
    }
    str= configIniRead.value("/Q13Scan/HDAC_SET", "").toString();
    return fromString(str);
}
