﻿#pragma once

#include <QtCore>

//tags
static inline QString tagMSParameter()        {return QStringLiteral("MSParameter");        }
static inline QString tagMSInterface()        {return QStringLiteral("MSInterface");        }
static inline QString tagSegment()            {return QStringLiteral("Segment");        }
//Event
static inline QString tagEvent()              {return QStringLiteral("Event");        }
static inline QString tagAcqMode()            {return QStringLiteral("AcqMode");        }
static inline QString tagPolarity()           {return QStringLiteral("Polarity");        }
static inline QString tagCompoundName()       {return QStringLiteral("CompoundName");        }
static inline QString tagEventTime()          {return QStringLiteral("EventTime");        }
static inline QString tagStartTime()          {return QStringLiteral("StartTime");        }
static inline QString tagEndTime()            {return QStringLiteral("EndTime");        }
//Channel
static inline QString tagChannel()                {return QStringLiteral("Channel");        }
static inline QString tagCE()                     {return QStringLiteral("CE");        }
static inline QString tagStartMz()                {return QStringLiteral("StartMz");        }
static inline QString tagEndMz()                  {return QStringLiteral("EndMz");        }
static inline QString tagAcqModeMz()              {return QStringLiteral("AcqModeMz");        }
static inline QString tagDwellTime()              {return QStringLiteral("DwellTime");        }
static inline QString tagQ1Resolution()           {return QStringLiteral("Q1Resolution");        }
static inline QString tagQ3Resolution()           {return QStringLiteral("Q3Resolution");        }
static inline QString tagQ1PreBias()              {return QStringLiteral("Q1PreBias");        }
static inline QString tagQ3PreBias()              {return QStringLiteral("Q3PreBias");        }
static inline QString tagDLBias()                 {return QStringLiteral("DLBias");           }
static inline QString tagQarrayBias()             {return QStringLiteral("QarrayBias");        }
static inline QString tagAutoMSMSInclusion()      {return QStringLiteral("AutoMSMSInclusion");        }
static inline QString tagPauseDwellTimeAutoSetting()    {return QStringLiteral("PauseDwellTimeAutoSetting");        }
static inline QString tagDwellTimeAutoSetting()         {return QStringLiteral("DwellTimeAutoSetting");        }
static inline QString tagPauseTimeAutoSetting()         {return QStringLiteral("PauseTimeAutoSetting");        }
static inline QString tagPauseTime()                    {return QStringLiteral("PauseTime");           }


//attribute
static inline QString attrVersion()        {return QStringLiteral("Version");           }
static inline QString attrNo()             {return QStringLiteral("no");                }
static inline QString attrUseTuningFile()  {return QStringLiteral("useTuningFile");     }



/**************************** MSInterface ****************************/
static inline QString tagInterface()                  {return QStringLiteral("Interface");           }
static inline QString tagESIusingInterfaceVoltage()   {return QStringLiteral("ESIusingInterfaceVoltage");    }
static inline QString tagAPCIusingInterfaceVoltage()  {return QStringLiteral("APCIusingInterfaceVoltage");     }
static inline QString tagSystemName()                 {return QStringLiteral("SystemName");           }
static inline QString tagNebulizingGasFlow()          {return QStringLiteral("NebulizingGasFlow");          }
static inline QString tagDLTemparature()              {return QStringLiteral("DLTemparature");     }
static inline QString tagHeatBlockTemparature()       {return QStringLiteral("HeatBlockTemparature");      }
static inline QString tagDryingGasFlow()              {return QStringLiteral("DryingGasFlow");                }
static inline QString tagCIDGas()                     {return QStringLiteral("CIDGas");     }
static inline QString tagConversionDynode()         {return QStringLiteral("ConversionDynode");           }
static inline QString tagInitialValvePosition()     {return QStringLiteral("InitialValvePosition");      }
//attribute
static inline QString attrUnit()                    {return QStringLiteral("unit");     }
static inline QString attrUseParameter()            {return QStringLiteral("useParameter");           }

