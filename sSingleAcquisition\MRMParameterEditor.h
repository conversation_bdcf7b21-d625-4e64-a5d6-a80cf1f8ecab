﻿
#include <UI/uiSingleAcquisition/uiMRMParameterEditor.h>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <sTune/sMapSetMZ.h>


class MRMParameterEditor : public uiMRMParameterEditor
{
    Q_OBJECT
public:
    MRMParameterEditor(sMapSetMZ* pMapSetMZ,
                       QWidget* parent = nullptr);
    ~MRMParameterEditor(){}
    void initClass(QString& filePath);
    //void saveParameter() override;
    bool getParamEvent(QList<cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM,
                       QList<cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM>& p_CHANNEL_EVENT_PARAM_LDAC,
                       int& Polarity);

private:
    sMapSetMZ* mMapSetMZ= nullptr;

};


