﻿#include "PreferenceManager.h"
#include <QApplication>
#include <QDataStream>
#include <QFile>
#include <QProcess>
#include <QSettings>

#include "PreferenceEditor.h"
#include "PublicDefine.h"

#include "I_InsertNotify.h"
#include "I_GlobalVariable.h"

using namespace uma_project;

PreferenceManager::PreferenceManager(QObject* parent)
    : QObject(parent)
    , m_prfsEditor(new PreferenceEditor(this))
    , m_prfsDeveloper(nullptr/*new PreferenceDeveloper()*/)
    , m_eventParamPtr(nullptr)
{
    m_mzVoltages.resize(12); // 12 = 3 resolutions x 4 analysisMode
    m_paramNormal.analysisMode = AnalysisMode::FilterScanMode;
    m_prfsEditor->setNormalParam(m_paramNormal);
//    m_prfsDeveloper->updatePreference();

    signalSlotManager();
}

int PreferenceManager::openFile(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly))
        return -1;

    QDataStream in(&file);
    FileHeader f1;

    in >> f1;
    if (f1.magic != uma_project::kUmaProjMagic) {
        file.close();
        return -2;
    }
    if (f1.dataVersion != uma_project::UMA_MAX_VERSION-1) {
        file.close();
        return -3;
    }

    InstrumentType type = I_GlobalVariable::getInstance()->instrumentType();
    if (type != f1.instrumentType)
        I_InsertNotify::getInstance()->insertNotify(tr("Instrument type does not match."), I_InsertNotify::Warning);

    QApplication::setOverrideCursor(Qt::WaitCursor);
    //param
    ParamNormal p1;
    in >> p1;
    m_paramNormal = p1;
    m_prfsEditor->setNormalParam(p1);
    //mz voltages
    for (int i = 0; i < 12 ; ++i){
        in >> m_mzVoltages[i].mzVoltages;
    }
    m_prfsDeveloper->updatePreferenceView();
    m_prfsDeveloper->updateMzVolTableView();
    m_prfsDeveloper->updateMzVoltagesToTable(mzVoltages());
    //scan preference
    in >> *m_prfsDeveloper;

    file.close();
    QApplication::restoreOverrideCursor();
    return 0;
}

bool PreferenceManager::writeFile(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    QDataStream out(&file);
    out.setVersion(QDataStream::Qt_5_8);

    FileHeader header;
    header.instrumentType = I_GlobalVariable::getInstance()->instrumentType();
    out << header;

    QApplication::setOverrideCursor(Qt::WaitCursor);

    out << m_paramNormal;
    for (int i = 0; i < 12; ++i)
    {
        out << m_mzVoltages[i].mzVoltages;
    }
    out << *m_prfsDeveloper;

    file.close();

    QApplication::restoreOverrideCursor();

    return true;
}

paramMZ_Voltages& PreferenceManager::mzVoltages()
{
    int index = 3 * m_paramNormal.analysisMode + m_paramNormal.resolution;
    Q_ASSERT(index > -1 && index < 12);
    return m_mzVoltages[index];
}

ParamAdvanceBase PreferenceManager::getMzPreference(double mz)
{
    ParamAdvanceBase base;
    base = mzVoltages().getMZParameters(mz);
    return base;
}

void PreferenceManager::saveMzPreference(double mz, const ParamAdvanceBase& param)
{
    mzVoltages().mzVoltages.insert(mz, param);
    m_prfsDeveloper->updatePreference();
}

void PreferenceManager::setLabEventParamPtr(LabSolutionsRW::Event *eventParamPtr)
{
    if (eventParamPtr == m_eventParamPtr)
        return;
    m_eventParamPtr = eventParamPtr;
    emit sig_labEventParamChanged(m_eventParamPtr);
}

void PreferenceManager::onQqQDetectorSwitch(bool uma)
{
    m_prfsDeveloper->switchDetectorToUMA(uma);
}

void PreferenceManager::onMzVoltagesTableChanged()
{
    paramMZ_Voltages mzVols = m_prfsDeveloper->getMzVoltagesFromTable();
    if (mzVoltages() == mzVols)
        return;
    mzVoltages() = mzVols;
    m_prfsDeveloper->updatePreference();

    emit modified();
}

void PreferenceManager::onNormalParamEdited(const ParamNormal &param)
{
    if (param == m_paramNormal)
        return;
    m_paramNormal = param;
    m_prfsDeveloper->updatePreference();

    emit modified();
}

void PreferenceManager::signalSlotManager()
{
    connect(m_prfsEditor, &PreferenceEditor::normalParamEdited,
            this, &PreferenceManager::onNormalParamEdited);
}

void PreferenceManager::setPrfsDeveloper(PreferenceDeveloper *prfsDeveloper)
{
    m_prfsDeveloper = prfsDeveloper;
    m_prfsDeveloper->setPrfsManager(this);
//    m_prfsDeveloper->updatePreference();

//    connect(m_prfsDeveloper, SIGNAL(modified()), this, SIGNAL(modified()));
//    connect(m_prfsDeveloper, &PreferenceDeveloper::mzVoltageMapChanged,
//            this, &PreferenceManager::onMzVoltagesTableChanged);
}

uma_project::ParamNormal PreferenceManager::getParamNormal() const
{
    return m_paramNormal;
}

void PreferenceManager::setParamNormal(const uma_project::ParamNormal &paramNormal)
{
    if (m_paramNormal == paramNormal)
        return;
    m_paramNormal = paramNormal;
    m_prfsEditor->setNormalParam(m_paramNormal);
    m_prfsDeveloper->updatePreference();
}

PreferenceManager *PreferenceManager::getInstance()
{
    static PreferenceManager prfsMng;
    return &prfsMng;
}

uma_project::ParamNormal* PreferenceManager::getPrfsNormalPointer()
{
    return &m_paramNormal;
}

