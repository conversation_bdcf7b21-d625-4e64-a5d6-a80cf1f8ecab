#include "sMapSetMZ.h"


sMapSetMZ::sMapSetMZ(QWidget *parent) :
    uiMapSetMZ(parent)
{

}

sMapSetMZ::~sMapSetMZ()
{

}

void sMapSetMZ::initClass(QString& filePath)
{
    initUI(filePath);
}

bool sMapSetMZ::updateMemoryParam()
{
    mList_MZ_PARAM_SET_HDAC.clear();
    QList<cTQ_StructCMD_HZH::_MZ_PARAM_SET> tmpListPos, tmpListNeg;
    if((!getMzHDACFromPosTable(tmpListPos))||(tmpListPos.size()!= 8))
        return false;
    if((!getMzHDACFromNegTable(tmpListNeg))||(tmpListNeg.size()!= 8))
        return false;

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[0];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[0];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[1];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[1];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[2];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[2];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[3];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[3];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[4];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[4];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[5];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[5];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[6];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[6];

    mList_MZ_PARAM_SET_HDAC<< tmpListPos[7];
    mList_MZ_PARAM_SET_HDAC<< tmpListNeg[7];

    mList_MZ_PARAM_SET_LDAC.clear();
    QList<cTQ_StructCMD_AD81416::_MZ_PARAM_SET> tmpListPosLDAC, tmpListNegLDAC;
    if((!getMzLDACFromPosTable(tmpListPosLDAC))||(tmpListPosLDAC.size()!= 8))
        return false;
    if((!getMzLDACFromNegTable(tmpListNegLDAC))||(tmpListNegLDAC.size()!= 8))
        return false;

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[0];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[0];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[1];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[1];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[2];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[2];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[3];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[3];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[4];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[4];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[5];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[5];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[6];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[6];

    mList_MZ_PARAM_SET_LDAC<< tmpListPosLDAC[7];
    mList_MZ_PARAM_SET_LDAC<< tmpListNegLDAC[7];
    return true;
}

bool sMapSetMZ::voltages2MzHDAC(const HZH::paramMZ_Voltages& mzVoltages,
                                const _STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC,
                                      cTQ_StructCMD_HZH::_MZ_PARAM_SET& p_MZ_PARAM_SET)
{
    QMapIterator<double, HZH::ParamAdvanceBase> i(mzVoltages.mzVoltages);
    while (i.hasNext()) {
        i.next();
        double paramBuffer[NUM_HDAC];
        for (int col = 0; col < NUM_HDAC; ++col) {
            paramBuffer[col]= (i.value().voltages[col]+ p_STRUCT_ADJUST_DAC.offsetDAC[col])/
                    p_STRUCT_ADJUST_DAC.gainDAC[col];
        }
        memcpy(const_cast<double*>(i.value().voltages), paramBuffer, (NUM_HDAC)* sizeof (double));
    }
    auto it = mzVoltages.mzVoltages.begin();
    while (it != mzVoltages.mzVoltages.end()) {
        p_MZ_PARAM_SET.addMZ(it.key(), it.value().voltages);
        ++it;
    }
    return true;
}

bool sMapSetMZ::voltages2MzLDAC(const HZH::paramMZ_Voltages& mzVoltages,
                                const _STRUCT_ADJUST_DAC& p_STRUCT_ADJUST_DAC,
                                 cTQ_StructCMD_AD81416::_MZ_PARAM_SET& p_MZ_PARAM_SET)
{
    QMapIterator<double, HZH::ParamAdvanceBase> i(mzVoltages.mzVoltages);
    while (i.hasNext()) {
        i.next();
        double paramBuffer[NUM_LDAC];
        for (int col = 0; col < NUM_LDAC; ++col) {
            paramBuffer[col]= (i.value().voltages[col+ NUM_HDAC]+ p_STRUCT_ADJUST_DAC.offsetDAC[col])/
                    p_STRUCT_ADJUST_DAC.gainDAC[col];
        }
        memcpy(const_cast<double*>(i.value().voltages)+ NUM_HDAC, paramBuffer, (NUM_LDAC)* sizeof (double));
    }
    auto it = mzVoltages.mzVoltages.begin();
    while (it != mzVoltages.mzVoltages.end()) {
        p_MZ_PARAM_SET.addMZ(it.key(), it.value().voltages+ NUM_HDAC);
        ++it;
    }
    return true;
}
