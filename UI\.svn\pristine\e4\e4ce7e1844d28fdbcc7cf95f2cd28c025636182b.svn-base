<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>uiEditMRM</class>
 <widget class="QWidget" name="uiEditMRM">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1049</width>
    <height>726</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QCheckBox" name="checkBox">
     <property name="text">
      <string>Experiment</string>
     </property>
     <property name="checked">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Polarity</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBox">
       <item>
        <property name="text">
         <string>Negative</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Positive</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>Spray voltage(V)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox"/>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>Experiment cycle time(ms)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox_2"/>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Advanced Experiment Settings</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="1">
         <widget class="QSpinBox" name="spinBox_3"/>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>Setting time(ms)</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_5">
          <property name="text">
           <string>Pause time(ms)</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="4">
         <widget class="QSpinBox" name="spinBox_4"/>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_6">
          <property name="text">
           <string>Minimum dwell time(ms)</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QSpinBox" name="spinBox_5"/>
        </item>
        <item row="1" column="3">
         <widget class="QLabel" name="label_7">
          <property name="text">
           <string>Maximun dwell time(ms)</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QSpinBox" name="spinBox_6"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>Mass Table</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton">
          <property name="text">
           <string>Import</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_2">
          <property name="text">
           <string>Export</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="UI_TW_TABLE_EMRM"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
