#ifndef SERIALDEVICEMANAGER_H
#define SERIALDEVICEMANAGER_H

#include <QtCore/qglobal.h>
#if defined(SERIALDEVICEMANAGER_LIBRARY)
#  define SERIALDEVICEMANAGER_EXPORT Q_DECL_EXPORT
#else
#  define SERIALDEVICEMANAGER_EXPORT Q_DECL_IMPORT
#endif

#include "cCommSerial.h"
#include <QMutex>
#include <sThread.h>

class SERIALDEVICEMANAGER_EXPORT SerialDeviceManager: public QObject
{
    Q_OBJECT
public:
    /**
     *注意此回调函数中不可进行UI相关操作，仅用作指令解析
     */
    typedef bool (SerialHandle)(QByteArray& byteArray, void* pThis);

    struct SERIALDEVICEMANAGER_EXPORT _STRUCT_SERIAL_CMD{
        quint16 LengthRead_CMD= 0;
        qint32 TimeOutMS_CMD= 500;
        QByteArray ByteArray_CMD;
        SerialHandle* SerialHandle_CMD= nullptr;
        void* Parent= nullptr;
        _STRUCT_SERIAL_CMD();
        _STRUCT_SERIAL_CMD(quint16 lengthRead,
                           QByteArray byteArray,
                           SerialHandle* pSerialHandle,
                           void* pParent,
                           qint32 timeOutMS= 300);
        _STRUCT_SERIAL_CMD & operator=(const _STRUCT_SERIAL_CMD &obj);
    };
    typedef _STRUCT_SERIAL_CMD SerialCMD;
    typedef QList<_STRUCT_SERIAL_CMD> ListSerialCMD;

    struct SERIALDEVICEMANAGER_EXPORT _STRUCT_THREAD{
        QMutex*         Mutex= nullptr;
        SThread*        Thread= nullptr;
        cCommSerial*    CommSerial= nullptr;
        SerialCMD OpenCMD;
        cCommSerial::_STRUCT_SERIAL STRUCT_SERIAL;
        ListSerialCMD ListCMD;
        ListSerialCMD ListThrough;
        unsigned long timeIntervalMs;

        _STRUCT_THREAD(QMutex* pMutex,
                       SThread* pThread,
                       cCommSerial* pCommSerial,
                       cCommSerial::_STRUCT_SERIAL pSTRUCT_SERIAL
                       );
    };
    SerialDeviceManager(QObject *parent = nullptr);
    virtual ~SerialDeviceManager(void);
    /**
    return the index of new serial
    */
    int addSerial(quint16 PID= 29987, quint16 VID= 6790,
                  qint32 DelayTimeMs= 1000,
                  qint32 baudRate= QSerialPort::Baud115200,
                  QSerialPort::Parity Parity= QSerialPort::NoParity,
                  QSerialPort::DataBits DataBits= QSerialPort::Data8,
                  QSerialPort::StopBits StopBits= QSerialPort::OneStop,
                  QSerialPort::OpenModeFlag OpenModeFlag= QIODevice::ReadWrite
                  );
    int addSerial(quint16 PID= 29987, quint16 VID= 6790,
                  qint32 DelayTimeMs= 1000,
                  BaudRateType baudRate= BAUD115200,
                  ParityType tempParity= PAR_NONE,
                  DataBitsType tempDataBits= DATA_8,
                  StopBitsType tempStopBits= STOP_1,
                  QIODevice::OpenMode tempOpenModeFlag= QIODevice::ReadWrite
                  );
    int addSerial(cCommSerial::_STRUCT_SERIAL STRUCT_SERIAL);
    cCommSerial* getSerial(int index);
    SThread* getSerialThread(int index);
    _STRUCT_THREAD* getSTRUCT_THREAD(int index);
    bool setOpenCMD(int index, SerialCMD& pSerialCMD);
    bool openWrite(int index, SerialCMD pSerialCMD);
    void closeSerial(int index);
    bool startThrough(int index, SerialCMD& pSerialCMD, unsigned long timeIntervalMs);
    bool startThrough(int index, ListSerialCMD& pListSerialCMD, unsigned long timeIntervalMs);
    void stopThrough(int index);

protected:
    static int CommThread(void *pParam, const bool &bRunning);

private:
    QList<_STRUCT_THREAD> mListThread;
};

class SERIALDEVICEMANAGER_EXPORT sCommManager{
public:
    static SerialDeviceManager* getCommManager();
private:
    SerialDeviceManager mCommManager;
    sCommManager();
    ~sCommManager();
    sCommManager(const sCommManager&);
    sCommManager& operator=(const sCommManager&);
};

#endif // SERIALDEVICEMANAGER_H
