#QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

#CONFIG += c++11

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    uiManualMode/uiDataAcqManual.cpp \
    uiManualMode/uiDeviceControl.cpp \
    uiManualMode/uiDeviceControl/uiDeviceControlLC.cpp \
    uiManualMode/uiDeviceControl/uiDeviceControlMASS.cpp \
    uiManualMode/uiMassTuneManual.cpp \
    uiManualMode/uiOptimizationManual.cpp

HEADERS += \
    uiManualMode/uiDataAcqManual.h \
    uiManualMode/uiDeviceControl.h \
    uiManualMode/uiDeviceControl/uiDeviceControlLC.h \
    uiManualMode/uiDeviceControl/uiDeviceControlMASS.h \
    uiManualMode/uiMassTuneManual.h \
    uiManualMode/uiOptimizationManual.h

FORMS += \
    uiManualMode/uiDataAcqManual.ui \
    uiManualMode/uiDeviceControl.ui \
    uiManualMode/uiDeviceControl/uiDeviceControlLC.ui \
    uiManualMode/uiDeviceControl/uiDeviceControlMASS.ui \
    uiManualMode/uiMassTuneManual.ui \
    uiManualMode/uiOptimizationManual.ui

include($$PWD/uiTripleQ_HZH.pri)

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
