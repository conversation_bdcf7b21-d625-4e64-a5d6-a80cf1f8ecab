#include "uiInitMainWindow.h"

#define mUi pMainWindow->ui

uiInitMainWindow::uiInitMainWindow(uiMainWindow *p) :
    QObject(nullptr),
pMainWindow(p)
{}

uiInitMainWindow::~uiInitMainWindow()
{}

void uiInitMainWindow::initUI()
{
    pMainWindow->mState->hide();
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mSystem);
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mManualMode);
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mBatch);
    connect(pMainWindow->mBatch, SIGNAL(sSubmit()), pMainWindow, SLOT(onSubmit()));
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mQueue);
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mTune);
    mUi.UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(pMainWindow->mSingleAcquisition);
    createMenuBar();
    createToolBar();
}

void uiInitMainWindow::createMenuBar()
{
    mUi.UI_MPB_LIST_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_LIST_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     tr("在途队列"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_LIST_MAINW->setTextSize(0,0,50);
    connect(mUi.UI_MPB_LIST_MAINW,SIGNAL(ButtonClicked()),this,SLOT(onUI_MPB_LIST_MAINW()));

    mUi.UI_MPB_CREATE_LIST_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_CREATE_LIST_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            tr("新建队列"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_CREATE_LIST_MAINW->setTextSize(0,0,50);
    connect(mUi.UI_MPB_CREATE_LIST_MAINW,SIGNAL(ButtonClicked()),this,SLOT(onUI_MPB_MENU_CREATE_LIST_MAINW()));

    mUi.UI_MPB_MASS_METHOD_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MASS_METHOD_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            tr("质谱方法"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_MASS_METHOD_MAINW->setTextSize(0,0,50);
    //connect(mSystemButton,SIGNAL(ButtonClicked()),this,SLOT(on_UI_PB_SNAPSHOT_clicked()));

    mUi.UI_MPB_LIQUID_METHOD_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_LIQUID_METHOD_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            tr("液相方法"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_LIQUID_METHOD_MAINW->setTextSize(0,0,50);
    //connect(mSystemButton,SIGNAL(ButtonClicked()),this,SLOT(on_UI_PB_SNAPSHOT_clicked()));

    mUi.UI_MPB_DATA_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_DATA_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     tr("数据浏览"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_DATA_MAINW->setTextSize(0,0,50);
    //connect(mSystemButton,SIGNAL(ButtonClicked()),this,SLOT(on_UI_PB_SNAPSHOT_clicked()));

    mUi.UI_MPB_TUNE_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_TUNE_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     QPixmap(":/Button/picture/Correct.png"),
                                     tr("质谱调谐"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_TUNE_MAINW->setTextSize(0,0,50);
    //connect(mSystemButton,SIGNAL(ButtonClicked()),this,SLOT(on_UI_PB_SNAPSHOT_clicked()));

    mUi.UI_MPB_SYSTEM_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_SYSTEM_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                       QPixmap(":/Button/picture/Correct.png"),
                                       QPixmap(":/Button/picture/Correct.png"),
                                       QPixmap(":/Button/picture/Correct.png"),
                                       tr("系统设置"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_SYSTEM_MAINW->setTextSize(0,0,50);
    connect(mUi.UI_MPB_SYSTEM_MAINW,SIGNAL(ButtonClicked()),this,SLOT(onUI_MPB_MENU_SYSTEM_MAINW()));

    mUi.UI_MPB_SIGNAL_SCAN_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_SIGNAL_SCAN_MAINW->setPicture(QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            QPixmap(":/Button/picture/Correct.png"),
                                            tr("单次采集"));
    //mSystemButton->setFontSize(9);
    mUi.UI_MPB_SIGNAL_SCAN_MAINW->setTextSize(0,0,50);
    connect(mUi.UI_MPB_SIGNAL_SCAN_MAINW,SIGNAL(ButtonClicked()),this,SLOT(onUI_MPB_MENU_SINAL_SCAN_MAINW()));

}

void uiInitMainWindow::createToolBar()
{
    mWindowMenu= new uiWindowMenu(pMainWindow);
    mUi.UI_LAYOUT_MENUBAR_MAINW->addWidget(mWindowMenu);

    mUi.UI_MPB_MENU_MAIN_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_MAIN_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("返回主页"));
    mUi.UI_MPB_MENU_MAIN_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_MAIN_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_MAIN_MAINW()));

    mUi.UI_MPB_MENU_SYSTEM_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_SYSTEM_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("系统设置"));
    mUi.UI_MPB_MENU_SYSTEM_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_SYSTEM_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_SYSTEM_MAINW()));

    mUi.UI_MPB_MENU_MANUAL_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_MANUAL_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("手动模式"));
    mUi.UI_MPB_MENU_MANUAL_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_MANUAL_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_MANUAL_MAINW()));

    mUi.UI_MPB_MENU_LIST_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_LIST_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("在途队列"));
    mUi.UI_MPB_MENU_LIST_MAINW->setTextSize(64,64,40);
    //connect(mMainWindowButton,SIGNAL(ButtonClicked()), this, SLOT(onEI_Clicked()));

    mUi.UI_MPB_MENU_CREATE_LIST_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_CREATE_LIST_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("新建队列"));
    mUi.UI_MPB_MENU_CREATE_LIST_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_CREATE_LIST_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_CREATE_LIST_MAINW()));

    mUi.UI_MPB_MENU_MASS_METHOD_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_MASS_METHOD_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("质谱方法"));
    mUi.UI_MPB_MENU_MASS_METHOD_MAINW->setTextSize(64,64,40);
    //connect(mMainWindowButton,SIGNAL(ButtonClicked()), this, SLOT(onEI_Clicked()));

    mUi.UI_MPB_MENU_LIQUID_METHOD_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_LIQUID_METHOD_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("液相方法"));
    mUi.UI_MPB_MENU_LIQUID_METHOD_MAINW->setTextSize(64,64,40);
    //connect(mMainWindowButton,SIGNAL(ButtonClicked()), this, SLOT(onEI_Clicked()));

    mUi.UI_MPB_MENU_DATA_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_DATA_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("数据浏览"));
    mUi.UI_MPB_MENU_DATA_MAINW->setTextSize(64,64,40);
    //connect(mMainWindowButton,SIGNAL(ButtonClicked()), this, SLOT(onEI_Clicked()));

    mUi.UI_MPB_MENU_TUNE_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_TUNE_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("质谱调谐"));
    mUi.UI_MPB_MENU_TUNE_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_TUNE_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_TUNE_MAINW()));

    mUi.UI_MPB_MENU_SINAL_SCAN_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_SINAL_SCAN_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("单次采集"));
    mUi.UI_MPB_MENU_SINAL_SCAN_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_SINAL_SCAN_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_SINAL_SCAN_MAINW()));

    mUi.UI_MPB_MENU_STATE_MAINW->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    mUi.UI_MPB_MENU_STATE_MAINW->setPicture(QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                QPixmap(":/Menu/picture/zhenkong_48_on.png"),
                                                tr("仪器状态"));
    mUi.UI_MPB_MENU_STATE_MAINW->setTextSize(64,64,40);
    connect(mUi.UI_MPB_MENU_STATE_MAINW,SIGNAL(ButtonClicked()), this, SLOT(onUI_MPB_MENU_STATE_MAINW()));
}

void uiInitMainWindow::onUI_MPB_MENU_MAIN_MAINW()
{
    pMainWindow->setCurrentWindow(mUi.UI_W_MAINWIDOW);
}

void uiInitMainWindow::onUI_MPB_MENU_SYSTEM_MAINW()
{
    if(pMainWindow->mSystem)
        pMainWindow->setCurrentWindow(pMainWindow->mSystem);
}

void uiInitMainWindow::onUI_MPB_MENU_MANUAL_MAINW()
{
    if(pMainWindow->mManualMode)
        pMainWindow->setCurrentWindow(pMainWindow->mManualMode);
}

void uiInitMainWindow::onUI_MPB_LIST_MAINW()
{
    if(!pMainWindow->mQueue)
        return;
    pMainWindow->setCurrentWindow(pMainWindow->mQueue);
}

void uiInitMainWindow::onUI_MPB_MENU_CREATE_LIST_MAINW()
{
    if(pMainWindow->mBatch)
        pMainWindow->setCurrentWindow(pMainWindow->mBatch);
}

void uiInitMainWindow::onUI_MPB_MENU_SINAL_SCAN_MAINW()
{
    if(pMainWindow->mSingleAcquisition)
        pMainWindow->setCurrentWindow(pMainWindow->mSingleAcquisition);
}

void uiInitMainWindow::onUI_MPB_MENU_TUNE_MAINW()
{
    if(pMainWindow->mTune)
        pMainWindow->setCurrentWindow(pMainWindow->mTune);
}

void uiInitMainWindow::onUI_MPB_MENU_STATE_MAINW()
{
    /*if(pMainWindow->mListWidgetForLog)
        pMainWindow->mListWidgetForLog->show();*/
    if(pMainWindow->mState)
        pMainWindow->mState->show();
}


