#include "uiDeviceControlMASS.h"
#include "ui_uiDeviceControlMASS.h"

uiDeviceControlMASS::uiDeviceControlMASS(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::uiDeviceControlMASS)
{
    ui->setupUi(this);
    mEditMRM= new uiEditMRM(this);
    ui->UI_SW_DCM->addWidget(mEditMRM);
    ui->UI_SW_DCM->setCurrentIndex(0);
}

uiDeviceControlMASS::~uiDeviceControlMASS()
{
    delete ui;
}

void uiDeviceControlMASS::on_UI_RECIPE_LIST_DCM_itemClicked(QListWidgetItem *item)
{
    int currentRow= ui->UI_RECIPE_LIST_DCM->row(item);
    QString key= mListExpenment[currentRow].first;
    QString str= mListExpenment[currentRow].second;
    if(key== "Scheduled MRM"){
        mEditMRM->setParam(str);
        ui->UI_SW_DCM->setCurrentIndex(0);
    }
}

void uiDeviceControlMASS::on_UI_RECIPE_ADD_DCM_clicked()
{
    QString key= ui->UI_RECIPE_CB_DCM->currentText();
    QString str= mEditMRM->getParam();
    mListExpenment.append(QPair(key, str));
    ui->UI_RECIPE_LIST_DCM->addItem(key);
}
