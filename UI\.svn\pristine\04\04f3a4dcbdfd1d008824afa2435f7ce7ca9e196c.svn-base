/* specfunc/gsl_sf_zeta.h
 * 
 * Copyright (C) 1996, 1997, 1998, 1999, 2000, 2004 <PERSON>
 * 
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 */

/* Author:  <PERSON><PERSON> */

#ifndef __GSL_SF_ZETA_H__
#define __GSL_SF_ZETA_H__

#include <gsl/gsl_sf_result.h>
#include <gsl/gsl_types.h>

#undef __BEGIN_DECLS
#undef __END_DECLS
#ifdef __cplusplus
# define __BEGIN_DECLS extern "C" {
# define __END_DECLS }
#else
# define __BEGIN_DECLS /* empty */
# define __END_DECLS /* empty */
#endif

__BEGIN_DECLS


/* Riemann Zeta Function
 * zeta(n) = Sum[ k^(-n), {k,1,Infinity} ]
 *
 * n=integer, n != 1
 * exceptions: GSL_EDOM, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_zeta_int_e(const int n, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_zeta_int(const int n);


/* Riemann Zeta Function
 * zeta(x) = Sum[ k^(-s), {k,1,Infinity} ], s != 1.0
 *
 * s != 1.0
 * exceptions: GSL_EDOM, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_zeta_e(const double s, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_zeta(const double s);


/* Riemann Zeta Function minus 1
 *   useful for evaluating the fractional part
 *   of Riemann zeta for large argument
 *
 * s != 1.0
 * exceptions: GSL_EDOM, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_zetam1_e(const double s, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_zetam1(const double s);


/* Riemann Zeta Function minus 1 for integer arg
 *   useful for evaluating the fractional part
 *   of Riemann zeta for large argument
 *
 * s != 1.0
 * exceptions: GSL_EDOM, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_zetam1_int_e(const int s, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_zetam1_int(const int s);


/* Hurwitz Zeta Function
 * zeta(s,q) = Sum[ (k+q)^(-s), {k,0,Infinity} ]
 *
 * s > 1.0, q > 0.0
 * exceptions: GSL_EDOM, GSL_EUNDRFLW, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_hzeta_e(const double s, const double q, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_hzeta(const double s, const double q);


/* Eta Function
 * eta(n) = (1-2^(1-n)) zeta(n)
 *
 * exceptions: GSL_EUNDRFLW, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_eta_int_e(int n, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_eta_int(const int n);


/* Eta Function
 * eta(s) = (1-2^(1-s)) zeta(s)
 *
 * exceptions: GSL_EUNDRFLW, GSL_EOVRFLW
 */
GSL_EXPORT int gsl_sf_eta_e(const double s, gsl_sf_result * result);
GSL_EXPORT double gsl_sf_eta(const double s);


__END_DECLS

#endif /* __GSL_SF_ZETA_H__ */
