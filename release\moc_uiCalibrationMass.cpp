/****************************************************************************
** Meta object code from reading C++ file 'uiCalibrationMass.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiTune/uiCalibrationMass.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiCalibrationMass.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiCalibrationMass_t {
    QByteArrayData data[11];
    char stringdata0[218];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiCalibrationMass_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiCalibrationMass_t qt_meta_stringdata_uiCalibrationMass = {
    {
QT_MOC_LITERAL(0, 0, 17), // "uiCalibrationMass"
QT_MOC_LITERAL(1, 18, 10), // "sStartScan"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 18), // "uiCalibrationView*"
QT_MOC_LITERAL(4, 49, 9), // "sStopScan"
QT_MOC_LITERAL(5, 59, 25), // "on_UI_PB_SAVE_CAL_clicked"
QT_MOC_LITERAL(6, 85, 25), // "on_UI_PB_LOAD_CAL_clicked"
QT_MOC_LITERAL(7, 111, 27), // "on_UI_PB_SAVETO_CAL_clicked"
QT_MOC_LITERAL(8, 139, 26), // "on_UI_PB_APPLY_CAL_clicked"
QT_MOC_LITERAL(9, 166, 25), // "on_UI_PB_SCAN_CAL_clicked"
QT_MOC_LITERAL(10, 192, 25) // "on_UI_PB_STOP_CAL_clicked"

    },
    "uiCalibrationMass\0sStartScan\0\0"
    "uiCalibrationView*\0sStopScan\0"
    "on_UI_PB_SAVE_CAL_clicked\0"
    "on_UI_PB_LOAD_CAL_clicked\0"
    "on_UI_PB_SAVETO_CAL_clicked\0"
    "on_UI_PB_APPLY_CAL_clicked\0"
    "on_UI_PB_SCAN_CAL_clicked\0"
    "on_UI_PB_STOP_CAL_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiCalibrationMass[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       4,    1,   57,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   60,    2, 0x08 /* Private */,
       6,    0,   61,    2, 0x08 /* Private */,
       7,    0,   62,    2, 0x08 /* Private */,
       8,    0,   63,    2, 0x08 /* Private */,
       9,    0,   64,    2, 0x08 /* Private */,
      10,    0,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    2,
    QMetaType::Void, 0x80000000 | 3,    2,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiCalibrationMass::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiCalibrationMass *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sStartScan((*reinterpret_cast< uiCalibrationView*(*)>(_a[1]))); break;
        case 1: _t->sStopScan((*reinterpret_cast< uiCalibrationView*(*)>(_a[1]))); break;
        case 2: _t->on_UI_PB_SAVE_CAL_clicked(); break;
        case 3: _t->on_UI_PB_LOAD_CAL_clicked(); break;
        case 4: _t->on_UI_PB_SAVETO_CAL_clicked(); break;
        case 5: _t->on_UI_PB_APPLY_CAL_clicked(); break;
        case 6: _t->on_UI_PB_SCAN_CAL_clicked(); break;
        case 7: _t->on_UI_PB_STOP_CAL_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< uiCalibrationView* >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< uiCalibrationView* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiCalibrationMass::*)(uiCalibrationView * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiCalibrationMass::sStartScan)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (uiCalibrationMass::*)(uiCalibrationView * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiCalibrationMass::sStopScan)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiCalibrationMass::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiCalibrationMass.data,
    qt_meta_data_uiCalibrationMass,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiCalibrationMass::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiCalibrationMass::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiCalibrationMass.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiCalibrationMass::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void uiCalibrationMass::sStartScan(uiCalibrationView * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void uiCalibrationMass::sStopScan(uiCalibrationView * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
