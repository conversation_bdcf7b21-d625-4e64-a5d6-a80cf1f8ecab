﻿#include "MSParameterParser.h"
#include "sMethod/xml_attr_tag.h"
#include <QFile>

using namespace LabSolutionsRW;

MSParameterParser::MSParameterParser(const QString& xmlFile, QObject *parent)
    : QObject(parent),m_domEvents(nullptr)
{
    m_domEvents = new QList<QDomElement>();
    m_domDocument = new QDomDocument();
    if (!xmlFile.isEmpty())
        setFile(xmlFile);
}

MSParameterParser::~MSParameterParser()
{
    delete m_domEvents;
    delete m_domDocument;
    qDeleteAll(m_massEventList);
}

/**
 * @brief ParamrParser::setFile
 * 设置要解析的xml参数文件，
 * 如果文件合法则会产生新的Event列表，可以通过getEvents()函数获取新的信息
 * @param file
 * @return
 */
bool MSParameterParser::setFile(const QString &xmlFile)
{
    QFile file(xmlFile);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << __func__ << __LINE__ << file.errorString();
        return false;
    }

    if (!parseFile(&file))
        return false;
    parseMassEventList();
    file.close();
    //文件解析成功
    m_xmlFileName = xmlFile;
    return true;
}
/**
 * @brief MSParameterParser::saveMSParameter
 * 保存修改的参数
 */
void MSParameterParser::saveMSParameter()
{
    if (m_xmlFileName.isEmpty())
        return;
    QFile file(m_xmlFileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)){
        qDebug() << __FUNCTION__ << __LINE__ << file.errorString();
        return;
    }
    QTextStream ts(&file);
    m_domDocument->save(ts, 4);
    file.close();
}
/**
 * @brief MSParameterParser::readSegment
 * 读取Segment中的event
 * @param element
 */
void MSParameterParser::readSegment(const QDomElement &element)
{
    QDomElement eventElement = element.firstChildElement(tagEvent());
    while (!eventElement.isNull()) {
        m_domEvents->append(eventElement);
        eventElement = eventElement.nextSiblingElement(tagEvent());
    }
}
/**
 * @brief MSParameterParser::parseMassEventList
 * 从m_domEvents中生成m_massEventList
 */
void MSParameterParser::parseMassEventList()
{
    qDeleteAll(m_massEventList);
    m_massEventList.clear();
    QString acqType;
    MassEvent* massEvent = nullptr;
    for (auto &domElement : *m_domEvents){
        QDomElement ele = domElement.firstChildElement(tagAcqMode());
        if (ele.isNull()){
            qWarning() << "The domElement have no AcqMode element";
            continue;
        }
        int index = domElement.attribute(attrNo()).toInt();
        acqType = ele.text();
        if (acqType == "Q1Scan" || acqType == "Q3Scan")
            massEvent = readScanEvent(domElement);
        else if (acqType == "Q1Sim" || acqType == "Q3Sim")
            massEvent = readSimEvent(domElement);
        else if (acqType == "MRM")
            massEvent = readMRMEvent(domElement);
        else if (acqType == "PrecursorIonScan" ||
                 acqType == "ProductIonScan" ||
                 acqType == "NeutralLosScan")
            massEvent = readIonScanEvent(domElement);

        if (massEvent){
            massEvent->index = index;
            m_massEventList.append(massEvent);
        }
    }
}

MassEvent *MSParameterParser::readSimEvent(const QDomElement &ele)
{
    auto pMsEvt = new MassEvent;
    QString acqType = ele.firstChildElement(tagAcqMode()).text();
    QString startTime = ele.firstChildElement(tagStartTime()).text();
    QString endTime = ele.firstChildElement(tagEndTime()).text();
    QString polarity = ele.firstChildElement(tagPolarity()).text();
    QString eventTime = ele.firstChildElement(tagEventTime()).text();

    pMsEvt->type = acqType == "Q1Sim" ? Q1SIM : Q3SIM;
    pMsEvt->startTime = startTime.toUInt();
    pMsEvt->endTime = endTime.toUInt();
    pMsEvt->polarity = polarity == "Positive" ? 1 : -1;
    pMsEvt->eventTime = static_cast<uint>(eventTime.toFloat() * 1000);

    QDomElement chEle = ele.firstChildElement(tagChannel());
    while (!chEle.isNull()) {
        auto pMsCh = new MassChannel;
        QString firstMz = chEle.firstChildElement(tagStartMz()).text();
        QString pauseTime = chEle.firstChildElement(tagPauseTime()).text();
        QString dwellTime = chEle.firstChildElement(tagDwellTime()).text();

        pMsCh->firstMz = firstMz.toFloat();
        pMsCh->prepareTime = pauseTime.toUInt();
        pMsCh->pureTime = dwellTime.toUInt();
        pMsEvt->addChannel(pMsCh);

        chEle = chEle.nextSiblingElement(tagChannel());
    }

    return pMsEvt;
}

MassEvent *MSParameterParser::readScanEvent(const QDomElement &ele)
{
    auto pMsEvt = new MassEvent;
    QString acqType = ele.firstChildElement(tagAcqMode()).text();
    QString startTime = ele.firstChildElement(tagStartTime()).text();
    QString endTime = ele.firstChildElement(tagEndTime()).text();
    QString polarity = ele.firstChildElement(tagPolarity()).text();
    QString eventTime = ele.firstChildElement(tagEventTime()).text();

    pMsEvt->type = acqType == "Q3Scan" ? Q3Scan : Q1Scan;
    pMsEvt->startTime = startTime.toUInt();
    pMsEvt->endTime = endTime.toUInt();
    pMsEvt->polarity = polarity == "Positive" ? 1 : -1;
    pMsEvt->eventTime = static_cast<uint>(eventTime.toFloat() * 1000);

    QDomElement chEle = ele.firstChildElement(tagChannel());

    auto pMsCh = new MassChannel;
    QString firstMz = chEle.firstChildElement(tagStartMz()).text();
    QString endMz = chEle.firstChildElement(tagEndMz()).text();

    pMsCh->firstMz = firstMz.toFloat();
    pMsCh->secondMz = endMz.toFloat();
    pMsCh->prepareTime = 5;
    pMsCh->pureTime = pMsEvt->eventTime - pMsCh->prepareTime;
    pMsEvt->addChannel(pMsCh);

    return pMsEvt;
}

MassEvent *MSParameterParser::readMRMEvent(const QDomElement &ele)
{
    auto pMsEvt = new MassEvent;
    QString acqType = ele.firstChildElement(tagAcqMode()).text();
    QString startTime = ele.firstChildElement(tagStartTime()).text();
    QString endTime = ele.firstChildElement(tagEndTime()).text();
    QString polarity = ele.firstChildElement(tagPolarity()).text();
    QString eventTime = ele.firstChildElement(tagEventTime()).text();

    pMsEvt->type = MRM;
    pMsEvt->startTime = startTime.toUInt();
    pMsEvt->endTime = endTime.toUInt();
    pMsEvt->polarity = polarity == "Positive" ? 1 : -1;
    pMsEvt->eventTime = static_cast<uint>(eventTime.toFloat() * 1000);

    QDomElement chEle = ele.firstChildElement(tagChannel());
    while (!chEle.isNull()) {
        auto pMsCh = new MassChannel;
        QString firstMz = chEle.firstChildElement(tagStartMz()).text();
        QString endMz = chEle.firstChildElement(tagEndMz()).text();
        QString pauseTime = chEle.firstChildElement(tagPauseTime()).text();
        QString dwellTime = chEle.firstChildElement(tagDwellTime()).text();
        QString ce = chEle.firstChildElement(tagCE()).text();

        pMsCh->firstMz = firstMz.toFloat();
        pMsCh->secondMz = endMz.toFloat();
        pMsCh->prepareTime = pauseTime.toUInt();
        pMsCh->pureTime = dwellTime.toUInt();
        pMsCh->ce = ce.toFloat();
        pMsEvt->addChannel(pMsCh);

        chEle = chEle.nextSiblingElement(tagChannel());
    }

    return pMsEvt;
}

MassEvent *MSParameterParser::readIonScanEvent(const QDomElement &ele)
{
    auto pMsEvt = new MassEvent;
    QString acqType = ele.firstChildElement(tagAcqMode()).text();
    QString startTime = ele.firstChildElement(tagStartTime()).text();
    QString endTime = ele.firstChildElement(tagEndTime()).text();
    QString polarity = ele.firstChildElement(tagPolarity()).text();
    QString eventTime = ele.firstChildElement(tagEventTime()).text();
    if (acqType == "PrecursorIonScan")
        pMsEvt->type = PrecursorIonScan;
    else if (acqType == "ProductIonScan")
        pMsEvt->type = ProductIonScan;
    else
        pMsEvt->type = NeutralLossScan;
    pMsEvt->startTime = startTime.toUInt();
    pMsEvt->endTime = endTime.toUInt();
    pMsEvt->polarity = polarity == "Positive" ? 1 : -1;
    pMsEvt->eventTime = static_cast<uint>(eventTime.toFloat() * 1000);

    QDomElement chEle = ele.firstChildElement(tagChannel());

    auto pMsCh = new MassChannel;
    QString firstMz = chEle.firstChildElement(tagStartMz()).text();
    QString endMz = chEle.firstChildElement(tagEndMz()).text();
    QString acqMz = chEle.firstChildElement(tagAcqModeMz()).text();
    QString ce = chEle.firstChildElement(tagCE()).text();

    pMsCh->firstMz = firstMz.toFloat();
    pMsCh->secondMz = endMz.toFloat();
    pMsCh->acqModeMz = acqMz.toFloat();
    pMsCh->ce = ce.toFloat();
    pMsCh->prepareTime = 5;
    pMsCh->pureTime = pMsEvt->eventTime - pMsCh->prepareTime;

    pMsEvt->addChannel(pMsCh);


    return pMsEvt;
}

QList<MassEvent *> MSParameterParser::getMassEventList() const
{
    return m_massEventList;
}
/**
 * @brief MSParameterParser::getXmlFileName
 * 获取当前解析的xml文件的名称
 * @return
 */
QString MSParameterParser::getXmlFileName() const
{
    return m_xmlFileName;
}
/**
 * @brief MSParameterParser::parseFile
 * 如果解析成功，将Event的DOM信息填充到m_Events列表。可通过get函数获取
 * @param device
 * @return true： success  ， false： fail
 */
bool MSParameterParser::parseFile(QFile *device)
{
    //判断文件是否为xml文件
    QString errorStr;
    int errorLine;
    int errorColumn;
    if (!m_domDocument->setContent(device, true, &errorStr, &errorLine,&errorColumn)) {
        qDebug() << "file:" << device->fileName() << "row:" << errorLine << "col:" << errorColumn << "\nerror:" << errorStr;
        return false;
    }
    //判断文件是否为合法的MS参数
    QDomElement root = m_domDocument->documentElement();
    if (root.tagName() != tagMSParameter())
        return false;

    //文件合法
    m_rootElement = root;
    m_domEvents->clear();
    //文件解析
    QDomElement rootElement = root.firstChildElement();
    while (!rootElement.isNull()) {
        QString name = rootElement.tagName();
        if (name == tagSegment())
            readSegment(rootElement);
        else
            qDebug() << "new tag:" << name;
        //elseif (name == ...)
        // ...
        rootElement = rootElement.nextSiblingElement();
    }
    return true;
}

