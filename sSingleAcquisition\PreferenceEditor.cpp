﻿#include "PreferenceEditor.h"

#include <QFileDialog>
#include <QSettings>
#include <QMessageBox>
#include <QLineEdit>

//#include "PreferenceManager.h"
//#include "I_InsertNotify.h"
//#include "PublicDefine.h"
//#include "parmeter_structs.h"
#include "ui_PreferenceEditor.h"
//#include "I_GlobalVariable.h"

//****** read LabSolutions method file
#include "MSParameterParser.h"
#include "IonScanParamEditor.h"
#include "MRMParameterEditor.h"
#include "Q13SIMParamEditor.h"
#include "Q13ScanParamEditor.h"

#include "sMethod/xml_attr_tag.h"
//******

//using namespace uma_project;

PreferenceEditor::PreferenceEditor(QWidget *parent/*PreferenceManager* manager*/):
    QWidget(parent),
    ui(new Ui::PreferenceEditor)
    //, m_prfsManager(manager)
    , m_ParameterWidgetPtr(nullptr)
{
    ui->setupUi(this);
    initialize();
    initConfig();
    signalSlotManager();
    ui->widget_advance->hide();
    ui->editor_GasSpeed->hide();/// change with resolution
}

PreferenceEditor::~PreferenceEditor() { delete ui; }

//void PreferenceEditor::setPreferenceManager(PreferenceManager* manager)
//{
//    m_prfsManager = manager;
//}

//void PreferenceEditor::setNormalParam(const ParamNormal& param)
//{
//    if (param == m_param)
//        return;
//    updateNormalParam(param);
//    m_param = param; // Do not switch these two lines, for compare some variable
//}

void PreferenceEditor::on_pushButton_loadMethod_clicked()
{
    QSettings settings;
    QString fileName = "";//settings.value(uma_project::getRecentMethodFile(), QDir::homePath())
                           //.toString();
    QString methodFile = QFileDialog::getOpenFileName(
        this, tr("Select Method"), fileName, tr("method file(*.lcm)"));

    if (methodFile.isEmpty())
        return;
//    if (methodFile != fileName)
//        settings.setValue(uma_project::getRecentMethodFile(), methodFile);

    updateMethodFile(methodFile, true);
}

void PreferenceEditor::on_pushButton_advance_clicked()
{
    if (ui->widget_advance->isHidden()) {
        ui->widget_advance->show();
        ui->pushButton_advance->setText(tr("Fold..."));
    } else {
        ui->widget_advance->hide();
        ui->pushButton_advance->setText(tr("Advance..."));
    }
}

//void PreferenceEditor::onAnalysisModeClicked(QAbstractButton* button)
//{
//    AnalysisMode mode = uma_project::FilterScanMode;
//    if (button == ui->radioButtonFilterScan)
//        mode = uma_project::FilterScanMode;
//    else if (button == ui->radioButtonSIM)
//        mode = uma_project::FilterSIMMode;
//    else if (button == ui->radioButtonTrapScan)
//        mode = uma_project::TrapScanMode;
//    else if (button == ui->radioButtonThrough)
//        mode = uma_project::ThroughMode;

//    if (mode == m_param.analysisMode)
//        return;

//    updateAnalysisMode(mode);
//}

//void PreferenceEditor::onTextEdited()
//{
//    m_param.pnMode = ui->comboBox_PNMode->currentIndex() == 0 ? Positive : Negative;
//    m_param.scanTime_ms = ui->editor_ScanPeroid->value();
//    m_param.E_Start_v_mm = ui->editor_E_Start->value();
//    m_param.accmulateTime_us = ui->editor_AccPeriod->value();
//    m_param.mobilityWindow_v_mm = ui->editor_DE->value();
//    m_param.E_End_v_mm = ui->editor_E_End->value();
//    m_param.gasSpeed_m_s = ui->editor_GasSpeed->value();

//    emit normalParamEdited(m_param);
//}

void PreferenceEditor::onMzVolParamModified()
{
    auto editor = qobject_cast<ParameterEditor*>(sender());
    if (editor)
        editor->setModified(true);
}

/*!
 * \brief PreferenceEditor::initialize
 * 初始化ui布局与显示
 */
void PreferenceEditor::initialize()
{
    // normal
    ui->editor_E_Start->setLabel("E1 Start");
    ui->editor_E_End->setLabel("E1 End");
    ui->editor_ScanPeroid->setLabel("Scan Period");
    ui->editor_AccPeriod->setLabel("Accumulate Period");
    ui->editor_GasSpeed->setLabel("Gas Speed");
    ui->editor_DE->setLabel("Mobility Window(DE)");

    ui->editor_AccPeriod->setUnit("ms");
    ui->editor_DE->setUnit("V/mm");
    ui->editor_E_End->setUnit("V/mm");
    ui->editor_E_Start->setUnit("V/mm");
    ui->editor_GasSpeed->setUnit("L/min(N2)");//<p style='line-height:4px'>L(N<span style= 'vertical-align:sub;'>2</span>)/min</p>
    ui->editor_ScanPeroid->setUnit("ms");

    // analysis
    m_analysisModeGroup = new QButtonGroup(this);
    m_analysisModeGroup->addButton(ui->radioButtonFilterScan);
    m_analysisModeGroup->addButton(ui->radioButtonSIM);
    m_analysisModeGroup->addButton(ui->radioButtonTrapScan);
    m_analysisModeGroup->addButton(ui->radioButtonThrough);
    // advance
    ui->currentMZ->setLabel(tr("current mz:"));
    ui->currentMZ->setUnit("mz");

    // mz preference
    QWidget* m_pInputVolatageWidget = new QWidget(ui->label_pic);
    QHBoxLayout* layout = new QHBoxLayout(m_pInputVolatageWidget);
    layout->setMargin(0);
    layout->setSpacing(5);
    for (int i = 0; i < 12; ++i) {
        m_pValatageEditor[i] = new ParameterEditor(ui->label_pic);
        m_pValatageEditor[i]->setUnit("V");
        layout->addWidget(m_pValatageEditor[i]);
    }
    m_pInputVolatageWidget->setGeometry(43, 478, 512, 18);

    for (int i = 0; i < 3; ++i) {
        m_pRfEditor[i] = new ParameterEditor(ui->label_pic);
        m_pRfEditor[i]->setUnit("V");
        m_pRfEditor[i]->setLabel("RF:");
    }

    m_pRfEditor[0]->setGeometry(128, 226, 90, 18);
    m_pRfEditor[1]->setGeometry(260, 20, 90, 18);
    m_pRfEditor[2]->setGeometry(390, 226, 90, 18);
}
/*!
 * \brief PreferenceEditor::initConfig
 * 输入限制
 */
void PreferenceEditor::initConfig()
{
    //I_GlobalVariable *instance = I_GlobalVariable::getInstance();
    //ui->editor_E_Start->setRange(instance->voltageRange().first, instance->voltageRange().second);
    //ui->editor_E_End->setRange(instance->voltageRange().first, instance->voltageRange().second);
    //ui->editor_GasSpeed->setRange(instance->gasSpeedRange().first, instance->gasSpeedRange().second);
    //firmware setting
    //auto type = I_GlobalVariable::getInstance()->instrumentType();
    //if (type == InstrumentType::QQQ)
    //    ui->widget_firmware_tof->hide();
    //else
        ui->widget_firmware_QQQ->hide();
}

void PreferenceEditor::signalSlotManager()
{
    connect(ui->editor_E_Start,    SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->editor_E_End,      SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->editor_ScanPeroid, SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->editor_AccPeriod,  SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->editor_DE,         SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->editor_GasSpeed,   SIGNAL(textEdited()), this, SLOT(onTextEdited()));
    connect(ui->comboBox_PNMode,   SIGNAL(currentIndexChanged(int)), this, SLOT(onTextEdited()));

    //mz voltages
    for (int i = 0; i < 12; ++i)
        connect(m_pValatageEditor[i], &ParameterEditor::textEdited, this, &PreferenceEditor::onMzVolParamModified);
    for (int i = 0; i < 3; ++i)
        connect(m_pRfEditor[i], &ParameterEditor::textEdited, this, &PreferenceEditor::onMzVolParamModified);

    connect(m_analysisModeGroup, SIGNAL(buttonClicked(QAbstractButton*)), this,
        SLOT(onAnalysisModeClicked(QAbstractButton*)));

//    connect(I_GlobalVariable::getInstance(), &I_GlobalVariable::voltageRangeChanged,
//            this, &PreferenceEditor::on_voltageRangeChanged);
//    connect(I_GlobalVariable::getInstance(), &I_GlobalVariable::gasSpeedRangeChanged,
//            this, &PreferenceEditor::on_gasSpeedRangeChanged);
//    connect(&m_fileWatcher, &QFileSystemWatcher::fileChanged,[&]{
//        auto ret = QMessageBox::question(this, tr("Method Changed"), tr("Method file is changed, reload now?"),
//                                         QMessageBox::Ok | QMessageBox::Cancel);
//        if (ret == QMessageBox::Cancel)
//            return ;
//        updateMethodFile(m_param.methodFile, true);
//    });
//    connect(ui->widget_firmware_QQQ,SIGNAL(detectorSwitch(bool)),
//            m_prfsManager, SLOT(onQqQDetectorSwitch(bool)));
}

//void PreferenceEditor::initMzVolParamStyle()
//{
//    for (int i = 0; i < 12; ++i)
//        m_pValatageEditor[i]->setModified(false);
//    for (int i = 0; i < 3; ++i)
//        m_pRfEditor[i]->setModified(false);
//}
/*!
 * \brief PreferenceEditor::updateNormalParam
 * 根据m_param的参数更新页面显示内容
 */
//void PreferenceEditor::updateNormalParam(const ParamNormal& param)
//{
//    blockSignals(true);
//    updateAnalysisMode(param.analysisMode);
//    updateMethodFile(param.methodFile);
//    updateResolution(param.resolution);

//    int index = param.pnMode == Positive ? 0 : 1;
//    ui->comboBox_PNMode->setCurrentIndex(index);
//    ui->editor_ScanPeroid->setValue(param.scanTime_ms);
//    ui->editor_E_Start->setValue(param.E_Start_v_mm);
//    ui->editor_AccPeriod->setValue(param.accmulateTime_us);
//    ui->editor_DE->setValue(param.mobilityWindow_v_mm);
//    ui->editor_E_End->setValue(param.E_End_v_mm);
//    ui->editor_GasSpeed->setValue(param.gasSpeed_m_s);

//    blockSignals(false);
//}

/*!
 * \brief PreferenceEditor::updateAnalysisMode
 * 根据mode更新对应的显示控件
 * \param mode
 */
//void PreferenceEditor::updateAnalysisMode(AnalysisMode mode)
//{
//    if (mode == m_param.analysisMode)
//        return;
//    // showAllUmaParameter
//    {
//        ui->editor_E_Start->setLabel("E1 Start");
//        ui->editor_E_End->setLabel("E1 End");
//        ui->editor_ScanPeroid->setLabel("Scan Period");

//        ui->editor_AccPeriod->show();
//        ui->editor_DE->show();
//        ui->editor_E_End->show();
//        ui->editor_E_Start->show();
////        ui->editor_GasSpeed->show();
//        ui->editor_ScanPeroid->show();
//    }
//    switch (mode) {
//    case AnalysisMode::FilterScanMode:
//        ui->radioButtonFilterScan->setChecked(true);
//        ui->editor_AccPeriod->hide();
//        break;
//    case AnalysisMode::FilterSIMMode:
//        ui->radioButtonSIM->setChecked(true);
//        ui->editor_E_End->hide();
//        ui->editor_AccPeriod->hide();
//        ui->editor_ScanPeroid->setLabel("Dwell Time");
//        ui->editor_E_Start->setLabel("E1");
//        break;
//    case AnalysisMode::TrapScanMode:
//        ui->radioButtonTrapScan->setChecked(true);
//        ui->editor_DE->hide();
//        break;
//    case AnalysisMode::ThroughMode:
//        ui->radioButtonThrough->setChecked(true);
//        ui->editor_AccPeriod->hide();
//        ui->editor_DE->hide();
//        ui->editor_E_Start->setLabel("E");
//        ui->editor_E_End->hide();
//        break;
//    default:
//        break;
//    }

//    m_param.analysisMode = mode;
//    emit normalParamEdited(m_param);
//}
/*!
 * \brief PreferenceEditor::updateMethodFile
 * 加载方法文件methodFile中的参数到软件中。always等于false时，
 * 如果文件名与当前已加载的文件名相同(实际文件内容可能已修改)则什么也不操作，
 * 反之则每次加载都读取方法文件。
 * \param methodFile
 * \param always The default value is false
 */
void PreferenceEditor::updateMethodFile(const QString& methodFile, bool always)
{
//    if (!always && methodFile == m_param.methodFile)
//        return;
    ui->lineEdit_method->setText(methodFile);
    //加载参数窗口
    QDomElement element = getFirstMassEventElement(methodFile);
    updateFromMethodEvent(element);
    //更新通知
//    if (methodFile != m_param.methodFile) {
//        if (!m_param.methodFile.isEmpty())
//            m_fileWatcher.removePath(m_param.methodFile);
//        m_fileWatcher.addPath(methodFile);
//        m_param.methodFile = methodFile.toUtf8();
//        emit normalParamEdited(m_param);
//    }
}

//void PreferenceEditor::updateResolution(Resolution resolution)
//{
//    double gasSpeed = 0;
//    switch (resolution) {
//    case High_Resolution:
//        gasSpeed = I_GlobalVariable::getInstance()->gasSpeed_HighResolution();
//        ui->editor_GasSpeed->setVisible(false);
//        break;
//    case Low_Resolution:
//        gasSpeed = I_GlobalVariable::getInstance()->gasSpeed_LowResolution();
//        ui->editor_GasSpeed->setVisible(false);
//        break;
//    case Custom:
//        gasSpeed = ui->editor_GasSpeed->value();
//        ui->editor_GasSpeed->setVisible(true);
//        break;
//    }
//    ui->comboBox_resolution->setCurrentIndex(resolution);
//    /// 自定义时需要切换mz voltage table数据源
//    if (!qFuzzyCompare(gasSpeed ,m_param.gasSpeed_m_s) ||
//        resolution != m_param.resolution) ///
//    {
//        m_param.resolution = resolution;
//        m_param.gasSpeed_m_s = gasSpeed;
//        ui->editor_GasSpeed->setValue(gasSpeed);

//        emit normalParamEdited(m_param);
//    }
//}
/*!
 * \brief PreferenceEditor::getMethodParamEvents
 * 获取方法文件methodFile中各个Event的xml节点数据
 * \param methodFile
 * \return xml节点指针
 */
QList<QDomElement>* PreferenceEditor::getMethodParamEvents(
    const QString& methodFile)
{
    if (!QFile(methodFile).exists()) {
//        I_InsertNotify::getInstance()->insertNotify(
//            tr("The method file: %1 is not exist.").arg(methodFile),
//            I_InsertNotify::Error);
        return nullptr;
    }
    QString m_tempFile = QStringLiteral("IOModule/temp.tmp");
    QString m_ioModuleCMD = QStringLiteral("IOModule/IOModuleCmd.exe");
    QStringList params;
    params << "get"
           << "-m" << methodFile
           << "-t" << "param"
           << "-p" << m_tempFile;
    QProcess m_process;
    m_process.start(m_ioModuleCMD, params);
    if (m_process.waitForFinished(3000) && m_process.exitCode() == 0) {
        // xml解析
        m_MSParamParser.setFile(m_tempFile);
        QList<QDomElement>* events = m_MSParamParser.getDomEvents();
        return events;
    }
    return nullptr;
}

QDomElement PreferenceEditor::getFirstMassEventElement(const QString& methodFile)
{
    QList<QDomElement>* events = getMethodParamEvents(methodFile);
    if (events && !events->isEmpty())
        return events->first();
//    else
//        I_InsertNotify::getInstance()->insertNotify(
//            tr("The method file is not exist OR is empty."), I_InsertNotify::Warning);
    return QDomElement();
}
/*!
 * \brief PreferenceEditor::updateFromMethodEvent
 * 根据xml节点element中的内容显示Event的参数
 * \param element
 */
void PreferenceEditor::updateFromMethodEvent(const QDomElement& element)
{
    if (element.isNull())
        return;
    if (m_ParameterWidgetPtr) {
        ui->verticalLayout_MassParameter->removeWidget(m_ParameterWidgetPtr);
        delete m_ParameterWidgetPtr;
    }
    QString eventNo, polarity, eventType;
    QDomElement tmp;
    eventNo = element.attribute(attrNo());
    tmp = element.firstChildElement(tagAcqMode());
    eventType = tmp.text();
    tmp = element.firstChildElement(tagPolarity());
    polarity = tmp.text() == QStringLiteral("Positive") ? QStringLiteral("+")
                                                        : QStringLiteral("-");
    // 更新状态
    {
        ui->label_EventNO->setText(eventNo);
        ui->label_Polarity->setText(polarity);
        ui->label_EventType->setText(eventType);
    }
    if (eventType == "MRM") //
        m_ParameterWidgetPtr = new MRMParameterEditor(m_MSParamParser.getDomDocumentP(), element, this);
    else if (eventType == "ProductIonScan" || eventType == "PrecursorIonScan" || eventType == "NeutralLosScan")
        m_ParameterWidgetPtr = new IonScanParamEditor(m_MSParamParser.getDomDocumentP(), element, eventType, this);
    else if (eventType == "Q3Sim" || eventType == "Q1Sim")
        m_ParameterWidgetPtr = new Q13SIMParamEditor(m_MSParamParser.getDomDocumentP(), element, eventType, this);
    else if (eventType == "Q3Scan" || eventType == "Q1Scan")
        m_ParameterWidgetPtr = new Q13ScanParamEditor(m_MSParamParser.getDomDocumentP(), element, eventType, this);
//    else if (eventType == "QtflMs")
//        m_ParameterWidgetPtr = new QtflMsParamEditor(m_MSParamParser.getDomDocumentP(), element, this);
//    else if (eventType == "QtflMsMs")
//        m_ParameterWidgetPtr = new QtfMSMSEditorforUMACtrl(m_MSParamParser.getDomDocumentP(), element, this);
//    else if (eventType == "QtflMRM")
//        m_ParameterWidgetPtr = new QtflMRMParamEditor(m_MSParamParser.getDomDocumentP(), element, this);
    else
        m_ParameterWidgetPtr = nullptr; //增加其他Event
    if (m_ParameterWidgetPtr) {
        ui->verticalLayout_MassParameter->addWidget(m_ParameterWidgetPtr);

        m_eventParamPtr = m_ParameterWidgetPtr->getParamters();
        if (m_eventParamPtr->type != LabSolutionsRW::QtflMsMs)
            m_ParameterWidgetPtr->setEnabled(false);
        else
            connect(m_ParameterWidgetPtr, &AbstractParamEditor::eventParamModified,[&]{
                m_eventParamPtr = m_ParameterWidgetPtr->getParamters();
                updateNormalParamFromLabEvent(m_eventParamPtr);
            });

//        if (!methodTypeCheck(m_eventParamPtr))//类型不匹配
//        {
//            m_ParameterWidgetPtr->hide();
//            I_InsertNotify::getInstance()->insertNotify(tr("The method file does not match the currently configured instrument type."),
//                                                        I_InsertNotify::Error);
//            return;
//        }
        updateNormalParamFromLabEvent(m_eventParamPtr);
        //m_prfsManager->setLabEventParamPtr(m_eventParamPtr);
        //I_InsertNotify::getInstance()->insertNotify(tr("Method file loaded successful."), I_InsertNotify::Info);
    }
//    else
//        I_InsertNotify::getInstance()->insertNotify(tr("The event %1 is not support now.")
//                                                        .arg(eventType), I_InsertNotify::Warning);

//    PN_Mode mode = Positive;
//    if (polarity == "-")
//        mode = Negative;
//    updatePNMode(mode);
}

/*!
 * \brief PreferenceEditor::updatePNMode
 * 更新离子极性
 * \param mode
 */
//void PreferenceEditor::updatePNMode(PN_Mode ionPolarity)
//{
//    if (ionPolarity == m_param.pnMode)
//        return;

//    m_param.pnMode = ionPolarity;
//    int index = (ionPolarity == Positive) ? 0 : 1;
//    ui->comboBox_PNMode->setCurrentIndex(index);

//    emit normalParamEdited(m_param);
//}

/*!
 * \brief setMzPreference
 * 更新显示mz质量数对应的电压参数。
 * 每次切换扫描模式和精度时均应根据不同的参数表更新
 * \param mz
 * \param param
 */
//void PreferenceEditor::updateMzPreference(double mz,
//    const ParamAdvanceBase& param)
//{
//    blockSignals(true);
//    ui->currentMZ->setValue(mz);

//    for (int i = 0; i < ParamAdvanceBase::MP1_RF + 1; ++i) {
//        m_pRfEditor[i]->setValue(param.RF[i]);
//    }

//    for (int i = 0; i < ParamAdvanceBase::U12 + 1; ++i) {
//        m_pValatageEditor[i]->setValue(param.voltages[i]);
//    }
//    blockSignals(false);
//}

/*!
 * \brief PreferenceEditor::getAdvanceBaseParam
 * 获取输入的各电极参数
 * \return 输入的各电极参数
 */
//ParamAdvanceBase PreferenceEditor::getAdvanceBaseInputParam()
//{
//    ParamAdvanceBase param;
//    for (int i = 0; i < ParamAdvanceBase::MP1_RF + 1; ++i) {
//        param.RF[i] = m_pRfEditor[i]->value();
//    }

//    for (int i = 0; i < ParamAdvanceBase::U12 + 1; ++i) {
//        param.voltages[i] = m_pValatageEditor[i]->value();
//    }
//    return param;
//}

void PreferenceEditor::updateNormalParamFromLabEvent(LabSolutionsRW::Event* evt)
{
    if (nullptr == evt)
        return;
    switch (evt->type) {
    case LabSolutionsRW::MRM: {
        LabSolutionsRW::EventMRM* eventParam = static_cast<LabSolutionsRW::EventMRM*>(evt);
        if (eventParam->chanels.count() > 0) {
//            m_param.mzLow = eventParam->chanels.first().precursor;
//            m_param.mzHigh = m_param.mzLow;//MRM 模式只取母离子;
        }
    } break;
    case LabSolutionsRW::Q1Scan:
    case LabSolutionsRW::Q3Scan: {
        LabSolutionsRW::EventScan* eventParam = static_cast<LabSolutionsRW::EventScan*>(evt);
//        m_param.mzLow = eventParam->startMz;
//        m_param.mzHigh = eventParam->endMz;
    } break;
    case LabSolutionsRW::Q1SIM:
    case LabSolutionsRW::Q3SIM: {
        LabSolutionsRW::EventSIM* eventParam = static_cast<LabSolutionsRW::EventSIM*>(evt);
        if (eventParam->chanels.count() > 0) {
//            m_param.mzLow = eventParam->chanels.first().precursor;
//            m_param.mzHigh = m_param.mzLow;//SIM 模式只取母离子;
        }
    } break;
    case LabSolutionsRW::QtflMs:
    {
        LabSolutionsRW::EventQtflMs* eventParam = static_cast<LabSolutionsRW::EventQtflMs*>(evt);
//        m_param.mzLow = eventParam->startMz;
//        m_param.mzHigh = eventParam->endMz;
//        m_param.ce = eventParam->ce;
    } break;
    case LabSolutionsRW::QtflMsMs:
    {
        LabSolutionsRW::EventQtfMsMs* eventParam = static_cast<LabSolutionsRW::EventQtfMsMs*>(evt);
//        m_param.mzLow = eventParam->ionMz;
//        m_param.mzHigh = eventParam->ionMz;
//        m_param.ce = eventParam->ce;
    } break;
    case LabSolutionsRW::QtflMRM:
    {
        LabSolutionsRW::EventQtfMRM* eventParam = static_cast<LabSolutionsRW::EventQtfMRM*>(evt);
        if (eventParam->channels.count() > 0)
        {
//            m_param.mzLow = eventParam->channels.first().startMz;
//            m_param.mzHigh = m_param.mzLow; //MRM 模式只取母离子;
//            m_param.ce = eventParam->channels.first().ce;
        }
    } break;
    default:
//        I_InsertNotify::getInstance()->insertNotify(tr("This event(%1) is not support now.")
//                                                        .arg(evt->type),
//            I_InsertNotify::Warning);
        break;
    }
    //emit normalParamEdited(m_param);
}
/*!
 * \brief PreferenceEditor::methodTypeCheck
 * 返回当前Event类型是否与选择的仪器类型匹配
 * \param pEvt
 * \return
 */
//bool PreferenceEditor::methodTypeCheck(LabSolutionsRW::Event *pEvt)
//{
//    InstrumentType insType = I_GlobalVariable::getInstance()->instrumentType();
//    LabSolutionsRW::EventType evtType = pEvt->type;
//    if (insType == InstrumentType::QTOF &&
//        (evtType != LabSolutionsRW::QtflMs &&
//         evtType != LabSolutionsRW::QtflMRM &&
//         evtType != LabSolutionsRW::QtflMsMs))
//        return false;
//    if (insType == InstrumentType::QQQ &&
//        (evtType == LabSolutionsRW::QtflMs ||
//         evtType == LabSolutionsRW::QtflMRM ||
//         evtType == LabSolutionsRW::QtflMsMs))
//        return false;
//    else
//        return true;
//}

LabSolutionsRW::Event *PreferenceEditor::getEventParamPtr() const
{
    return m_eventParamPtr;
}

void PreferenceEditor::setMassEditable(bool able)
{
    setEnabled(able);
}

//void PreferenceEditor::on_pushButton_getMZParam_clicked()
//{
//    Q_ASSERT(m_prfsManager);
//    double mz = ui->currentMZ->value();
//    // get from manager
//    ParamAdvanceBase base = m_prfsManager->getMzPreference(mz);
//    updateMzPreference(mz, base);
//    initMzVolParamStyle();
//}

//void PreferenceEditor::on_pushButton_SaveMZ_clicked()
//{
//    auto r = QMessageBox::warning(this, tr("Save MZ"), tr("This operation will modify the mz-voltage table, please be careful."),
//                                  QMessageBox::Save | QMessageBox::Cancel);
//    if (r == QMessageBox::Cancel)
//        return;
//    Q_ASSERT(m_prfsManager);
//    double mz = ui->currentMZ->value();
//    ParamAdvanceBase param = getAdvanceBaseInputParam();
//    // save to manager
//    m_prfsManager->saveMzPreference(mz, param);
//    initMzVolParamStyle();
//}

//void PreferenceEditor::on_comboBox_resolution_currentIndexChanged(int index)
//{
//    Resolution resolution;
//    switch (index) {
//    case 0: resolution = uma_project::High_Resolution; break;
//    case 1: resolution = uma_project::Low_Resolution; break;
//    default: resolution = uma_project::Custom;
//    }
//    updateResolution(resolution);
//}

void PreferenceEditor::on_voltageRangeChanged(QPair<double, double> range)
{
    ui->editor_E_Start->setRange(range.first, range.second);
    ui->editor_E_End->setRange(range.first, range.second);
}

void PreferenceEditor::on_gasSpeedRangeChanged(QPair<double, double> range)
{
    ui->editor_GasSpeed->setRange(range.first, range.second);
}
