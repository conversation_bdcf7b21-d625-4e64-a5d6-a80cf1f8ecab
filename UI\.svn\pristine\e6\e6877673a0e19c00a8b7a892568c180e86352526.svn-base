/****************************************************************************
** Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
** All right reserved.
**
** Permission is hereby granted, free of charge, to any person obtaining
** a copy of this software and associated documentation files (the
** "Software"), to deal in the Software without restriction, including
** without limitation the rights to use, copy, modify, merge, publish,
** distribute, sublicense, and/or sell copies of the Software, and to
** permit persons to whom the Software is furnished to do so, subject to
** the following conditions:
**
** The above copyright notice and this permission notice shall be
** included in all copies or substantial portions of the Software.
**
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
** EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
** MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
** NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
** LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
** OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
** WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
**
****************************************************************************/

/*!
    \title Qt Xlsx
    \page index.html
    \brief Qt Xlsx provides functionality for handling .xlsx files.

    The \l{Qt Xlsx C++ Classes}{Qt Xlsx Module} provides a set of classes to read and write Excel files. It doesn't require
    Microsoft Excel and can be used in any platform that Qt5 supported. The library can be used to

    \list
       \li \l{Hello QtXlsx Example}{Generate a new .xlsx file from scratch}
       \li \l{Extract Data Example}{Extract data from an existing .xlsx file}
       \li Edit an existing .xlsx file
    \endlist

    \image xlsx_demo.gif

    \table
      \row
        \li Source code: \li \l{https://github.com/dbzhang800/QtXlsxWriter}
      \row
        \li Issures: \li \l{https://github.com/dbzhang800/QtXlsxWriter/issues}
      \row
        \li License: \li MIT
    \endtable

    \section1 Getting Started

    To include the definitions of the module's classes, using the following directive:

    \code
    #include <QtXlsx>
    \endcode

    To link against the module, add this line to your qmake .pro file:

    \code
    QT += xlsx
    \endcode

    More information can be found in \l{Qt Xlsx Build} page.

    \section1 Related information
    \list
        \li \l{Qt Xlsx C++ Classes}
        \li \l{Qt Xlsx Examples}
    \endlist
*/
