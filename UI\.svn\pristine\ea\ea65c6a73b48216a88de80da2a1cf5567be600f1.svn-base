/****************************************************************************
** Meta object code from reading C++ file 'uiQueue.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../uiQueue.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiQueue.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiQueue_t {
    QByteArrayData data[9];
    char stringdata0[120];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiQueue_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiQueue_t qt_meta_stringdata_uiQueue = {
    {
QT_MOC_LITERAL(0, 0, 7), // "uiQueue"
QT_MOC_LITERAL(1, 8, 8), // "onSubmit"
QT_MOC_LITERAL(2, 17, 0), // ""
QT_MOC_LITERAL(3, 18, 30), // "QList<QMap<QString,QString> >&"
QT_MOC_LITERAL(4, 49, 5), // "pList"
QT_MOC_LITERAL(5, 55, 4), // "date"
QT_MOC_LITERAL(6, 60, 19), // "onUI_MB_START_QUEUE"
QT_MOC_LITERAL(7, 80, 18), // "onUI_MB_STOP_QUEUE"
QT_MOC_LITERAL(8, 99, 20) // "onUI_MB_PRINT_QUEUEH"

    },
    "uiQueue\0onSubmit\0\0QList<QMap<QString,QString> >&\0"
    "pList\0date\0onUI_MB_START_QUEUE\0"
    "onUI_MB_STOP_QUEUE\0onUI_MB_PRINT_QUEUEH"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiQueue[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    2,   34,    2, 0x0a /* Public */,
       6,    0,   39,    2, 0x08 /* Private */,
       7,    0,   40,    2, 0x08 /* Private */,
       8,    0,   41,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::QString,    4,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiQueue::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiQueue *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onSubmit((*reinterpret_cast< QList<QMap<QString,QString> >(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 1: _t->onUI_MB_START_QUEUE(); break;
        case 2: _t->onUI_MB_STOP_QUEUE(); break;
        case 3: _t->onUI_MB_PRINT_QUEUEH(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiQueue::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiQueue.data,
    qt_meta_data_uiQueue,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiQueue::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiQueue::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiQueue.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiQueue::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
