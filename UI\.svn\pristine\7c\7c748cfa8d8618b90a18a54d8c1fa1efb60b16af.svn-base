#pragma once

#include <QWidget>
#include <qUiWidget.h>
//#include <cParamCCS.h>
#include "ui_uiSystem.h"
#include <QtScript/QScriptEngine>
#include "uiSystem/uiDevices.h"
#include <uiSystem/uiProjects.h>
#include <uiSystem/uiQueueSys.h>
#include <uiSystem/uiUserManagement.h>


class uiSystem : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiSystem(QScriptEngine* pScriptEngine,
                     QWidget *parent = nullptr);
    ~uiSystem();
    virtual void initClass(QString& filePath);
//    libControlCCS::ParamCCS::_DAQ_CONFIG* getConfigDAQ(){
//        return &mDAQ_CONFIG;
//    }
    uiDevices* mDevices= nullptr;
    uiProjects* mProjects= nullptr;
    uiQueueSys* mQueueSys= nullptr;
    uiUserManagement* mUserManagement= nullptr;
private slots:
    void on_UI_LISTW_MENU_LEFT_SYSTEM_currentRowChanged(int currentRow);

protected:
    bool initUI(QString& filePath);
    virtual void createToolBar();

private:
    Ui::uiSystem ui;
    QWidget* mParent=nullptr;
    QScriptEngine* mScriptEngine= nullptr;

    //void initUI();
    void setCurrentWindow(QWidget* pWidget);

    //libControlCCS::ParamCCS::_DAQ_CONFIG mDAQ_CONFIG;
};
