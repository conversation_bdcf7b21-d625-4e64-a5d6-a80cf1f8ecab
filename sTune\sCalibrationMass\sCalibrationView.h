#pragma once

#include <QWidget>
#include "ui_sCalibrationView.h"
#include <LibWidget/sChartWidget.h>
//#include <USBConnection/cParamCCS.h>
#include <HWConnection.h>
#include <USBConnection/HCSCommandUSB.h>
#include <sSingleAcquisition/Q13ScanParamEditor.h>
#include <uiCalibrationMass/uiCalibrationView.h>
#include "sTune/sCalibrationMass/sCalibrationMassItem.h"
#include <MassEventSplitter.h>
#include <cMatrix.h>
#include <cPublicStructHZH.h>
#include <sThread.h>
#include "cDebugFunctions.h"
#include "cFooting/cAcqFunction.h"
class sCalibrationView : public uiCalibrationView
{
    Q_OBJECT

public:
    QString FormulaTitle;

    explicit sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type,//QString pro,
                              sMapSetMZ* pMapSetMZ,
                              QWidget *parent = nullptr);
    ~sCalibrationView();
    void initClass(QString& filePath);
    Q13ScanParamEditor* getScanParamEditor(){
        return (Q13ScanParamEditor*)mQ13ScanParamEditor;
    }

    bool calibrate(const double& scanSpeed, float& startMass, float& endMass){
        _CONGIG_OMS::_PARAM_FIT tmpPARAM_FIT;
        QList<QVector<double>> tmpList;
        QVector<double> speedV, startV, endV;
        for(auto key: mCalibrationMassItemMap.keys()){
            QVector<double> tmpV(2);
            tmpV[0]= startMass;
            tmpV[1]= endMass;
            sCalibrationMassItem* pCalibrationMassItem= (sCalibrationMassItem*)mCalibrationMassItemMap[key];
            if(!pCalibrationMassItem->calculate(tmpV.data(), tmpV.data(), 2))
                continue;
            speedV.append(key);
            startV.append(tmpV[0]);
            endV.append(tmpV[1]);
        }
        if(speedV.isEmpty())
            return false;
        double slope, intercept, squareR;
        if(!cMatrix::linearFit(speedV.data(), startV.data(), speedV.size(), slope, intercept, squareR))
            return false;
        startMass= scanSpeed* slope+ intercept;
        if(!cMatrix::linearFit(speedV.data(), endV.data(), speedV.size(), slope, intercept, squareR))
            return false;
        endMass= scanSpeed* slope+ intercept;
        return true;
    }
//    bool calibrate(const double& scanSpeed, QVector<float>& mass){
//        int sizeMass= mass.size();
//        _CONGIG_OMS::_PARAM_FIT tmpPARAM_FIT;
//        QList<QVector<double>> tmpList;
//        QVector<double> speedV, startV, endV;
//        for(auto key: mCalibrationMassItemMap.keys()){
//            QVector<double> tmpV(sizeMass);
//            tmpV[0]= startMass;
//            tmpV[1]= endMass;
//            sCalibrationMassItem* pCalibrationMassItem= (sCalibrationMassItem*)mCalibrationMassItemMap[key];
//            if(!pCalibrationMassItem->calculate(tmpV.data(), tmpV.data(), 2))
//                continue;
//            speedV.append(key);
//            startV.append(tmpV[0]);
//            endV.append(tmpV[1]);
//        }
//        if(speedV.isEmpty())
//            return false;
//        double slope, intercept, squareR;
//        if(!cMatrix::linearFit(speedV.data(), startV.data(), speedV.size(), slope, intercept, squareR))
//            return false;
//        startMass= scanSpeed* slope+ intercept;
//        if(!cMatrix::linearFit(speedV.data(), endV.data(), speedV.size(), slope, intercept, squareR))
//            return false;
//        endMass= scanSpeed* slope+ intercept;
//        return true;
//    }

public:
    _STRUCT_ADC_TDC m_STRUCT_ADC_TDC;
    MassEventSplitter *m_pMsEvtSplitter;
    //cTQ_CMD_HZH::_EVENT_PARAM_SET m_EVENT_PARAM_SET;
    bool acquisitionStart(){
        mAnalyzeThread->start();
        if(mTimerRefreshID==-1){
            mTimerRefreshID=startTimer(500);
        }
        return true;
    }
    void acquisitionStop(){
        if(mTimerRefreshID!=-1)
            killTimer(mTimerRefreshID);
        mTimerRefreshID=-1;
        mAnalyzeThread->stop();
    }

protected:
    SThread* mAnalyzeThread= nullptr;
    int mTimerRefreshID = -1;
    static int analyzeThread(void *pParam, const bool &bRunning);
    void timerEvent(QTimerEvent *evt);
    std::vector<MassEventSplitter::MassSegment> m_msSegments;
    //int umaChMapMsChs(const UMA_HCS::HCSDataFrame::HCSData &chData);
    void updateScanParam();
    uiCalibrationMassItem* createItem(QString& str) override{
        sCalibrationMassItem* pCalibrationMassItem= new sCalibrationMassItem(this);
        if(!pCalibrationMassItem->setParam(str)){
            delete pCalibrationMassItem;
            pCalibrationMassItem= nullptr;
            return nullptr;
        }
        return pCalibrationMassItem;
    }
    QByteArray mDataBuffer;
    std::vector<double> mGraphBuffX;//std::vector<double>* mGraphBuff[2];         //size-allPoint
    std::vector<double> mGraphBuffY;
    volatile bool mIsNewMass= false;
    QMutex mGraphBuffMutex;
private slots:
    void onUI_PB_ADDMASS_CAL_clicked() override;
    void onUI_PB_DELETEMASS_CAL_clicked() override;
};

