#ifndef SDOUBLEVALIDATOR_H
#define SDOUBLEVALIDATOR_H

#include <QObject>
#include <QDoubleValidator>


class SDoubleValidator : public QDoubleValidator
{
public:
    SDoubleValidator(double bottom, double top, int decimals, QObject *parent = nullptr);
    Q_DISABLE_COPY(SDoubleValidator)

protected:
    QValidator::State validate(QString &input, int &pos) const;
//    void setRange(double minimum, double maximum, int decimals = 0);

};

#endif // SDOUBLEVALIDATOR_H
