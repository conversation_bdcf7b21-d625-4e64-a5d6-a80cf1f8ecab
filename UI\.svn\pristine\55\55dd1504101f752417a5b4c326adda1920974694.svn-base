﻿#pragma once

#include "uiBaseParamEditor.h"
#include "ui_uiMRMParameterEditor.h"
#include <QTableWidgetItem>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <cPublicDefine.h>


class uiMRMParameterEditor : public uiBaseParamEditor//AbstractParamEditor
{
    Q_OBJECT
public:
    uiMRMParameterEditor(QWidget* parent = nullptr);
    ~uiMRMParameterEditor(){}
    //void saveParameter() override;
    virtual void initClass(QString& filePath);

protected:
    virtual bool initUI(QString& filePath);
public:
    bool getTableParam(QVector<float>& Precursor,
                       QVector<float>& Product,
                       QVector<float>& DwellTimeMs,
                       QVector<float>& CE);

private slots:
    void on_chanel_Changed(QTableWidgetItem *item);
    void on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1);
    void on_lineEdit_pauseTime_textEdited(const QString &arg1);
    void on_lineEdit_eventTime_textEdited(const QString &arg1);

private:
    Ui::uiMRMParameterEditor ui;
    bool errorParam= false;
    void initPage();
    QTableWidgetItem *getItem(int r, int c);
    bool getWaitTimeMs(QString strPN_SwitchTimeMs, QString strPauseTimeMs,
                         QString strEventTimeMs);

};

