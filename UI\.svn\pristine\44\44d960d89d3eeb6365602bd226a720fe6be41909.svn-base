#pragma once

#include <QScriptEngine>
#include <QWidget>
#include "ui_uiSingleAcquisition.h"
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include <LibWidget/sChartWidget.h>
#include "uiSystem.h"
#include "uiTune.h"
#include <uiSingleAcquisition/uiMRMParameterEditor.h>
#include <uiSingleAcquisition/uiQ13SIMParamEditor.h>
#include "uiSingleAcquisition/uiQ13ScanParamEditor.h"
#include "uiSingleAcquisition/uiIonScanParamEditor.h"

class uiSingleAcquisition : public qUiWidget
{
    Q_OBJECT

public:
    Ui::uiSingleAcquisition ui;
    uiTune* mTune= nullptr;
    uiSystem* mSystem= nullptr;
    explicit uiSingleAcquisition(QScriptEngine* pScriptEngine,
                                uiTune* pTune,
                                uiSystem* pSystem,
                                QWidget *parent = nullptr);
    ~uiSingleAcquisition();
    virtual void initClass(QString& filePath);
    sChartWidget* mChartTIC=nullptr;
    QList<sChartWidget*> mMassChart;

    uiQ13ScanParamEditor* mQ1ScanParamEditor= nullptr;
    uiQ13ScanParamEditor* mQ3ScanParamEditor= nullptr;
    uiQ13SIMParamEditor* mQ1SIMParamEditor= nullptr;
    uiQ13SIMParamEditor* mQ3SIMParamEditor= nullptr;
    uiMRMParameterEditor* mMRMParameterEditor= nullptr;
    uiIonScanParamEditor* mProIonScanParamEditor= nullptr;
    uiIonScanParamEditor* mPreIonScanParamEditor= nullptr;
    void resizeChart(int size);

protected:

    bool initUI(QString& filePath);
    void createToolBar();

protected slots:
    virtual void onUI_MB_RUN_SINGLEACQ(){}
    virtual void onUI_MB_STOP_SINGLEACQ(){}
    virtual void on_UI_PB_ADVANCE_SINGLEACQ_clicked(){}
    //void on_UI_TABWIDGET_PARAM_SINGLEACQ_currentChanged(int index);
};

