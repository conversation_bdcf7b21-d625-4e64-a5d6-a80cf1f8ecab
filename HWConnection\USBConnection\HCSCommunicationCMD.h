﻿#ifndef HCSCOMMUNICATIONCMD_H
#define HCSCOMMUNICATIONCMD_H

#include <QObject>
#include <cCommunicationCMD.h>
#include <sCommandUSB.h>
#include <boost/lockfree/queue.hpp>

//#include "USBConnection/sCommandUSB/cCommunicationCMD.h"
#include "cDebugFunctions.h"
/**
 * @brief The HCSCommunicationCMD class
 * 用于HCS DAQ数据采集
 */
class HCSCommunicationCMD : public libControlCCS::CommunicationCMD
{
    Q_OBJECT
public:
    typedef struct{
        uint64_t frameId;
        std::vector<uint32_t> data;
    } HCSDaqPacket;
public:
    explicit HCSCommunicationCMD(QObject *parent = nullptr);
    virtual ~HCSCommunicationCMD();
    //QList<sCommandUSB::_DAQ_DATA*>* getDataDAQ() = delete;
    bool getDataDAQ(HCSDaqPacket* packet);
//    int startASG(bool extTrigger = true) override;
//    int stopASG() override;
//    int startTuning();
//    int stopTuning();
//bool clearBuffUSB();
private:
    boost::lockfree::queue<HCSDaqPacket*, boost::lockfree::fixed_sized<true>> mDaqPacket;

    void clearBuffer();
    static int HcsDaqThread(void *pParam, const bool &bRunning);
    bool daqGetData(QVector<uint>& recvBuffer, uint* pLastPkt,
                    uint& uFrameCount, uint& uPacketCount, bool& isSameFrame,
                    bool& isRightPacket, bool &isGet);
    HCSDaqPacket* mCurrentPacket;
};

#endif // HCSCOMMUNICATIONCMD_H
