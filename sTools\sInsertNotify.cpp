﻿#include "sInsertNotify.h"
#include <QTime>
#include <QDebug>
#include <QMetaEnum>

cSafeData<QStringList> sInsertNotify::m_logs;
int sInsertNotify::m_logBeginID = 0;

sInsertNotify::sInsertNotify(QObject *parent) : QObject(parent)
{
    qRegisterMetaType<Degree>("Degree");
    qInstallMessageHandler(msgHandler);
}

void sInsertNotify::insertNotify(const QString &msg, sInsertNotify::Degree d)
{
    if (d == Info)
        insertTemporary(msg, 5);
    else{
        qDebug() << QString("C_Insert %1: ").arg(QMetaEnum::fromType<Degree>().valueToKey(d)) + msg;
        emit sig_insertNotify(msg, d);
    }
}

/*!
 * \brief sInsertNotify::insertTemporary
 *
 * \param msg
 * \param sec
 */
void sInsertNotify::insertTemporary(const QString &msg, int sec)
{
    qDebug() << QString("C_Insert Temporary: ") + msg;
    emit sig_insertTemporary(msg, sec);
}

void sInsertNotify::msgHandler(QtMsgType type, const QMessageLogContext &/*content*/, const QString &msg)
{
    QString strMsg;
    strMsg = QTime::currentTime().toString("hhmmss");
    switch (type) {
    case QtInfoMsg:
        strMsg += "I: ";
        break;
    case QtDebugMsg:
        strMsg +=  "D: ";
        break;
    case QtWarningMsg:
        strMsg +=  "W: ";//QString("w: %1 line %2 %3 ").arg(content.function).arg(content.line).arg(content.category) ;
        break;
    case QtCriticalMsg:
        strMsg +=  "C: ";
        break;
    case QtFatalMsg:
        strMsg +=  "F: ";
    }
    strMsg += msg;// + "\n";

    m_logs.lockForWrite();
    if(m_logs->size() > 1000){
        m_logs->erase(m_logs->begin(), m_logs->begin() + 500);
        m_logBeginID += 500;
    }
    m_logs->append(strMsg);
    m_logs.unlock();
    //ts<<strMsg;
    fprintf(stderr,"%s\n",strMsg.toLocal8Bit().constData());
    fflush(stderr);
    if(type == QtFatalMsg)abort();
}

QStringList sInsertNotify::logs(int& nBeginID)
{
    QStringList lstLog;
    if(nBeginID > (m_logBeginID + m_logs->size() - 1)){
        return lstLog;
    }
    int nIndex;
    m_logs.lockForRead();
    if(m_logBeginID > nBeginID)
        nIndex = 0;
    else
        nIndex = nBeginID - m_logBeginID;
    nBeginID = m_logBeginID + m_logs->size();
    for(auto iterMsg = m_logs->begin() + nIndex; iterMsg != m_logs->end(); iterMsg++)
        lstLog.append(*iterMsg);
    m_logs.unlock();
    return lstLog;
}
