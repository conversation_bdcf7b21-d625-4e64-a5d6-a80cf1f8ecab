/****************************************************************************
** Meta object code from reading C++ file 'uiBatch.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiBatch.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiBatch.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiBatch_t {
    QByteArrayData data[9];
    char stringdata0[142];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiBatch_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiBatch_t qt_meta_stringdata_uiBatch = {
    {
QT_MOC_LITERAL(0, 0, 7), // "uiBatch"
QT_MOC_LITERAL(1, 8, 7), // "sSubmit"
QT_MOC_LITERAL(2, 16, 0), // ""
QT_MOC_LITERAL(3, 17, 17), // "onUI_MB_NEW_BATCH"
QT_MOC_LITERAL(4, 35, 18), // "onUI_MB_OPEN_BATCH"
QT_MOC_LITERAL(5, 54, 18), // "onUI_MB_SAVE_BATCH"
QT_MOC_LITERAL(6, 73, 20), // "onUI_MB_SAVEAS_BATCH"
QT_MOC_LITERAL(7, 94, 26), // "onUI_MB_PLATE_LAYOUT_BATCH"
QT_MOC_LITERAL(8, 121, 20) // "onUI_MB_SUBMIT_BATCH"

    },
    "uiBatch\0sSubmit\0\0onUI_MB_NEW_BATCH\0"
    "onUI_MB_OPEN_BATCH\0onUI_MB_SAVE_BATCH\0"
    "onUI_MB_SAVEAS_BATCH\0onUI_MB_PLATE_LAYOUT_BATCH\0"
    "onUI_MB_SUBMIT_BATCH"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiBatch[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    0,   50,    2, 0x08 /* Private */,
       4,    0,   51,    2, 0x08 /* Private */,
       5,    0,   52,    2, 0x08 /* Private */,
       6,    0,   53,    2, 0x08 /* Private */,
       7,    0,   54,    2, 0x08 /* Private */,
       8,    0,   55,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void uiBatch::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiBatch *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sSubmit(); break;
        case 1: _t->onUI_MB_NEW_BATCH(); break;
        case 2: _t->onUI_MB_OPEN_BATCH(); break;
        case 3: _t->onUI_MB_SAVE_BATCH(); break;
        case 4: _t->onUI_MB_SAVEAS_BATCH(); break;
        case 5: _t->onUI_MB_PLATE_LAYOUT_BATCH(); break;
        case 6: _t->onUI_MB_SUBMIT_BATCH(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiBatch::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiBatch::sSubmit)) {
                *result = 0;
                return;
            }
        }
    }
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject uiBatch::staticMetaObject = { {
    &qUiWidget::staticMetaObject,
    qt_meta_stringdata_uiBatch.data,
    qt_meta_data_uiBatch,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiBatch::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiBatch::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiBatch.stringdata0))
        return static_cast<void*>(this);
    return qUiWidget::qt_metacast(_clname);
}

int uiBatch::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = qUiWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void uiBatch::sSubmit()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
