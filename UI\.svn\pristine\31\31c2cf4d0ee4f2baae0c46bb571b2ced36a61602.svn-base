/********************************************************************************
** Form generated from reading UI file 'sTICList.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_STICLIST_H
#define UI_STICLIST_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sTICList
{
public:
    QVBoxLayout *verticalLayout;
    QListWidget *listWidget;
    QHBoxLayout *horizontalLayout;
    QLineEdit *lineEdit;
    QPushButton *BtnAdd;

    void setupUi(QWidget *sTICList)
    {
        if (sTICList->objectName().isEmpty())
            sTICList->setObjectName(QString::fromUtf8("sTICList"));
        sTICList->resize(122, 300);
        sTICList->setMaximumSize(QSize(150, 16777215));
        verticalLayout = new QVBoxLayout(sTICList);
        verticalLayout->setSpacing(0);
        verticalLayout->setContentsMargins(11, 11, 11, 11);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        listWidget = new QListWidget(sTICList);
        listWidget->setObjectName(QString::fromUtf8("listWidget"));
        listWidget->setAutoFillBackground(false);
        listWidget->setStyleSheet(QString::fromUtf8("background-color:transparent"));
        listWidget->setFrameShape(QFrame::NoFrame);

        verticalLayout->addWidget(listWidget);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(6);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        lineEdit = new QLineEdit(sTICList);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));

        horizontalLayout->addWidget(lineEdit);

        BtnAdd = new QPushButton(sTICList);
        BtnAdd->setObjectName(QString::fromUtf8("BtnAdd"));

        horizontalLayout->addWidget(BtnAdd);


        verticalLayout->addLayout(horizontalLayout);


        retranslateUi(sTICList);

        QMetaObject::connectSlotsByName(sTICList);
    } // setupUi

    void retranslateUi(QWidget *sTICList)
    {
        sTICList->setWindowTitle(QApplication::translate("sTICList", "sTICList", nullptr));
        BtnAdd->setText(QApplication::translate("sTICList", "Add", nullptr));
    } // retranslateUi

};

namespace Ui {
    class sTICList: public Ui_sTICList {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_STICLIST_H
