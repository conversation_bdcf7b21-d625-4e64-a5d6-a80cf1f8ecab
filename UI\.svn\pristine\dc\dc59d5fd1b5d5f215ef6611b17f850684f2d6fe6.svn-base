﻿#include "uiQ13ScanParamEditor.h"

//#include "sMethod/xml_attr_tag.h"

#include <cGlobalStruct.h>

uiQ13ScanParamEditor::uiQ13ScanParamEditor(QString Q,
                                       QWidget* parent):
    uiBaseParamEditor(parent)
{

    setProperty("currentQ", Q);
}

uiQ13ScanParamEditor::~uiQ13ScanParamEditor()
{

}

void uiQ13ScanParamEditor::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiQ13ScanParamEditor::initUI(QString& filePath)
{
    ui.setupUi(this);
    uiBase.uiBaseParamEditorLayout->insertWidget(0, ui.uiQ13ScanParamEditorWidget);
    return true;
}

void uiQ13ScanParamEditor::on_lineEdit_endMZ_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    setPolarity_switch_time(tmpPARAM.PN_SwitchTimeMs);
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    setPauseTime(tmpPARAM.PauseTimeMs);
    tmpPARAM.Q1_Mz_Start= getStartMZ(&ok);
    if(!ok) return;
    tmpPARAM.Q1_Mz_End= arg1.toFloat(&ok);
    if(!ok) return;
    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(getEventTime(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0)) return;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if(uiBase.UI_PB_Polarity->text()== "+"){
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
        }
    }else{
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
        }
    }
    calcUseMapMZ(Type, tmpPARAM);
    setWaitTime(tmpPARAM.WaitTimeMs);
    setScanSpeed(tmpPARAM.getScanSpeedQ1());
}

void uiQ13ScanParamEditor::on_lineEdit_startMZ_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(tmpPARAM.PN_SwitchTimeMs));
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(tmpPARAM.PauseTimeMs));
    tmpPARAM.Q1_Mz_Start= arg1.toFloat();
    tmpPARAM.Q1_Mz_End= ui.lineEdit_endMZ->text().toFloat();
    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;//tmpPARAM.getWaitTimeMs(uiBase.lineEdit_eventTime->text().toFloat(),
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(uiBase.lineEdit_eventTime->text().toFloat(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0)) return;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if(uiBase.UI_PB_Polarity->text()== "+"){
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
        }
    }else{
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
        }
    }
    calcUseMapMZ(Type, tmpPARAM);
    uiBase.lineEdit_waitTime_rOnly->setText(QString::number(tmpPARAM.WaitTimeMs));
    float speed= tmpPARAM.getScanSpeedQ1();
    ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
}
//    void uiQ13ScanParamEditor::on_lineEdit_scanSpeed_textChanged(const QString &arg1)
//{
//        cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
//        tmpPARAM.PauseTimeMs=0;
//        tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(ui.lineEdit_Polarity_switch_time->text().toFloat()* 1000);
//        ui.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(tmpPARAM.PN_SwitchTimeMs/1000));
//        tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(ui.lineEdit_pauseTime->text().toFloat()* 1000);
//        ui.lineEdit_pauseTime_rOnly->setText(QString::number(tmpPARAM.PauseTimeMs/1000));
//        tmpPARAM.Q1_Mz_Start= ui.lineEdit_startMZ->text().toFloat();
//        tmpPARAM.Q1_Mz_End= ui.lineEdit_endMZ->text().toFloat();
//        float speed= tmpPARAM.setScanTimeMsQ1(arg1.toFloat());
//        ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
//        if(speed>0){
//            ui.lineEdit_eventTime_rOnly->setText(QString::number(tmpPARAM.eventTime_ms()/1000));
//        }else{
//            ui.lineEdit_eventTime_rOnly->setText("0");
//        }
//    }

void uiQ13ScanParamEditor::on_lineEdit_pauseTime_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(tmpPARAM.PN_SwitchTimeMs));
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(arg1.toFloat());
    uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(tmpPARAM.PauseTimeMs));
    tmpPARAM.Q1_Mz_Start= ui.lineEdit_startMZ->text().toFloat();
    tmpPARAM.Q1_Mz_End= ui.lineEdit_endMZ->text().toFloat();
    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;//tmpPARAM.WaitTimeMs= tmpPARAM.getWaitTimeMs(ui.lineEdit_eventTime->text().toFloat());
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(uiBase.lineEdit_eventTime->text().toFloat(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0)) return;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if(uiBase.UI_PB_Polarity->text()== "+"){
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
        }
    }else{
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
        }
    }
    calcUseMapMZ(Type, tmpPARAM);
    uiBase.lineEdit_waitTime_rOnly->setText(QString::number(tmpPARAM.WaitTimeMs));
    float speed= tmpPARAM.getScanSpeedQ1();
    ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
}

void uiQ13ScanParamEditor::on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(arg1.toFloat());
    uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(tmpPARAM.PN_SwitchTimeMs));
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(tmpPARAM.PauseTimeMs));
    tmpPARAM.Q1_Mz_Start= ui.lineEdit_startMZ->text().toFloat();
    tmpPARAM.Q1_Mz_End= ui.lineEdit_endMZ->text().toFloat();
    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;//tmpPARAM.WaitTimeMs= tmpPARAM.getWaitTimeMs(ui.lineEdit_eventTime->text().toFloat());
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(uiBase.lineEdit_eventTime->text().toFloat(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0)) return;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if(uiBase.UI_PB_Polarity->text()== "+"){
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
        }
    }else{
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
        }
    }
    calcUseMapMZ(Type, tmpPARAM);
    uiBase.lineEdit_waitTime_rOnly->setText(QString::number(tmpPARAM.WaitTimeMs));
    float speed= tmpPARAM.getScanSpeedQ1();
    ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
}

void uiQ13ScanParamEditor::on_lineEdit_eventTime_textEdited(const QString &arg1)
{
    bool ok=false;
    cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM tmpPARAM;
    tmpPARAM.PN_SwitchTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_Polarity_switch_time->text().toFloat());
    uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(tmpPARAM.PN_SwitchTimeMs));
    tmpPARAM.PauseTimeMs= tmpPARAM.getUsableTimeMs(uiBase.lineEdit_pauseTime->text().toFloat());
    uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(tmpPARAM.PauseTimeMs));
    tmpPARAM.Q1_Mz_Start= ui.lineEdit_startMZ->text().toFloat();
    tmpPARAM.Q1_Mz_End= ui.lineEdit_endMZ->text().toFloat();
    tmpPARAM.WaitTimeMs= WAIT_TIME_MIN_MS;//tmpPARAM.WaitTimeMs= tmpPARAM.getWaitTimeMs(ui.lineEdit_eventTime->text().toFloat());
    tmpPARAM.ScanTimeMs= tmpPARAM.getScanTimeMs(arg1.toFloat(&ok));
    if((!ok)||(tmpPARAM.ScanTimeMs<= 0)) return;
    cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type;
    if(uiBase.UI_PB_Polarity->text()== "+"){
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS;
        }
    }else{
        if(property("currentQ").toString()=="Q1"){
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG;
        }else{
            Type= cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG;
        }
    }
    calcUseMapMZ(Type, tmpPARAM);
    uiBase.lineEdit_waitTime_rOnly->setText(QString::number(tmpPARAM.WaitTimeMs));
    float speed= tmpPARAM.getScanSpeedQ1();
    ui.lineEdit_scanSpeed_rOnly->setText(QString::number(speed));
}
