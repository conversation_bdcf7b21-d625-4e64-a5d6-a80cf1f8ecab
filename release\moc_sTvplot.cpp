/****************************************************************************
** Meta object code from reading C++ file 'sTvplot.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/LibWidget/sTvplot.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sTvplot.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TVPlot_t {
    QByteArrayData data[21];
    char stringdata0[206];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TVPlot_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TVPlot_t qt_meta_stringdata_TVPlot = {
    {
QT_MOC_LITERAL(0, 0, 6), // "TVPlot"
QT_MOC_LITERAL(1, 7, 7), // "setMode"
QT_MOC_LITERAL(2, 15, 0), // ""
QT_MOC_LITERAL(3, 16, 10), // "exportPlot"
QT_MOC_LITERAL(4, 27, 8), // "populate"
QT_MOC_LITERAL(5, 36, 19), // "std::vector<double>"
QT_MOC_LITERAL(6, 56, 7), // "ValuesX"
QT_MOC_LITERAL(7, 64, 20), // "std::vector<double>&"
QT_MOC_LITERAL(8, 85, 7), // "ValuesY"
QT_MOC_LITERAL(9, 93, 8), // "baseLine"
QT_MOC_LITERAL(10, 102, 13), // "const double*"
QT_MOC_LITERAL(11, 116, 5), // "xData"
QT_MOC_LITERAL(12, 122, 5), // "yData"
QT_MOC_LITERAL(13, 128, 4), // "size"
QT_MOC_LITERAL(14, 133, 10), // "setSamples"
QT_MOC_LITERAL(15, 144, 11), // "setZoomBase"
QT_MOC_LITERAL(16, 156, 7), // "setBase"
QT_MOC_LITERAL(17, 164, 8), // "onZoomed"
QT_MOC_LITERAL(18, 173, 4), // "rect"
QT_MOC_LITERAL(19, 178, 22), // "setQwtPlotCurveVisible"
QT_MOC_LITERAL(20, 201, 4) // "flag"

    },
    "TVPlot\0setMode\0\0exportPlot\0populate\0"
    "std::vector<double>\0ValuesX\0"
    "std::vector<double>&\0ValuesY\0baseLine\0"
    "const double*\0xData\0yData\0size\0"
    "setSamples\0setZoomBase\0setBase\0onZoomed\0"
    "rect\0setQwtPlotCurveVisible\0flag"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TVPlot[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x0a /* Public */,
       3,    0,   77,    2, 0x0a /* Public */,
       4,    3,   78,    2, 0x0a /* Public */,
       4,    2,   85,    2, 0x2a /* Public | MethodCloned */,
       4,    4,   90,    2, 0x0a /* Public */,
       4,    3,   99,    2, 0x2a /* Public | MethodCloned */,
      14,    4,  106,    2, 0x0a /* Public */,
      14,    3,  115,    2, 0x2a /* Public | MethodCloned */,
      15,    1,  122,    2, 0x0a /* Public */,
      17,    1,  125,    2, 0x0a /* Public */,
      19,    1,  128,    2, 0x0a /* Public */,
      19,    0,  131,    2, 0x2a /* Public | MethodCloned */,

 // slots: parameters
    QMetaType::Void, QMetaType::Int,    2,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5, 0x80000000 | 7, QMetaType::Double,    6,    8,    9,
    QMetaType::Void, 0x80000000 | 5, 0x80000000 | 7,    6,    8,
    QMetaType::Void, 0x80000000 | 10, 0x80000000 | 10, QMetaType::Int, QMetaType::Double,   11,   12,   13,    9,
    QMetaType::Void, 0x80000000 | 10, 0x80000000 | 10, QMetaType::Int,   11,   12,   13,
    QMetaType::Void, 0x80000000 | 10, 0x80000000 | 10, QMetaType::Int, QMetaType::Double,   11,   12,   13,    9,
    QMetaType::Void, 0x80000000 | 10, 0x80000000 | 10, QMetaType::Int,   11,   12,   13,
    QMetaType::Void, QMetaType::Bool,   16,
    QMetaType::Void, QMetaType::QRectF,   18,
    QMetaType::Void, QMetaType::Bool,   20,
    QMetaType::Void,

       0        // eod
};

void TVPlot::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TVPlot *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->setMode((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->exportPlot(); break;
        case 2: _t->populate((*reinterpret_cast< std::vector<double>(*)>(_a[1])),(*reinterpret_cast< std::vector<double>(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3]))); break;
        case 3: _t->populate((*reinterpret_cast< std::vector<double>(*)>(_a[1])),(*reinterpret_cast< std::vector<double>(*)>(_a[2]))); break;
        case 4: _t->populate((*reinterpret_cast< const double*(*)>(_a[1])),(*reinterpret_cast< const double*(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 5: _t->populate((*reinterpret_cast< const double*(*)>(_a[1])),(*reinterpret_cast< const double*(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 6: _t->setSamples((*reinterpret_cast< const double*(*)>(_a[1])),(*reinterpret_cast< const double*(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 7: _t->setSamples((*reinterpret_cast< const double*(*)>(_a[1])),(*reinterpret_cast< const double*(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 8: _t->setZoomBase((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 9: _t->onZoomed((*reinterpret_cast< const QRectF(*)>(_a[1]))); break;
        case 10: _t->setQwtPlotCurveVisible((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 11: _t->setQwtPlotCurveVisible(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TVPlot::staticMetaObject = { {
    &QwtPlot::staticMetaObject,
    qt_meta_stringdata_TVPlot.data,
    qt_meta_data_TVPlot,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TVPlot::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TVPlot::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TVPlot.stringdata0))
        return static_cast<void*>(this);
    return QwtPlot::qt_metacast(_clname);
}

int TVPlot::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QwtPlot::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
