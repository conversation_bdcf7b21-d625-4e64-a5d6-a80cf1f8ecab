#pragma once

#include <QWidget>
#include <cPublicDefine.h>
#include <cPublicCCS.h>
#include <qUiWidget.h>
#include <uiMethod/cTQ_StructCMD_HZH.h>
#include "ui_uiBaseParamEditor.h"


class uiBaseParamEditor : public qUiWidget
{
    Q_OBJECT

public:
    Ui::uiBaseParamEditor uiBase;
    explicit uiBaseParamEditor(QWidget *parent = nullptr);
    ~uiBaseParamEditor();
    virtual void initClass(QString& filePath)= 0;

protected:
    virtual bool initUI(QString& filePath)= 0;

public:
    float getLE_gating_mv(bool *ok=nullptr){
        float tmpV= uiBase.UI_LE_gating_mv->text().toFloat(ok);
        if((!ok)||(tmpV> 2500)||(tmpV< -2500)){
            *ok= false;
            return 0;
        }
        return tmpV;
    }
    float getPolarity_switch_time(bool *ok=nullptr){
        return uiBase.lineEdit_Polarity_switch_time_rOnly->text().toFloat(ok);
    }
    void setPolarity_switch_time(float PN_SwitchTimeMs){
        uiBase.lineEdit_Polarity_switch_time_rOnly->setText(QString::number(PN_SwitchTimeMs));
    }
    float getPauseTime(bool *ok=nullptr){
        return uiBase.lineEdit_pauseTime_rOnly->text().toFloat(ok);
    }
    void setPauseTime(float PauseTimeMs){
        uiBase.lineEdit_pauseTime_rOnly->setText(QString::number(PauseTimeMs));
    }
    float getWaitTime(bool *ok=nullptr){
        return uiBase.lineEdit_waitTime_rOnly->text().toFloat(ok);
    }
    void setWaitTime(float WaitTimeMs){
        uiBase.lineEdit_waitTime_rOnly->setText(QString::number(WaitTimeMs));
    }
    float getEventTime(bool *ok=nullptr){
        return uiBase.lineEdit_eventTime->text().toFloat(ok);
    }
    int getPolarity(){
        return (uiBase.UI_PB_Polarity->text()== "+")? 1: 0;
    }

    float getStartTimeMinute(){
        return uiBase.lineEdit_startTime->text().toFloat();
    }
    float getEndTimeMinute(){
        return uiBase.lineEdit_endTime->text().toFloat();
    }

private slots:
    void on_UI_PB_Polarity_clicked();
    virtual void on_lineEdit_pauseTime_textEdited(const QString &arg1)= 0;
    virtual void on_lineEdit_Polarity_switch_time_textEdited(const QString &arg1)= 0;
    virtual void on_lineEdit_eventTime_textEdited(const QString &arg1)= 0;
};

