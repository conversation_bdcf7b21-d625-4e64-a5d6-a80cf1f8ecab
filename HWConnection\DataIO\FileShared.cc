#include "FileShared.h"

using namespace UMA;

MetaInfoBase::MetaInfoBase()
{

}

MetaInfoBase::~MetaInfoBase()
{

}

uint64_t MetaInfoBase::size() const
{
    return 0ULL;
}

const void* MetaInfoBase::data() const
{
    return nullptr;
}

bool MetaInfoBase::deserialize(char* data, uint64_t size)
{
    return true;
}

uint64_t MetaInfoBase::magicId() const
{
    return MAGIC_ID_ALL_MASK;
}

DataFrameBase::DataFrameBase() :
    mMetaInfoPtr(nullptr)
{

}

DataFrameBase::~DataFrameBase()
{

}

uint64_t DataFrameBase::serialHeadSize() const
{
    if (!mMetaInfoPtr) {
        return 0ULL;
    }
    return mMetaInfoPtr->size();
}

uint64_t DataFrameBase::size() const
{
    return 0ULL;
}

const void* DataFrameBase::headData() const
{
    if (!mMetaInfoPtr) {
        return nullptr;
    }
    return mMetaInfoPtr->data();
}

const void* DataFrameBase::data() const
{
    return nullptr;
}

bool DataFrameBase::deserialize(char* data, uint64_t size)
{
    return true;
}

uint64_t DataFrameBase::magicId() const
{
    return MAGIC_ID_ALL_MASK;
}

void DataFrameBase::setDataPosition(uint64_t position)
{
    mSegementItemHead._DataPosition = position;
}

void DataFrameBase::setDataLength(uint64_t length)
{
    mSegementItemHead._DataLength = length;
}

void DataFrameBase::setMetaInfo(const std::shared_ptr<MetaInfoBase>& metaInfoPtr)
{
    mMetaInfoPtr = metaInfoPtr;
}

const std::shared_ptr<MetaInfoBase>& DataFrameBase::metaInfo() const
{
    return mMetaInfoPtr;
}

const SegmentItemHead* DataFrameBase::segmentItemHead() const
{
    return &mSegementItemHead;
}
