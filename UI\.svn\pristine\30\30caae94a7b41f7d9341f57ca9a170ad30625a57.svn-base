/* const/gsl_const_cgsm.h
 * 
 * Copyright (C) 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005,
 * 2006 <PERSON>
 * 
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 */

#ifndef __GSL_CONST_CGSM__
#define __GSL_CONST_CGSM__

#define GSL_CONST_CGSM_SPEED_OF_LIGHT (2.99792458e10) /* cm / s */
#define GSL_CONST_CGSM_GRAVITATIONAL_CONSTANT (6.673e-8) /* cm^3 / g s^2 */
#define GSL_CONST_CGSM_PLANCKS_CONSTANT_H (6.62606876e-27) /* g cm^2 / s */
#define GSL_CONST_CGSM_PLANCKS_CONSTANT_HBAR (1.05457159642e-27) /* g cm^2 / s */
#define GSL_CONST_CGSM_ASTRONOMICAL_UNIT (1.49597870691e13) /* cm */
#define GSL_CONST_CGSM_LIGHT_YEAR (9.46053620707e17) /* cm */
#define GSL_CONST_CGSM_PARSEC (3.08567758135e18) /* cm */
#define GSL_CONST_CGSM_GRAV_ACCEL (9.80665e2) /* cm / s^2 */
#define GSL_CONST_CGSM_ELECTRON_VOLT (1.602176462e-12) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_MASS_ELECTRON (9.10938188e-28) /* g */
#define GSL_CONST_CGSM_MASS_MUON (1.88353109e-25) /* g */
#define GSL_CONST_CGSM_MASS_PROTON (1.67262158e-24) /* g */
#define GSL_CONST_CGSM_MASS_NEUTRON (1.67492716e-24) /* g */
#define GSL_CONST_CGSM_RYDBERG (2.17987190389e-11) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_BOLTZMANN (1.3806503e-16) /* g cm^2 / K s^2 */
#define GSL_CONST_CGSM_BOHR_MAGNETON (9.27400899e-21) /* abamp cm^2 */
#define GSL_CONST_CGSM_NUCLEAR_MAGNETON (5.05078317e-24) /* abamp cm^2 */
#define GSL_CONST_CGSM_ELECTRON_MAGNETIC_MOMENT (9.28476362e-21) /* abamp cm^2 */
#define GSL_CONST_CGSM_PROTON_MAGNETIC_MOMENT (1.410606633e-23) /* abamp cm^2 */
#define GSL_CONST_CGSM_MOLAR_GAS (8.314472e7) /* g cm^2 / K mol s^2 */
#define GSL_CONST_CGSM_STANDARD_GAS_VOLUME (2.2710981e4) /* cm^3 / mol */
#define GSL_CONST_CGSM_MINUTE (6e1) /* s */
#define GSL_CONST_CGSM_HOUR (3.6e3) /* s */
#define GSL_CONST_CGSM_DAY (8.64e4) /* s */
#define GSL_CONST_CGSM_WEEK (6.048e5) /* s */
#define GSL_CONST_CGSM_INCH (2.54e0) /* cm */
#define GSL_CONST_CGSM_FOOT (3.048e1) /* cm */
#define GSL_CONST_CGSM_YARD (9.144e1) /* cm */
#define GSL_CONST_CGSM_MILE (1.609344e5) /* cm */
#define GSL_CONST_CGSM_NAUTICAL_MILE (1.852e5) /* cm */
#define GSL_CONST_CGSM_FATHOM (1.8288e2) /* cm */
#define GSL_CONST_CGSM_MIL (2.54e-3) /* cm */
#define GSL_CONST_CGSM_POINT (3.52777777778e-2) /* cm */
#define GSL_CONST_CGSM_TEXPOINT (3.51459803515e-2) /* cm */
#define GSL_CONST_CGSM_MICRON (1e-4) /* cm */
#define GSL_CONST_CGSM_ANGSTROM (1e-8) /* cm */
#define GSL_CONST_CGSM_HECTARE (1e8) /* cm^2 */
#define GSL_CONST_CGSM_ACRE (4.04685642241e7) /* cm^2 */
#define GSL_CONST_CGSM_BARN (1e-24) /* cm^2 */
#define GSL_CONST_CGSM_LITER (1e3) /* cm^3 */
#define GSL_CONST_CGSM_US_GALLON (3.78541178402e3) /* cm^3 */
#define GSL_CONST_CGSM_QUART (9.46352946004e2) /* cm^3 */
#define GSL_CONST_CGSM_PINT (4.73176473002e2) /* cm^3 */
#define GSL_CONST_CGSM_CUP (2.36588236501e2) /* cm^3 */
#define GSL_CONST_CGSM_FLUID_OUNCE (2.95735295626e1) /* cm^3 */
#define GSL_CONST_CGSM_TABLESPOON (1.47867647813e1) /* cm^3 */
#define GSL_CONST_CGSM_TEASPOON (4.92892159375e0) /* cm^3 */
#define GSL_CONST_CGSM_CANADIAN_GALLON (4.54609e3) /* cm^3 */
#define GSL_CONST_CGSM_UK_GALLON (4.546092e3) /* cm^3 */
#define GSL_CONST_CGSM_MILES_PER_HOUR (4.4704e1) /* cm / s */
#define GSL_CONST_CGSM_KILOMETERS_PER_HOUR (2.77777777778e1) /* cm / s */
#define GSL_CONST_CGSM_KNOT (5.14444444444e1) /* cm / s */
#define GSL_CONST_CGSM_POUND_MASS (4.5359237e2) /* g */
#define GSL_CONST_CGSM_OUNCE_MASS (2.8349523125e1) /* g */
#define GSL_CONST_CGSM_TON (9.0718474e5) /* g */
#define GSL_CONST_CGSM_METRIC_TON (1e6) /* g */
#define GSL_CONST_CGSM_UK_TON (1.0160469088e6) /* g */
#define GSL_CONST_CGSM_TROY_OUNCE (3.1103475e1) /* g */
#define GSL_CONST_CGSM_CARAT (2e-1) /* g */
#define GSL_CONST_CGSM_UNIFIED_ATOMIC_MASS (1.66053873e-24) /* g */
#define GSL_CONST_CGSM_GRAM_FORCE (9.80665e2) /* cm g / s^2 */
#define GSL_CONST_CGSM_POUND_FORCE (4.44822161526e5) /* cm g / s^2 */
#define GSL_CONST_CGSM_KILOPOUND_FORCE (4.44822161526e8) /* cm g / s^2 */
#define GSL_CONST_CGSM_POUNDAL (1.38255e4) /* cm g / s^2 */
#define GSL_CONST_CGSM_CALORIE (4.1868e7) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_BTU (1.05505585262e10) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_THERM (1.05506e15) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_HORSEPOWER (7.457e9) /* g cm^2 / s^3 */
#define GSL_CONST_CGSM_BAR (1e6) /* g / cm s^2 */
#define GSL_CONST_CGSM_STD_ATMOSPHERE (1.01325e6) /* g / cm s^2 */
#define GSL_CONST_CGSM_TORR (1.33322368421e3) /* g / cm s^2 */
#define GSL_CONST_CGSM_METER_OF_MERCURY (1.33322368421e6) /* g / cm s^2 */
#define GSL_CONST_CGSM_INCH_OF_MERCURY (3.38638815789e4) /* g / cm s^2 */
#define GSL_CONST_CGSM_INCH_OF_WATER (2.490889e3) /* g / cm s^2 */
#define GSL_CONST_CGSM_PSI (6.89475729317e4) /* g / cm s^2 */
#define GSL_CONST_CGSM_POISE (1e0) /* g / cm s */
#define GSL_CONST_CGSM_STOKES (1e0) /* cm^2 / s */
#define GSL_CONST_CGSM_FARADAY (9.6485341472e3) /* abamp s / mol */
#define GSL_CONST_CGSM_ELECTRON_CHARGE (1.602176462e-20) /* abamp s */
#define GSL_CONST_CGSM_GAUSS (1e0) /* g / abamp s^2 */
#define GSL_CONST_CGSM_STILB (1e0) /* cd / cm^2 */
#define GSL_CONST_CGSM_LUMEN (1e0) /* cd sr */
#define GSL_CONST_CGSM_LUX (1e-4) /* cd sr / cm^2 */
#define GSL_CONST_CGSM_PHOT (1e0) /* cd sr / cm^2 */
#define GSL_CONST_CGSM_FOOTCANDLE (1.076e-3) /* cd sr / cm^2 */
#define GSL_CONST_CGSM_LAMBERT (1e0) /* cd sr / cm^2 */
#define GSL_CONST_CGSM_FOOTLAMBERT (1.07639104e-3) /* cd sr / cm^2 */
#define GSL_CONST_CGSM_CURIE (3.7e10) /* 1 / s */
#define GSL_CONST_CGSM_ROENTGEN (2.58e-8) /* abamp s / g */
#define GSL_CONST_CGSM_RAD (1e2) /* cm^2 / s^2 */
#define GSL_CONST_CGSM_SOLAR_MASS (1.98892e33) /* g */
#define GSL_CONST_CGSM_BOHR_RADIUS (5.291772083e-9) /* cm */
#define GSL_CONST_CGSM_NEWTON (1e5) /* cm g / s^2 */
#define GSL_CONST_CGSM_DYNE (1e0) /* cm g / s^2 */
#define GSL_CONST_CGSM_JOULE (1e7) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_ERG (1e0) /* g cm^2 / s^2 */
#define GSL_CONST_CGSM_STEFAN_BOLTZMANN_CONSTANT (5.67039934436e-5) /* g / K^4 s^3 */
#define GSL_CONST_CGSM_THOMSON_CROSS_SECTION (6.65245853542e-25) /* cm^2 */

#endif /* __GSL_CONST_CGSM__ */
