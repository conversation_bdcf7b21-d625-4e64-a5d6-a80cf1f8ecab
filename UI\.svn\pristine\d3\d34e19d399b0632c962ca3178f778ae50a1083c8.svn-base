#ifndef CGLOBALSTRUCT_H
#define CGLOBALSTRUCT_H

#include <QMutex>
#include <QString>

struct ConfigParam{
public:
    QString getFilePathSysIni(){
        QMutexLocker locker(&mlockerSysIni);
        return mFilePathSysIni;
    }
    void setFilePathSysIni(QString filePath){
        QMutexLocker locker(&mlockerSysIni);
        mFilePathSysIni= filePath;
    }
private:
    QMutex mlockerSysIni;
    QString mFilePathSysIni;
};

class GlobalConfigParam{
public:
    static ConfigParam* getConfigParam(){
        static GlobalConfigParam insGlobalConfigParam;
        return &(insGlobalConfigParam.mConfigParam);
    }
private:
    ConfigParam mConfigParam;
    GlobalConfigParam(){}
    ~GlobalConfigParam(){}
    GlobalConfigParam(const GlobalConfigParam&){}
    GlobalConfigParam& operator=(const GlobalConfigParam&){
        static GlobalConfigParam insGlobalConfigParam;
        return insGlobalConfigParam;
    }
};


#endif // CGLOBALSTRUCT_H
