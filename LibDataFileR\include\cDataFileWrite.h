#pragma once
#include "DataFile_global.h"
#include <QFile>


class DATAFILE_EXPORT cDataFileWrite
{
public:
    cDataFileWrite();
    ~cDataFileWrite();
    /**文件是否打开*/
    bool isOpened();
    /**创建文件*/
    bool createDataFile(const QByteArray& fileHead,//in<-TIC参数头
                        QString filePath= QString()//in<-路径，若为空，默认为应用程序当前路径+"/data"
            );
    /**获取当前文件路径*/
    QString currentPath(){
        return mCurrentPath;
    }
    /**存档文件结点*/
    void backup();
    /**提取文件结点*/
    void recover();
    /**写TIC数据，注意写入的个数必须符合参数个数，若无自定义规则，标准为偏移量、时间数据、数据、XIC数据...*/
    bool writeParam(const char* pData, qint64 size);
    /**写MASS数据*/
    bool writeData(QByteArray& pData);//pData直接写入文件
    unsigned long writeData(QByteArray& pStreamBody,
                            const QByteArray& pData);//传入double数组内存结构
    bool writeData(char* pData,//pData直接写入文件
                   qint64 nLength);//pData长度
    /**关闭当前文件*/
    void closeFile();
    /**将当前文件重命名为所输入名称*/
    void renameFile(char* pName,int lengthName);
    void renameFile(QString fileName/*期望的文件名，若为空，则删除当前文件*/);
    static void renameFile(QString fileName/*期望的文件名，若为空，则删除当前文件*/,
                           QString filePath//文件所在目录
            );

private:
    bool mOpened=false;
    QString mCurrentPath;
    QFile* mDataFileParam= nullptr;
    QFile* mDataFileData= nullptr;
    qint64 mPosFileParam= 0;
    qint64 mPosFileData= 0;
    QString createFileTIC(const QByteArray& fileHead, QString filePath);
    bool createFileMass(/*QByteArray& fileHead, */QString fileName= QString());
    template <typename T>
    static void fillData(const QByteArray& pSrcData,//传入double数组内存结构
                         QByteArray& pDstData);

};

