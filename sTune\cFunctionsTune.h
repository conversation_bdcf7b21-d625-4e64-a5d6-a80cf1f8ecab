#pragma once
#include "sStaticASG.h"

#include <cPublicStructHZH.h>
using namespace libControlCCS;

class cFunctionsTune
{
public:
    cFunctionsTune();
    static bool updateASG(sStaticASG* pStaticASG,
                          QVector<quint32>& pBuffMethodASG/*, QString& paramStr*/){
        ParamCCS::_ASG_STRUCT tmpDMSIT_STRUCT;
        if(!/*pTune->getStaticASG()*/pStaticASG->createMethod(tmpDMSIT_STRUCT))
            return false;
        if(ParamCCS::ASG_Update(pBuffMethodASG, tmpDMSIT_STRUCT)!=0)
            return false;
        if(pBuffMethodASG.size()/128< 2)
            return false;
        //paramStr= mTune->getStaticASG()->getParam();
        return true;
    }

    static void fillingParamDAQ(const _STRUCT_ADC_TDC& m_STRUCT_ADC_TDC,
                                ParamCCS::_DAQ_CONFIG& pDAQ_CONFIG){
        pDAQ_CONFIG.Frq_AQ = 100000000 / m_STRUCT_ADC_TDC.Frq; //第 uDAQ_Trigger 个数据采样一次
        pDAQ_CONFIG.No_ACC = m_STRUCT_ADC_TDC.ACC;     // 累加次数
        pDAQ_CONFIG.No_AQP = 0;     // 采样点数（HCS多通道中无效），实际采样点数为:采样点数/累加次数(既uDAQ_AQP/uDAQ_Acc）
        pDAQ_CONFIG.Mode= m_STRUCT_ADC_TDC.Mode;
    }

};

