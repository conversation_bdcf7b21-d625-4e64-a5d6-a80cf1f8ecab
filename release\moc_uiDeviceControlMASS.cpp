/****************************************************************************
** Meta object code from reading C++ file 'uiDeviceControlMASS.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiManualMode/uiDeviceControl/uiDeviceControlMASS.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiDeviceControlMASS.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiDeviceControlMASS_t {
    QByteArrayData data[6];
    char stringdata0[106];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiDeviceControlMASS_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiDeviceControlMASS_t qt_meta_stringdata_uiDeviceControlMASS = {
    {
QT_MOC_LITERAL(0, 0, 19), // "uiDeviceControlMASS"
QT_MOC_LITERAL(1, 20, 33), // "on_UI_RECIPE_LIST_DCM_itemCli..."
QT_MOC_LITERAL(2, 54, 0), // ""
QT_MOC_LITERAL(3, 55, 16), // "QListWidgetItem*"
QT_MOC_LITERAL(4, 72, 4), // "item"
QT_MOC_LITERAL(5, 77, 28) // "on_UI_RECIPE_ADD_DCM_clicked"

    },
    "uiDeviceControlMASS\0"
    "on_UI_RECIPE_LIST_DCM_itemClicked\0\0"
    "QListWidgetItem*\0item\0"
    "on_UI_RECIPE_ADD_DCM_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiDeviceControlMASS[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   24,    2, 0x08 /* Private */,
       5,    0,   27,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,

       0        // eod
};

void uiDeviceControlMASS::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiDeviceControlMASS *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_UI_RECIPE_LIST_DCM_itemClicked((*reinterpret_cast< QListWidgetItem*(*)>(_a[1]))); break;
        case 1: _t->on_UI_RECIPE_ADD_DCM_clicked(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiDeviceControlMASS::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_uiDeviceControlMASS.data,
    qt_meta_data_uiDeviceControlMASS,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiDeviceControlMASS::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiDeviceControlMASS::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiDeviceControlMASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int uiDeviceControlMASS::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 2;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
