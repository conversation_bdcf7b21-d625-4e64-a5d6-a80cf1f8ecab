/****************************************************************************
** Meta object code from reading C++ file 'sChartXIC.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../LibWidget/sChartWidget/sChartXIC.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sChartXIC.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_sChartXIC_t {
    QByteArrayData data[12];
    char stringdata0[182];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_sChartXIC_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_sChartXIC_t qt_meta_stringdata_sChartXIC = {
    {
QT_MOC_LITERAL(0, 0, 9), // "sChartXIC"
QT_MOC_LITERAL(1, 10, 14), // "ChangedManager"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 16), // "on_B_Add_clicked"
QT_MOC_LITERAL(4, 43, 19), // "on_B_Modify_clicked"
QT_MOC_LITERAL(5, 63, 19), // "on_B_Remove_clicked"
QT_MOC_LITERAL(6, 83, 15), // "on_B_OK_clicked"
QT_MOC_LITERAL(7, 99, 18), // "on_Manager_clicked"
QT_MOC_LITERAL(8, 118, 16), // "QListWidgetItem*"
QT_MOC_LITERAL(9, 135, 16), // "pQListWidgetItem"
QT_MOC_LITERAL(10, 152, 9), // "isChanged"
QT_MOC_LITERAL(11, 162, 19) // "on_B_Cancel_clicked"

    },
    "sChartXIC\0ChangedManager\0\0on_B_Add_clicked\0"
    "on_B_Modify_clicked\0on_B_Remove_clicked\0"
    "on_B_OK_clicked\0on_Manager_clicked\0"
    "QListWidgetItem*\0pQListWidgetItem\0"
    "isChanged\0on_B_Cancel_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_sChartXIC[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    0,   54,    2, 0x08 /* Private */,
       4,    0,   55,    2, 0x08 /* Private */,
       5,    0,   56,    2, 0x08 /* Private */,
       6,    0,   57,    2, 0x08 /* Private */,
       7,    2,   58,    2, 0x08 /* Private */,
      11,    0,   63,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    2,    2,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 8, QMetaType::Bool,    9,   10,
    QMetaType::Void,

       0        // eod
};

void sChartXIC::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<sChartXIC *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->ChangedManager((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 1: _t->on_B_Add_clicked(); break;
        case 2: _t->on_B_Modify_clicked(); break;
        case 3: _t->on_B_Remove_clicked(); break;
        case 4: _t->on_B_OK_clicked(); break;
        case 5: _t->on_Manager_clicked((*reinterpret_cast< QListWidgetItem*(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 6: _t->on_B_Cancel_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (sChartXIC::*)(QString , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sChartXIC::ChangedManager)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject sChartXIC::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_sChartXIC.data,
    qt_meta_data_sChartXIC,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *sChartXIC::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *sChartXIC::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_sChartXIC.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int sChartXIC::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void sChartXIC::ChangedManager(QString _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
