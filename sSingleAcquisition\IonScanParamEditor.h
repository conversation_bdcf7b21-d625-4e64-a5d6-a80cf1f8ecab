﻿#pragma once

#include <uiIonScanParamEditor.h>

#include <sTune/sMapSetMZ.h>

class IonScanParamEditor : public uiIonScanParamEditor
{
public:
    IonScanParamEditor(const QString& type, sMapSetMZ* pMapSetMZ,QWidget* parent = nullptr);
    ~IonScanParamEditor(){}
    void initClass(QString& filePath);
    bool getParamEvent(cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM,
                       cTQ_StructCMD_AD81416::_CHANNEL_EVENT_PARAM& p_CHANNEL_EVENT_PARAM_LDAC,
                       int& Polarity);
    //void saveParameter() override;
private:
    sMapSetMZ* mMapSetMZ= nullptr;
    bool calcUseMapMZ(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE&,
                            cTQ_StructCMD_HZH::_CHANNEL_EVENT_PARAM&);
};


