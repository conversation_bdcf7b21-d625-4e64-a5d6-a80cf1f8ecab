/********************************************************************************
** Form generated from reading UI file 'uiCalibrationMassItem.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UICALIBRATIONMASSITEM_H
#define UI_UICALIBRATIONMASSITEM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "LibWidget/sclickwidget.h"

QT_BEGIN_NAMESPACE

class Ui_uiCalibrationMassItem
{
public:
    QHBoxLayout *horizontalLayout;
    QVBoxLayout *verticalLayout_3;
    sClickWidget *UI_W_TITLE_CAL;
    QHBoxLayout *horizontalLayout_3;
    QLabel *UI_L_Exponent_CAL;
    QComboBox *UI_CB_Exponent_CAL;
    QSpacerItem *horizontalSpacer;
    QTableWidget *UI_TW_TABLE_CAL;
    QWidget *UI_W_RIGHT_CAL;
    QVBoxLayout *verticalLayout_2;
    QSpacerItem *verticalSpacer;
    QPushButton *UI_PB_Calculate_CAL;
    QPushButton *UI_PB_APPLY_CAL;
    QPushButton *UI_PB_CANCEL_CAL;

    void setupUi(QWidget *uiCalibrationMassItem)
    {
        if (uiCalibrationMassItem->objectName().isEmpty())
            uiCalibrationMassItem->setObjectName(QString::fromUtf8("uiCalibrationMassItem"));
        uiCalibrationMassItem->resize(981, 141);
        uiCalibrationMassItem->setMinimumSize(QSize(0, 0));
        uiCalibrationMassItem->setMaximumSize(QSize(16777215, 16777215));
        horizontalLayout = new QHBoxLayout(uiCalibrationMassItem);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        UI_W_TITLE_CAL = new sClickWidget(uiCalibrationMassItem);
        UI_W_TITLE_CAL->setObjectName(QString::fromUtf8("UI_W_TITLE_CAL"));
        UI_W_TITLE_CAL->setAutoFillBackground(true);
        horizontalLayout_3 = new QHBoxLayout(UI_W_TITLE_CAL);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        UI_L_Exponent_CAL = new QLabel(UI_W_TITLE_CAL);
        UI_L_Exponent_CAL->setObjectName(QString::fromUtf8("UI_L_Exponent_CAL"));

        horizontalLayout_3->addWidget(UI_L_Exponent_CAL);

        UI_CB_Exponent_CAL = new QComboBox(UI_W_TITLE_CAL);
        UI_CB_Exponent_CAL->setObjectName(QString::fromUtf8("UI_CB_Exponent_CAL"));

        horizontalLayout_3->addWidget(UI_CB_Exponent_CAL);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer);


        verticalLayout_3->addWidget(UI_W_TITLE_CAL);

        UI_TW_TABLE_CAL = new QTableWidget(uiCalibrationMassItem);
        if (UI_TW_TABLE_CAL->rowCount() < 3)
            UI_TW_TABLE_CAL->setRowCount(3);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        UI_TW_TABLE_CAL->setVerticalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        UI_TW_TABLE_CAL->setVerticalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        UI_TW_TABLE_CAL->setVerticalHeaderItem(2, __qtablewidgetitem2);
        UI_TW_TABLE_CAL->setObjectName(QString::fromUtf8("UI_TW_TABLE_CAL"));
        UI_TW_TABLE_CAL->setMinimumSize(QSize(0, 0));
        UI_TW_TABLE_CAL->setMaximumSize(QSize(16777215, 116));

        verticalLayout_3->addWidget(UI_TW_TABLE_CAL);


        horizontalLayout->addLayout(verticalLayout_3);

        UI_W_RIGHT_CAL = new QWidget(uiCalibrationMassItem);
        UI_W_RIGHT_CAL->setObjectName(QString::fromUtf8("UI_W_RIGHT_CAL"));
        verticalLayout_2 = new QVBoxLayout(UI_W_RIGHT_CAL);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);

        UI_PB_Calculate_CAL = new QPushButton(UI_W_RIGHT_CAL);
        UI_PB_Calculate_CAL->setObjectName(QString::fromUtf8("UI_PB_Calculate_CAL"));

        verticalLayout_2->addWidget(UI_PB_Calculate_CAL);

        UI_PB_APPLY_CAL = new QPushButton(UI_W_RIGHT_CAL);
        UI_PB_APPLY_CAL->setObjectName(QString::fromUtf8("UI_PB_APPLY_CAL"));

        verticalLayout_2->addWidget(UI_PB_APPLY_CAL);

        UI_PB_CANCEL_CAL = new QPushButton(UI_W_RIGHT_CAL);
        UI_PB_CANCEL_CAL->setObjectName(QString::fromUtf8("UI_PB_CANCEL_CAL"));

        verticalLayout_2->addWidget(UI_PB_CANCEL_CAL);


        horizontalLayout->addWidget(UI_W_RIGHT_CAL);


        retranslateUi(uiCalibrationMassItem);

        QMetaObject::connectSlotsByName(uiCalibrationMassItem);
    } // setupUi

    void retranslateUi(QWidget *uiCalibrationMassItem)
    {
        uiCalibrationMassItem->setWindowTitle(QApplication::translate("uiCalibrationMassItem", "Form", nullptr));
        UI_L_Exponent_CAL->setText(QApplication::translate("uiCalibrationMassItem", "  Exponent:", nullptr));
        QTableWidgetItem *___qtablewidgetitem = UI_TW_TABLE_CAL->verticalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("uiCalibrationMassItem", "Target Mass", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = UI_TW_TABLE_CAL->verticalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("uiCalibrationMassItem", "Current Mass", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = UI_TW_TABLE_CAL->verticalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("uiCalibrationMassItem", "Correction", nullptr));
        UI_PB_Calculate_CAL->setText(QApplication::translate("uiCalibrationMassItem", "Calculate", nullptr));
        UI_PB_APPLY_CAL->setText(QApplication::translate("uiCalibrationMassItem", "Apply", nullptr));
        UI_PB_CANCEL_CAL->setText(QApplication::translate("uiCalibrationMassItem", "Cancel", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiCalibrationMassItem: public Ui_uiCalibrationMassItem {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UICALIBRATIONMASSITEM_H
