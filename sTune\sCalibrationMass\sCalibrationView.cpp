#include "sCalibrationView.h"
#include <QSettings>

sCalibrationView::sCalibrationView(cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type,//QString pro,
                                   sMapSetMZ* pMapSetMZ,
                                   QWidget *parent) :
    uiCalibrationView(Type, pMapSetMZ, parent)
{

}

sCalibrationView::~sCalibrationView()
{
    if(mTimerRefreshID!=-1)
        killTimer(mTimerRefreshID);
    mTimerRefreshID=-1;
    if(mAnalyzeThread){
        if(mAnalyzeThread->isRunning()){
            mAnalyzeThread->stop();
            mAnalyzeThread->wait();
        }
        delete mAnalyzeThread;
        mAnalyzeThread= nullptr;
    }
}

void sCalibrationView::initClass(QString& filePath)
{
    QString strPro, strPolarity;
    switch(mType){
    case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
        strPro="Q1";
        strPolarity="+";
        break;
    case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:
        strPro="Q1";
        strPolarity="-";
        break;
    case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
        strPro="Q3";
        strPolarity="+";
        break;
    case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:
        strPro="Q3";
        strPolarity="-";
        break;
    default: break;
    }
    mQ13ScanParamEditor= new Q13ScanParamEditor(strPro,
                                                (sMapSetMZ*)mMapSetMZ,
                                                this);
    mQ13ScanParamEditor-> initClass(filePath);
    initUI(filePath);
    mQ13ScanParamEditor->setPolarity(strPolarity, true);
    //mChart = new sChartWidget(sChartWidget::_TUNING_CHART);
    m_pMsEvtSplitter= new MassEventSplitter();
    mAnalyzeThread= new SThread(this);
    mAnalyzeThread->setUserFunction(analyzeThread);
    mAnalyzeThread->setUserParam(this);
    QObject::connect(this,SIGNAL(sUpdateGraphMass(int, quint32)),this,SLOT(onUpdateGraphMass(int, quint32)),Qt::QueuedConnection);
    connect(ui.UI_PB_ADDMASS_CAL, &QPushButton::clicked, this, &sCalibrationView::onUI_PB_ADDMASS_CAL_clicked);
    connect(ui.UI_PB_DELETEMASS_CAL, &QPushButton::clicked, this, &sCalibrationView::onUI_PB_DELETEMASS_CAL_clicked);
}

void sCalibrationView::onUI_PB_ADDMASS_CAL_clicked()
{
    QString str= ui.UI_LE_SPEED_CAL->text();
    bool ok=false;
    double speed= str.toDouble(&ok);
    if(!ok)
        return;
    sCalibrationMassItem* pCalibrationMassItem= new sCalibrationMassItem(this);
    pCalibrationMassItem->setScanSpeed(str);
    connect(pCalibrationMassItem, SIGNAL(focused(QString)), this, SLOT(onFocused(QString)));
    mCalibrationMassItemMap.insert(speed, pCalibrationMassItem);
    for(auto CalibrationMassItem: mCalibrationMassItemMap){
        if(CalibrationMassItem){
            ui.UI_LAYOUT_SPEEDMAP_CAL->insertWidget(0, CalibrationMassItem);
        }
    }
}

void sCalibrationView::onUI_PB_DELETEMASS_CAL_clicked()
{
    if(!mCurrentFocus)
        return;
    for(auto CalibrationMassItem: mCalibrationMassItemMap){
        if(CalibrationMassItem){
            ui.UI_LAYOUT_SPEEDMAP_CAL->removeWidget(CalibrationMassItem);
            disconnect(CalibrationMassItem, nullptr, nullptr, nullptr);
            delete CalibrationMassItem;
            CalibrationMassItem= nullptr;
            return;
        }
    }
}
