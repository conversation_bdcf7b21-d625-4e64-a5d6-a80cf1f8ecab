#include "sStaticASG.h"
#include "ui_uiStaticASG.h"
#include <QDebug>

sStaticASG::sStaticASG(QWidget *parent) : uiStaticASG(parent)
{
}

sStaticASG::~sStaticASG()
{
}

void sStaticASG::initClass(QString &filePath)
{
    initUI(filePath);
}

bool sStaticASG::createMethod(libControlCCS::ParamCCS::_ASG_STRUCT &pDMSIT_STRUCT)
{
#if 0
    pDMSIT_STRUCT.pStructDAC.clear();
    ParamCCS::DMSIT_DAC_STRUCT tmpDMSIT_DAC_STRUCT;
    for (int i=0;i<8;++i)
        tmpDMSIT_DAC_STRUCT.MaskDAC[i]=0xFFFFF;
    //tmpDMSIT_DAC_STRUCT.MaskDAC[0]=0xff;
    tmpDMSIT_DAC_STRUCT.HoldTime=10;
    pDMSIT_STRUCT.pStructDAC.push_back(tmpDMSIT_DAC_STRUCT);
    if(!getParamASG1(pDMSIT_STRUCT))
        return false;
    if(!getParamASG2(pDMSIT_STRUCT))
        return false;
    if(!getParamASG3(pDMSIT_STRUCT))
        return false;
    if(!getParamASG4(pDMSIT_STRUCT))
        return false;

    for (int i= 0; i< pDMSIT_STRUCT.pStructDAC.size(); i++){
        for(int j=0;j<160;j++){
            pDMSIT_STRUCT.pStructDAC[i].DacValue[j]= (pDMSIT_STRUCT.pStructDAC[i].DacValue[j]-pDMSIT_STRUCT.DacOffset[j])/pDMSIT_STRUCT.DacGain[j];
            if(pDMSIT_STRUCT.pStructDAC[i].DacValue[j]-10.0>0.0000001|| pDMSIT_STRUCT.pStructDAC[i].DacValue[j]+10.0<-0.0000001){
                qDebug()<<"ERROR-DMSIT-OVER RANGE";
                return false;
            }
        }
    }
    pDMSIT_STRUCT.MethodValue=0;
#endif
    return true;
}
