#ifndef uiStaticASG_H
#define uiStaticASG_H

#include "ui_uiStaticASG.h"
#include <QTableWidgetItem>
#include <cGlobalStruct.h>
#include <qUiWidget.h>
//#include <USBConnection/cParamCCS.h>

class uiStaticASG : public qUiWidget
{
    Q_OBJECT
public:
    explicit uiStaticASG(QWidget *parent = nullptr);
    ~uiStaticASG();
    void initClass(QString& filePath);
    void getParam(QString& strParam, QTableWidget* pTableWidget);
    QString getParam();
    bool setParam(QString& strParam, QTableWidget* pTableWidget);
    bool setParam(QString strParam);
//    bool getParamASG1(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT);
//    bool getParamASG2(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT);
//    bool getParamASG3(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT);
//    bool getParamASG4(ParamCCS::_ASG_STRUCT& pDMSIT_STRUCT);

private slots:
    void on_UI_PB_SAVE_ASG_clicked(){
        ui.UI_PB_SAVE_ASG->setEnabled(false);
        QString filePath= GlobalConfigParam::getConfigParam()->getFilePathSysIni();
        saveIniToFile(filePath);
        ui.UI_PB_SAVE_ASG->setEnabled(true);
    }

protected:
    Ui::uiStaticASG ui;
    bool initUI(QString& filePath);
    bool saveIniToFile(QString& filePath);
    bool loadIniFromFile(QString& filePath, bool updateUI= false);
    void titleUpdate(QString strTitle);

};

#endif // uiStaticASG_H
