#include "uiCalibrationMassItem.h"
#include <QMenu>
#include "cMathHZH.h"

uiCalibrationMassItem::uiCalibrationMassItem(QWidget *parent) :
    QWidget(parent)
{
    ui.setupUi(this);
    connect(ui.UI_W_TITLE_CAL,SIGNAL(clicked()),this,SLOT(onClicked()));
    ui.UI_TW_TABLE_CAL->horizontalHeader()->setVisible(false);
    ui.UI_TW_TABLE_CAL->setColumnCount(0);
    ui.UI_TW_TABLE_CAL->setContextMenuPolicy(Qt::CustomContextMenu);
    popMenu= new QMenu(ui.UI_TW_TABLE_CAL);

    QAction* action= new QAction("Add Mass Front", this);
    connect(action, SIGNAL(triggered()), this, SLOT(onAddFront()));
    popMenu->addAction(action);

    action= new QAction("Add Mass Behind", this);
    connect(action, SIGNAL(triggered()), this, SLOT(onAddBehind()));
    popMenu->addAction(action);

    action= new QAction("Remove Mass", this);
    connect(action, SIGNAL(triggered()), this, SLOT(onRemove()));
    popMenu->addAction(action);

    QStringList str_list;
    str_list<<"1"<<"2"<<"3"<<"4"<<"5"<<"6";
    ui.UI_CB_Exponent_CAL->addItems(str_list);
}

uiCalibrationMassItem::~uiCalibrationMassItem()
{
    if(popMenu)
        delete popMenu;
    popMenu= nullptr;
}

bool uiCalibrationMassItem::setParam(QString& str)
{
    QStringList tmpList= str.split("  ");
    if(tmpList.size()!= 5)
        return false;
    if(!mPARAM_FIT.splitCalibrat(tmpList[3]))
        return false;
    int sizeData= mPARAM_FIT.actualValues.size();
    if(sizeData!= mPARAM_FIT.predictedValues.size())
        return false;
    QVector<double> actualValues(sizeData), cal(sizeData);
    for(int i=0; i< sizeData; i++)
        actualValues[i]= mPARAM_FIT.actualValues[i].toDouble();
    mPARAM_FIT.calibrarionF(actualValues.data(), cal.data(), sizeData);
    QString Formula= _CONGIG_OMS::_PARAM_FIT::getFormula(mPARAM_FIT.coefF);
    if(Formula.isEmpty())
        return false;

    mSpeed= tmpList[0];
    QString name= QString("ScanSpeed:%1  MassRang:%2-%3  Formula:%4  R2:%5  Exponent:")
            .arg(tmpList[0]).arg(tmpList[1]).arg(tmpList[2]).arg(Formula).arg(tmpList[4]);
    setName(name);

//    if(mPARAM_FIT.enable)
//        ui.UI_CB_ENABLE_CAL->setChecked(true);
//    else
//        ui.UI_CB_ENABLE_CAL->setChecked(false);

    ui.UI_TW_TABLE_CAL->clear();
    ui.UI_TW_TABLE_CAL->setRowCount(3);
    ui.UI_TW_TABLE_CAL->setColumnCount(sizeData);
    QStringList verticalLabels;
    verticalLabels << tr("Predicted Mass")<< tr("Actual Mass")<< tr("Correction");
    ui.UI_TW_TABLE_CAL->setVerticalHeaderLabels(verticalLabels);
    for(int i=0; i< sizeData; ++i){
        ui.UI_TW_TABLE_CAL->setItem(0, i, new QTableWidgetItem(mPARAM_FIT.predictedValues[i]));
        ui.UI_TW_TABLE_CAL->setItem(1, i, new QTableWidgetItem(mPARAM_FIT.actualValues[i]));
        ui.UI_TW_TABLE_CAL->setItem(2, i, new QTableWidgetItem(QString::number(cal[i])));
    }
    ui.UI_CB_Exponent_CAL->setCurrentText(QString::number(mPARAM_FIT.ployD));
    return true;
}

QString uiCalibrationMassItem::getParam(_CONGIG_OMS::_PARAM_FIT &pPARAM_FIT)
{
    //if(ui.UI_CB_ENABLE_CAL->isChecked())
        pPARAM_FIT.enable= 1;
//    else
//        pPARAM_FIT.enable= 0;
    pPARAM_FIT.ployD=ui.UI_CB_Exponent_CAL->currentText().toInt();
    int sizeData= ui.UI_TW_TABLE_CAL->columnCount();
    pPARAM_FIT.predictedValues.resize(sizeData);
    pPARAM_FIT.actualValues.resize(sizeData);
    for(int i=0; i< sizeData; ++i){
        pPARAM_FIT.predictedValues[i]= ui.UI_TW_TABLE_CAL->item(0, i)->text();
        pPARAM_FIT.actualValues[i]= ui.UI_TW_TABLE_CAL->item(1, i)->text();
    }
    return pPARAM_FIT.comboCalibrat();
}

void uiCalibrationMassItem::on_UI_PB_APPLY_CAL_clicked()
{

}

void uiCalibrationMassItem::on_UI_PB_CANCEL_CAL_clicked()
{

}

void uiCalibrationMassItem::on_UI_TW_TABLE_CAL_customContextMenuRequested(const QPoint &pos)
{
    if(!popMenu)
        return;
    popMenu->exec(QCursor::pos());
}

bool uiCalibrationMassItem::calculate()
{
    _CONGIG_OMS::_PARAM_FIT tmpPARAM_FIT;
    getParam(tmpPARAM_FIT);

    std::vector<double> predictedValues;

    std::vector<double> correction/*(sizeData)*/;
    QString Formula;//= _CONGIG_OMS::_PARAM_FIT::getFormula(mCoefF);

    if(!calcFun(tmpPARAM_FIT, predictedValues, correction, Formula, mR2))
        return false;
    QString name= QString("ScanSpeed:%1  MassRang:%2-%3  Formula:%4  R2:%5  Exponent:")
            .arg(mSpeed).arg(predictedValues[0]).arg(predictedValues[correction.size()- 1]).arg(Formula).arg(mR2);
    setName(name);
    for(int i=0; i< correction.size(); ++i){
        ui.UI_TW_TABLE_CAL->item(2, i)->setText(QString::number(correction[i]));
    }

    return true;
}

void uiCalibrationMassItem::on_UI_PB_Calculate_CAL_clicked()
{
    if(!calculate())
        return;
}

void uiCalibrationMassItem::on_UI_TW_TABLE_CAL_clicked(const QModelIndex &index)
{
    emit focused(mSpeed);
}
