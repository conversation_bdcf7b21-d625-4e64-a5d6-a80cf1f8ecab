#ifndef cDataFit_H
#define cDataFit_H

#include <vector>
#include <map>
#include <gsl/include/gsl/gsl_fit.h>
#include <gsl/include/gsl/gsl_cdf.h>    /* 提供了 gammaq 函数 */
#include <gsl/include/gsl/gsl_vector.h> /* 提供了向量结构*/
#include <gsl/include/gsl/gsl_matrix.h>
#include <gsl/include/gsl/gsl_multifit.h>
#include <gsl/include/gsl/gsl_poly.h>

namespace libMatrix{
class cDataFit
{
public:
    cDataFit();
    ~cDataFit();
public:
    static int polyfit(const double *x
                       ,const double *y
                       ,size_t xyLength
                       ,unsigned poly_n
                       ,std::vector<double>& out_factor
                       ,double& out_chisq);//拟合曲线与数据点的优值函数最小值 ,χ2 检验
    static int polyfit(const std::vector<double>& x
                       ,const std::vector<double>& y
                       ,unsigned poly_n
                       ,std::vector<double>& out_factor
                       ,double& out_chisq);
    ///
    /// \brief 获取斜率
    /// \return 斜率值
    ///
    double getSlope();
    ///
    /// \brief 获取截距
    /// \return 截距值
    ///
    double getIntercept();
    ///
    /// \brief 均方根误差
    /// \return 均方根误差
    ///
    double getRMSE();
    ///
    /// \brief 确定系数，系数是0~1之间的数，是数理上判定拟合优度的一个量
    /// \return 确定系数
    ///
    double getR_square();
    ///
    /// \brief 获取两个vector的安全size
    /// \return 最小的一个长度
    ///
    size_t getSeriesLength(const std::vector<double>& x, const std::vector<double>& y);
    ///
    /// \brief 计算均值
    /// \return 均值
    ///
    static double Mean(const std::vector<double>& v);
    static double Mean(const double* v,size_t length);
    ///
    /// \brief 根据x获取拟合方程的y值
    /// \return 返回x对应的y值
    ///
    double getY(const double x) const;
    bool linearFit(const std::vector<double>& x, const std::vector<double>& y);
    bool linearFit(const double* x, const double* y,size_t length,bool isSaveFitYs=false);

private:
    std::vector<double> m_factor; ///<拟合后的方程系数  //std::map<double,double> m_factor;//记录各个点的系数，key中0是0次方，1是1次方，value是对应的系数
    std::map<double,double> m_err;
    //std::vector<double> fitedYs;///<存放拟合后的y值，在拟合时可设置为不保存节省内存
    double m_cov;//相关度
    double m_ssr;//回归平方和
    double m_sse;//(剩余平方和)
    double m_rmse;//RMSE均方根误差
    double m_wssr;
    double m_goodness;//基于wssr的拟合优度
    void calcError(const double* x
                   ,const double* y
                   ,size_t length
                   ,double& r_ssr
                   ,double& r_sse
                   ,double& r_rmse
                   ,bool isSaveFitYs=true);
};
}
#endif // cDataFit_H
