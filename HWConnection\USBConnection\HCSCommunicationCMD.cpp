﻿#include "HCSCommunicationCMD.h"
#include <QElapsedTimer>

//#include "UMAStructure/UMA_HCS_Structure.h"

#define REG_DAC_OFS	(0xffff >> 3)
#define CCS_SYS_FREQENCY_HZ	100000000

//using namespace UMA_HCS;

HCSCommunicationCMD::HCSCommunicationCMD(QObject *parent) : CommunicationCMD(parent),
    mDaqPacket(16)
{
    mThreadDAQ.setUserParam(this);
    mThreadDAQ.setUserFunction(HcsDaqThread);
}

HCSCommunicationCMD::~HCSCommunicationCMD()
{
    clearBuffer();
}

bool HCSCommunicationCMD::getDataDAQ(HCSCommunicationCMD::HCSDaqPacket *packet)
{
    if (packet == nullptr || mDaqPacket.empty()){
        return false;
    }

    HCSDaqPacket* tmpPacket = nullptr;
    if(!mDaqPacket.pop(tmpPacket) || tmpPacket == nullptr){
        return false;
    }

    *packet = *tmpPacket;
    delete tmpPacket;
    return true;
}

void HCSCommunicationCMD::clearBuffer()
{
    while(!mDaqPacket.empty())
    {
        HCSDaqPacket* pack = nullptr;
        if(mDaqPacket.pop(pack))
        {
            if (pack != nullptr)
            {
                delete pack;
            }
        }
    }
}

int HCSCommunicationCMD::HcsDaqThread(void *pParam, const bool &bRunning)
{
    SINFO("N_CommunicationCMD_daqThread_start");
    HCSCommunicationCMD* pCmd = reinterpret_cast<HCSCommunicationCMD*>(pParam);
    if(!pCmd)
        return -1;
    int uAccNumber = static_cast<int>(pCmd->m_DAQ_CONFIG.No_ACC);
    if(uAccNumber <= 0){
        SINFO("E_CommunicationCMD_daqThread_ACC");return -1;
    }

    QVector<uint32_t> arrCmd;   //DAQ_Request指令缓存
    QVector<uint32_t> recvBuffer;   //DAQ_Request回包缓存
    libControlCCS::ParamCCS::DAQ_Request(arrCmd, 1);//生成DAQ_Request指令

    uint uFrameCount = 1;
    uint uPacketCount = 1;
    bool isSameFrame=true;
    bool isRightPacket=true;

    uint uRecvBytes =0;
    uint uLeftBytes =512;
    uint* pLastPkt = nullptr;
    QElapsedTimer tmEmptyPkt;
    tmEmptyPkt.start();

    bool isGet = false;
    pCmd->mCurrentPacket = new HCSDaqPacket;
    pCmd->mCurrentPacket->data.clear();
    while(bRunning){//forever{
        uRecvBytes =0;
        recvBuffer.resize(static_cast<int>(arrCmd[7] * 128));
        uLeftBytes = arrCmd[7] * 512;
        if(pCmd->mSentDaqStop)
            break;
        //SDBG("N_HCSCommunicationCMD_DAQ_Request_nPacket_W:"<< arrCmd[7]);
        if(!pCmd->mComm.writeForBack(arrCmd.data(), 512,
                                     recvBuffer.data(), uLeftBytes,
                                     &uRecvBytes, arrCmd[7]*500)){
            if(!pCmd->mSentDaqStop)
                SINFO("E_CommunicationCMD_daqThread_OVERTIME");
            while(!pCmd->mSentDaqStop){
                uLeftBytes= 512;
                libControlCCS::ParamCCS::DAQ_Request(arrCmd, 1);//生成DAQ_Request指令
                if(pCmd->mComm.writeForBack(arrCmd.data(), 512, recvBuffer.data(), uLeftBytes, &uRecvBytes, arrCmd[7]*500))
                    break;
                QThread::msleep(1);
            }
            SINFO("E_CommunicationCMD_writeForBack_OVERTIME");
            continue;
        }
        //SDBG("N_HCSCommunicationCMD_DAQ_Request_nPacket_R:"<< arrCmd[7]);
        if(pCmd->mSentDaqStop)
            break;
        if(uRecvBytes!= uLeftBytes){
            SINFO("E_CommunicationCMD_daqThread_uRecvBytes:" << uRecvBytes<< " uLeftBytes:"<<uLeftBytes);break;
        }
        pLastPkt = &(recvBuffer[uRecvBytes / 4 - 128]);// offset to last package
        if(pLastPkt[2] & 0x00010000){
            if(tmEmptyPkt.elapsed()>500 && pCmd->mSentDaqStop){
                SINFO("E_CommunicationCMD_daqThread_StopOvertime");break;
            }
            QThread::usleep(1);
        }else{
            //SDBG("HCSCommunicationCMD::HcsDaqThread empty");
            if(!pCmd->daqGetData(recvBuffer, pLastPkt, uFrameCount, uPacketCount,
                           isSameFrame, isRightPacket, isGet))
                break;
        }
//        SDBG("HCSCommunicationCMD::HcsDaqThread FRAME,PKT,PKT6:"
//              << pLastPkt[3]<<" "<<pLastPkt[4]<<" "<<pLastPkt[6]);
        if(pLastPkt[6] > 1){//FPGA中剩余包数
            arrCmd[7] = (pLastPkt[6] > 3000) ? 3000 : (pLastPkt[6] - 1);
        }else{
            arrCmd[7] = 1;
        }
        libControlCCS::ParamCCS::DAQ_Request(arrCmd, arrCmd[7]);//生成DAQ_Request指令
    }
    pCmd->clearBuffer();
    SINFO("N_CommunicationCMD_daqThread_Over");
    return 0;
}

bool HCSCommunicationCMD::daqGetData(QVector<uint> &recvBuffer, uint *pLastPkt,
                                     uint &uFrameCount, uint &uPacketCount,
                                     bool &isSameFrame, bool &isRightPacket, bool &isGet)
{
    isGet = false;
    for(uint* pIndexPkt = recvBuffer.data(); pIndexPkt <= pLastPkt; pIndexPkt += 128){//,uPacketCount++
        if(pIndexPkt[5] == 0x00010001){// if there is not more package, this process is ended.
            SINFO("N_CommunicationCMD_daqGetData_lastPackage");
            return false;//goto OUT;
        }

        if(pIndexPkt[4]== 1){//first packet
            uPacketCount= pIndexPkt[4];
            uFrameCount= pIndexPkt[3];
            isSameFrame= true;
            isRightPacket=true;
        }else{
            uPacketCount++;
        }

        if(pIndexPkt[5] & 0x1){//last packet
            if(uFrameCount!= pIndexPkt[3]){//errorFrame
                //SINFO("W_CommunicationCMD_daqGetData_errorFrame");
                SDBG("errorFrame: uFrameCount!=pIndexPkt[3],uPacketCount,pIndexPkt[4]"
                        <<uFrameCount<<pIndexPkt[3]<<uPacketCount<<pIndexPkt[4]);
                isSameFrame= false;break;
            }
            if(uPacketCount!= pIndexPkt[4]){//errorPacket
                //SDBG1("W_CommunicationCMD_daqGetData_errorPacket");
                SDBG("errorPacket: uFrameCount,pIndexPkt[3],uPacketCount!=pIndexPkt[4]"
                        <<uFrameCount<<pIndexPkt[3]<<uPacketCount<<pIndexPkt[4]);
                isRightPacket= false;break;
            }
            // no valid number check with HCS
            if(isSameFrame && isRightPacket){
                const uint64_t lSize = mCurrentPacket->data.size();
                const uint64_t lPacketPoint = pIndexPkt[5] >> 8 & 0xFFULL;

                for(uint64_t lPoint = 0; lPoint < lPacketPoint; ++lPoint)
                {
                    mCurrentPacket->data.push_back(*(pIndexPkt + 7 + lPoint));
                }
                mCurrentPacket->frameId = uFrameCount;
                mDaqPacket.push(mCurrentPacket);
                mCurrentPacket = new HCSDaqPacket;
                mCurrentPacket->data.clear();
                isGet = true;
            }else
                SINFO("W_CommunicationCMD_daqGetData_lostFrame");
        }else{
            if(uFrameCount!= pIndexPkt[3]){//errorFrame
                //SDBG1("W_CommunicationCMD_daqGetData_errorFrame");
                SDBG("errorFrame: uFrameCount!=pIndexPkt[3],uPacketCount,pIndexPkt[4]"
                        <<uFrameCount<<pIndexPkt[3]<<uPacketCount<<pIndexPkt[4]);
                isSameFrame= false;break;
            }
            if(uPacketCount!= pIndexPkt[4]){//errorPacket
                //SDBG1("W_CommunicationCMD_daqGetData_errorPacket");
                SDBG("errorPacket: uFrameCount,pIndexPkt[3],uPacketCount!=pIndexPkt[4]"
                        <<uFrameCount<<pIndexPkt[3]<<uPacketCount<<pIndexPkt[4]);
                isRightPacket= false;break;
            }

            const uint64_t lSize = mCurrentPacket->data.size();
            const uint64_t lPacketPoint = 120;
            for(uint64_t lPoint = 0; lPoint < lPacketPoint; ++lPoint){
                mCurrentPacket->data.push_back(*(pIndexPkt + 7 + lPoint));
            }
        }
    }
    return true;
}
