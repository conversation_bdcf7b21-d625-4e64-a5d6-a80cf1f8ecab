#pragma once

#include <QMainWindow>
#include <uiMainWindow/uiWindowMenu.h>
#include <QtScript/QScriptEngine>
#include "ui_uiMainWindow.h"
#include "uiInitMainWindow.h"
#include "uiSystem.h"
#include "uiSingleAcquisition.h"
#include "uiBatch.h"
#include "uiQueue.h"
#include "uiTune.h"
#include "uiState.h"

class uiInitMainWindow;
class uiMainWindow : public qUiWidget
{
    Q_OBJECT

public:

    uiMainWindow(QWidget *parent = nullptr);
    ~uiMainWindow();
    virtual void initClass(QString& filePath);
    friend class uiInitMainWindow;
//    static int m_logBeginID; /**< The begin ID of log */
//    static sSafeData<QList<QString> > m_logs; /**< Log list */
    void setCurrentWindow(QWidget* pWidget);
//    static void msgHandler(QtMsgType type, const QMessageLogContext &content, const QString &msg);
//    static QStringList logs(int& nBeginID);
    QScriptEngine mScriptEngine;
    uiSystem* mSystem= nullptr;
    uiSingleAcquisition* mSingleAcquisition= nullptr;
    uiBatch* mBatch= nullptr;
    uiQueue* mQueue= nullptr;
    uiTune* mTune= nullptr;
    uiState* mState= nullptr;
    static int m_logBeginID; /**< The begin ID of log */
    static sSafeData<QList<QString> > m_logs; /**< Log list */
    static void msgHandler(QtMsgType type, const QMessageLogContext &content, const QString &msg);
    static QStringList logs(int& nBeginID);

protected slots:
    void closeEvent ( QCloseEvent * e );

public slots:
    void onSubmit();

protected:
    Ui::uiMainWindow ui;
    uiInitMainWindow* mInitMainWindow= nullptr;

    bool initUI(QString& filePath);
    virtual void createToolBar();
};
