/****************************************************************************
** Meta object code from reading C++ file 'sChartWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../LibWidget/sChartWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sChartWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_sChartWidget_t {
    QByteArrayData data[29];
    char stringdata0[302];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_sChartWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_sChartWidget_t qt_meta_stringdata_sChartWidget = {
    {
QT_MOC_LITERAL(0, 0, 12), // "sChartWidget"
QT_MOC_LITERAL(1, 13, 13), // "CommandSignal"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 13), // "sChartWidget*"
QT_MOC_LITERAL(4, 42, 8), // "selected"
QT_MOC_LITERAL(5, 51, 9), // "clearLine"
QT_MOC_LITERAL(6, 61, 8), // "onAddXIC"
QT_MOC_LITERAL(7, 70, 8), // "uint32_t"
QT_MOC_LITERAL(8, 79, 5), // "event"
QT_MOC_LITERAL(9, 85, 4), // "mass"
QT_MOC_LITERAL(10, 90, 9), // "massRange"
QT_MOC_LITERAL(11, 100, 5), // "color"
QT_MOC_LITERAL(12, 106, 4), // "Gain"
QT_MOC_LITERAL(13, 111, 6), // "Offset"
QT_MOC_LITERAL(14, 118, 11), // "crossThread"
QT_MOC_LITERAL(15, 130, 11), // "onRemoveXIC"
QT_MOC_LITERAL(16, 142, 15), // "onShowPlotCurve"
QT_MOC_LITERAL(17, 158, 4), // "show"
QT_MOC_LITERAL(18, 163, 14), // "onTitleClicked"
QT_MOC_LITERAL(19, 178, 10), // "exportPlot"
QT_MOC_LITERAL(20, 189, 10), // "onScalDown"
QT_MOC_LITERAL(21, 200, 8), // "onScalUp"
QT_MOC_LITERAL(22, 209, 12), // "onSelectFile"
QT_MOC_LITERAL(23, 222, 7), // "setPlot"
QT_MOC_LITERAL(24, 230, 14), // "on_ShowManager"
QT_MOC_LITERAL(25, 245, 7), // "on_hide"
QT_MOC_LITERAL(26, 253, 10), // "onSelected"
QT_MOC_LITERAL(27, 264, 18), // "showCoordinateMask"
QT_MOC_LITERAL(28, 283, 18) // "hideCoordinateMask"

    },
    "sChartWidget\0CommandSignal\0\0sChartWidget*\0"
    "selected\0clearLine\0onAddXIC\0uint32_t\0"
    "event\0mass\0massRange\0color\0Gain\0Offset\0"
    "crossThread\0onRemoveXIC\0onShowPlotCurve\0"
    "show\0onTitleClicked\0exportPlot\0"
    "onScalDown\0onScalUp\0onSelectFile\0"
    "setPlot\0on_ShowManager\0on_hide\0"
    "onSelected\0showCoordinateMask\0"
    "hideCoordinateMask"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_sChartWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    3,  104,    2, 0x06 /* Public */,
       4,    1,  111,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,  114,    2, 0x0a /* Public */,
       6,    7,  115,    2, 0x0a /* Public */,
       6,    6,  130,    2, 0x2a /* Public | MethodCloned */,
      15,    2,  143,    2, 0x0a /* Public */,
      16,    1,  148,    2, 0x0a /* Public */,
      18,    0,  151,    2, 0x0a /* Public */,
      19,    0,  152,    2, 0x0a /* Public */,
      20,    0,  153,    2, 0x0a /* Public */,
      21,    0,  154,    2, 0x0a /* Public */,
      22,    2,  155,    2, 0x0a /* Public */,
      23,    0,  160,    2, 0x0a /* Public */,
      24,    0,  161,    2, 0x0a /* Public */,
      25,    0,  162,    2, 0x0a /* Public */,
      26,    1,  163,    2, 0x0a /* Public */,
      27,    0,  166,    2, 0x0a /* Public */,
      28,    0,  167,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, 0x80000000 | 3, QMetaType::VoidStar,    2,    2,    2,
    QMetaType::Void, QMetaType::Double,    2,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 7, QMetaType::QString, QMetaType::Double, QMetaType::UInt, QMetaType::Double, QMetaType::Double, QMetaType::Bool,    8,    9,   10,   11,   12,   13,   14,
    QMetaType::Void, 0x80000000 | 7, QMetaType::QString, QMetaType::Double, QMetaType::UInt, QMetaType::Double, QMetaType::Double,    8,    9,   10,   11,   12,   13,
    QMetaType::Void, 0x80000000 | 7, QMetaType::QString,    8,    9,
    QMetaType::Void, QMetaType::Bool,   17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    2,    2,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPointF,    2,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void sChartWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<sChartWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->CommandSignal((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< sChartWidget*(*)>(_a[2])),(*reinterpret_cast< void*(*)>(_a[3]))); break;
        case 1: _t->selected((*reinterpret_cast< const double(*)>(_a[1]))); break;
        case 2: _t->clearLine(); break;
        case 3: _t->onAddXIC((*reinterpret_cast< uint32_t(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3])),(*reinterpret_cast< uint(*)>(_a[4])),(*reinterpret_cast< double(*)>(_a[5])),(*reinterpret_cast< double(*)>(_a[6])),(*reinterpret_cast< bool(*)>(_a[7]))); break;
        case 4: _t->onAddXIC((*reinterpret_cast< uint32_t(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3])),(*reinterpret_cast< uint(*)>(_a[4])),(*reinterpret_cast< double(*)>(_a[5])),(*reinterpret_cast< double(*)>(_a[6]))); break;
        case 5: _t->onRemoveXIC((*reinterpret_cast< uint32_t(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 6: _t->onShowPlotCurve((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->onTitleClicked(); break;
        case 8: _t->exportPlot(); break;
        case 9: _t->onScalDown(); break;
        case 10: _t->onScalUp(); break;
        case 11: _t->onSelectFile((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 12: _t->setPlot(); break;
        case 13: _t->on_ShowManager(); break;
        case 14: _t->on_hide(); break;
        case 15: _t->onSelected((*reinterpret_cast< const QPointF(*)>(_a[1]))); break;
        case 16: _t->showCoordinateMask(); break;
        case 17: _t->hideCoordinateMask(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< sChartWidget* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (sChartWidget::*)(QString , sChartWidget * , void * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sChartWidget::CommandSignal)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (sChartWidget::*)(const double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sChartWidget::selected)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject sChartWidget::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_sChartWidget.data,
    qt_meta_data_sChartWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *sChartWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *sChartWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_sChartWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int sChartWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    return _id;
}

// SIGNAL 0
void sChartWidget::CommandSignal(QString _t1, sChartWidget * _t2, void * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void sChartWidget::selected(const double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
