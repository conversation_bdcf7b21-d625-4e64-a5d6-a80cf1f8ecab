#include "uiSingleAcquisition.h"
//#include "sTools/sInsertNotify.h"

//#include <sMethod/cTQ_CMD_HZH.h>
//#include "cDebugFunctions.h"
//#include <HWConnection.h>
#include <QFileDialog>
//#include "sMethod/parmeter_structs.h"
//#include "UMAStructure/UMA_HCS_Data.h"//<UMA_HCS_Data.h>
//#include <USBConnection/cParamCCS.h>

uiSingleAcquisition::uiSingleAcquisition(QScriptEngine* pScriptEngine,
                                       uiTune* pTune,
                                       uiSystem* pSystem,
                                       QWidget *parent) :
    qUiWidget(parent),
    mTune(pTune),
    mSystem(pSystem)
{
    ui.setupUi(this);
}

uiSingleAcquisition::~uiSingleAcquisition()
{
    if(mChartTIC){
        delete mChartTIC;
        mChartTIC=nullptr;
    }
    resizeChart(0);
}

void uiSingleAcquisition::initClass(QString& filePath)
{
    mQ1ScanParamEditor= new uiQ13ScanParamEditor(QLatin1String("Q1"),
                                                 this);
    mQ1ScanParamEditor->initClass(filePath);
    mQ3ScanParamEditor= new uiQ13ScanParamEditor(QLatin1String("Q3"),
                                                 this);
    mQ3ScanParamEditor->initClass(filePath);
    mQ1SIMParamEditor= new uiQ13SIMParamEditor(QLatin1String("Q1"),
                                               this);
    mQ1SIMParamEditor->initClass(filePath);
    mQ3SIMParamEditor= new uiQ13SIMParamEditor(QLatin1String("Q3"),
                                               this);
    mQ3SIMParamEditor->initClass(filePath);
    mMRMParameterEditor= new uiMRMParameterEditor(this);
    mMRMParameterEditor->initClass(filePath);
    initUI(filePath);
}

bool uiSingleAcquisition::initUI(QString& filePath)
{

    mChartTIC = new sChartWidget(sChartWidget::_TIC_LEGEND,this);//, ui->UI_W_ACQ_MENU
    ui.UI_LAYOUT_CHART_TIC_SINGLEACQ->insertWidget(0, mChartTIC);
    resizeChart(1);
    QPalette palette;//= this->palette();
    palette.setColor(QPalette::Background, QColor(233, 234, 239, 255));
    mChartTIC->setPalette(palette);
    mChartTIC->setAutoFillBackground(true);
    ui.UI_LAYOUT_Q1SCAN_SINGLEACQ->addWidget(mQ1ScanParamEditor);
    ui.UI_LAYOUT_Q3SCAN_SINGLEACQ->addWidget(mQ3ScanParamEditor);

    ui.UI_LAYOUT_Q1SIM_SINGLEACQ->addWidget(mQ1SIMParamEditor);
    ui.UI_LAYOUT_Q3SIM_SINGLEACQ->addWidget(mQ3SIMParamEditor);

    ui.UI_LAYOUT_MRM_SINGLEACQ->addWidget(mMRMParameterEditor);

    createToolBar();
    return true;
}

void uiSingleAcquisition::createToolBar()
{
    ui.UI_MB_RUN_SINGLEACQ->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_RUN_SINGLEACQ->setPicture(QPixmap(":/start.png"),
                                       QPixmap(":/start.png"),
                                       QPixmap(":/start.png"),
                                       QPixmap(":/start.png"),
                                       tr("开始扫描"));
    ui.UI_MB_RUN_SINGLEACQ->setTextSize(64,64,40);
    connect(ui.UI_MB_RUN_SINGLEACQ,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_RUN_SINGLEACQ()));

    ui.UI_MB_STOP_SINGLEACQ->setTypeButton(MyWidget::sMyButton::_TYPE_TEXTICON);
    ui.UI_MB_STOP_SINGLEACQ->setPicture(QPixmap(":/stop.png"),
                                       QPixmap(":/stop.png"),
                                       QPixmap(":/stop.png"),
                                       QPixmap(":/stop.png"),
                                       tr("停止扫描"));
    ui.UI_MB_STOP_SINGLEACQ->setTextSize(64,64,40);
    connect(ui.UI_MB_STOP_SINGLEACQ,SIGNAL(ButtonClicked()), this, SLOT(onUI_MB_STOP_SINGLEACQ()));
}

void uiSingleAcquisition::resizeChart(int size)
{
    if(mMassChart.size()!= size){
        while(mMassChart.size()!=0){
            ui.UI_LAYOUT_PROCESS->removeWidget(mMassChart.last());
            delete mMassChart.last();
            mMassChart.removeLast();
        }
        for(int i=0; i<size; i++){
            sChartWidget* tempChart = new sChartWidget(sChartWidget::_NORMAL_CHART);
            //tempChart->setMaximumHeight(mCONGIG_ACQUISITION.maxiHeighMassChart);
            ui.UI_LAYOUT_PROCESS->addWidget(tempChart);
            QPalette palette;//= this->palette();
            palette.setColor(QPalette::Background, QColor(233, 234, 239, 255));
            tempChart->setPalette(palette);
            tempChart->setAutoFillBackground(true);
            mMassChart.append(tempChart);
        }
    }
}
