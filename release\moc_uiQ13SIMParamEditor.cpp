/****************************************************************************
** Meta object code from reading C++ file 'uiQ13SIMParamEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.5)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../UI/uiSingleAcquisition/uiQ13SIMParamEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'uiQ13SIMParamEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.5. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_uiQ13SIMParamEditor_t {
    QByteArrayData data[11];
    char stringdata0[190];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_uiQ13SIMParamEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_uiQ13SIMParamEditor_t qt_meta_stringdata_uiQ13SIMParamEditor = {
    {
QT_MOC_LITERAL(0, 0, 19), // "uiQ13SIMParamEditor"
QT_MOC_LITERAL(1, 20, 12), // "updateFreAcq"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 5), // "speed"
QT_MOC_LITERAL(4, 40, 17), // "on_chanel_Changed"
QT_MOC_LITERAL(5, 58, 17), // "QTableWidgetItem*"
QT_MOC_LITERAL(6, 76, 4), // "item"
QT_MOC_LITERAL(7, 81, 41), // "onLineEdit_Polarity_switch_ti..."
QT_MOC_LITERAL(8, 123, 4), // "arg1"
QT_MOC_LITERAL(9, 128, 30), // "onLineEdit_pauseTimeTextEdited"
QT_MOC_LITERAL(10, 159, 30) // "onLineEdit_eventTimeTextEdited"

    },
    "uiQ13SIMParamEditor\0updateFreAcq\0\0"
    "speed\0on_chanel_Changed\0QTableWidgetItem*\0"
    "item\0onLineEdit_Polarity_switch_timeTextEdited\0"
    "arg1\0onLineEdit_pauseTimeTextEdited\0"
    "onLineEdit_eventTimeTextEdited"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_uiQ13SIMParamEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    1,   42,    2, 0x08 /* Private */,
       7,    1,   45,    2, 0x08 /* Private */,
       9,    1,   48,    2, 0x08 /* Private */,
      10,    1,   51,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,    8,

       0        // eod
};

void uiQ13SIMParamEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<uiQ13SIMParamEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->updateFreAcq((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->on_chanel_Changed((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1]))); break;
        case 2: _t->onLineEdit_Polarity_switch_timeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->onLineEdit_pauseTimeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->onLineEdit_eventTimeTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (uiQ13SIMParamEditor::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&uiQ13SIMParamEditor::updateFreAcq)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject uiQ13SIMParamEditor::staticMetaObject = { {
    &uiBaseParamEditor::staticMetaObject,
    qt_meta_stringdata_uiQ13SIMParamEditor.data,
    qt_meta_data_uiQ13SIMParamEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *uiQ13SIMParamEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *uiQ13SIMParamEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_uiQ13SIMParamEditor.stringdata0))
        return static_cast<void*>(this);
    return uiBaseParamEditor::qt_metacast(_clname);
}

int uiQ13SIMParamEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = uiBaseParamEditor::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void uiQ13SIMParamEditor::updateFreAcq(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
