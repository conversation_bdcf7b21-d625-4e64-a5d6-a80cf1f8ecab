#ifndef CCONFIGOMS_H
#define CCONFIGOMS_H

#include <qsystemdetection.h>
#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif
#include <QCoreApplication>
#include <QVector>
#include <QObject>


struct _CONGIG_OMS
{
public:
    enum MODEL_GAUGE{NULL_GAUGE,PFEIFFER_GAUGE,INSTRUE_GAUGE,LEYBOLD_GAUGE,LEYBOLD_PTR90N_GAUGE,
                     LEYBOLD_PTR90_GAUGE,LEYBOLD_PTR90P_GAUGE,PFEIFFER_PKR251};
    struct _CONGIG_MAINWINDOW_LIT{
        quint32 SuperMode= 0;//bool superMode=false;
        _StreamBody::Type_Data DataFormat= _StreamBody::Type_FloatCompress;
        //quint32 DetectMode= 0;
        //bool showPressure=true;
        //        double gainPressure= 2.1507;
        //        double offsetPressure= -1.3921;
        //        MODEL_GAUGE ModelGauge= LEYBOLD_GAUGE/*PFEIFFER_GAUGE*/;
        //        quint16 arduinoVID=6790;
        //        quint16 arduinoPID=29987;
    };

    struct _CONGIG_METHOD_LIT{
        double MassExtension=5;
        QString LimitInjectTime;
        QString ScaleRangeAGC;
        QString FunctionAGC;
    };
    enum STYLE_CHART: quint32 { ORIGINAL_CHART, PROCESS_CHART, STICK_CHART, MASS_BAR_CHART };
    struct _CONGIG_ACQUISITION_LIT{
        double Period= 1000;
        int maxiHeighMassChart= 16777215;
        STYLE_CHART modelChart= PROCESS_CHART;
        quint32 fileSaveParam= 1;
        double xLeft = 0, xRight = 0;
        double backGround = 0;
        double a = 0, b = 0, c = 0;
    };

    struct _CONGIG_ANALYSIS{
        double Period= 1000;
        int maxiHeighMassChart= 16777215;

        //        double xLeft = 0, xRight = 0;
        //        double backGround = 0;
        //        double a = 0, b = 0, c = 0;
    };

    struct _CONGIG_STATE_SYS_LIT{
        //double MinDriver= 0.1;
        //double MinSpeed= 1;
        double gainPressure= 2.1507;
        double offsetPressure= -1.3921;
        MODEL_GAUGE ModelGauge= LEYBOLD_GAUGE/*PFEIFFER_GAUGE*/;
        quint16 arduinoVID=6790;
        quint16 arduinoPID=29987;

        _CONGIG_OMS::MODEL_GAUGE machineGaugeModel =_CONGIG_OMS::NULL_GAUGE;//冻干机规
        double machineGaugeOffset = 0;
        double machineGaugeGain = 0;
    };

    struct _CONGIG_SYSTEM_LIT{
        float LimitPressLYO=0.05;//bar
    };

    struct _CONGIG_MANUAL_TUNING_LIT{
        double MassExtension=5;
        bool showFit= false;
    };

    struct _CONGIG_AUTO_TUNING_LIT{
        double MassExtension=5;
    };

    struct _STRUCT_DATA{
        uint32_t uEvtValidPoint=0,//uPrePoint+ uEventPoint+ uPostPoint
        uDelayPoint=0,
        uPrePoint=0,
        uEventPoint=0,//without pre post
        uPostPoint=0;
    };

    struct _PARAM_FIT{
        uint enable= 1;
        uint ployD=3;
        QVector<QString> predictedValues;//predictedValues;//std::vector<double> predictedValues;
        QVector<QString> actualValues;//std::vector<double> actualValues;
        std::vector<double> coefF;
        std::vector<double> coefB;
    public:
        _PARAM_FIT& operator =(const _PARAM_FIT& p){
            if(this == &p)
                return *this;
            predictedValues.clear();
            actualValues.clear();
            coefF.clear();
            coefB.clear();
            int length = p.predictedValues.size();
            for(int i=0; i< length; ++i)
                predictedValues.append(p.predictedValues[i]);
            for(int i=0; i< length; ++i)
                actualValues.append(p.actualValues[i]);
            for(int i=0; i< length; ++i)
                coefF.push_back(p.coefF[i]);
            for(int i=0; i< length; ++i)
                coefB.push_back(p.coefB[i]);
            return *this;
        }
        QString comboCalibrat(/*_CONGIG_OMS::_PARAM_FIT& pPARAM_FIT*/){
            auto tempFunctionStr = [&](QVector<QString>& tempVec)->QString{
                QStringList tempList;
                for(int i=0; i< tempVec.size(); ++i)
                    tempList.append(tempVec[i]);
                return tempList.join('@');
            };
            auto tempFunctionDouble = [&](std::vector<double>& tempVec)->QString{
                QStringList tempList;
                for(uint i=0; i< tempVec.size(); ++i)
                    tempList.append(QString::number(tempVec[i]));
                return tempList.join('@');
            };
            QStringList tempListRoot;
            tempListRoot.append(QString::number(enable));
            tempListRoot.append(QString::number(ployD));
            tempListRoot.append(tempFunctionStr(actualValues));
            tempListRoot.append(tempFunctionStr(predictedValues));
            tempListRoot.append(tempFunctionDouble(coefF));
            tempListRoot.append(tempFunctionDouble(coefB));
            return tempListRoot.join('&');
        }
#define SIZE_LINEEDIT 6
        bool splitCalibrat(QString strFIT){
            QStringList tmpList= strFIT.split("&");
            if(tmpList.size()!= SIZE_LINEEDIT)
                return false;
            enable= tmpList[0].toUInt();
            ployD= tmpList[1].toUInt();
            QStringList tempactualValues= tmpList[2].split("@");
            QStringList temppredictedValues= tmpList[3].split("@");
            QStringList tempCF= tmpList[4].split("@");
            QStringList tempCB= tmpList[5].split("@");
            if(tempactualValues.size()!=temppredictedValues.size())
                return false;
            actualValues.clear();
            predictedValues.clear();
            coefF.clear();
            coefB.clear();
            foreach (QString var, tempactualValues)
                if(!var.isEmpty())
                    actualValues.push_back(var);
            foreach (QString var, temppredictedValues)
                if(!var.isEmpty()
                        )predictedValues.push_back(var);
            foreach (QString var, tempCF)
                if(!var.isEmpty())
                    coefF.push_back(var.toDouble());
            foreach (QString var, tempCB)
                if(!var.isEmpty())
                    coefB.push_back(var.toDouble());
            return true;
        }
        static QString getFormula(std::vector<double>& coef){
            QString tempStr;
            switch (coef.size()) {
            case 6:
                tempStr= QString("y= %1x^5+ %2x^4+ %3x^3+ %4x^2+ %5x+ %6").
                        arg(coef[5]).arg(coef[4]).arg(coef[3]).arg(coef[2]).arg(coef[1]).arg(coef[0]);
                break;
            case 5:
                tempStr= QString("y= %1x^4+ %2x^3+ %3x^2+ %4x+ %5").
                        arg(coef[4]).arg(coef[3]).arg(coef[2]).arg(coef[1]).arg(coef[0]);
                break;
            case 4:
                tempStr= QString("y= %1x^3+ %2x^2+ %3x+ %4").
                        arg(coef[3]).arg(coef[2]).arg(coef[1]).arg(coef[0]);
                break;
            case 3:
                tempStr= QString("y= %1x^2+ %2x+ %3").
                        arg(coef[2]).arg(coef[1]).arg(coef[0]);
                break;
            case 2:
                tempStr= QString("y= %1x+ %2").
                        arg(coef[1]).arg(coef[0]);
                break;
            default:
                tempStr= QString("y= x");
                break;
            }
            return tempStr;
        }
        bool calibrarionF(const double* pSrc, double* pDest, int length){
            if(!enable)
                return false;
            if((coefF.size()> SIZE_LINEEDIT)||(coefF.size()< 2))
                return false;
            calibrarion(pSrc, pDest, length, coefF);
            return true;
        }
        bool calibrarionB(const double* pSrc, double* pDest, int length){
            if(!enable)
                return false;
            if((coefF.size()> SIZE_LINEEDIT)||(coefB.size()< 2))
                return false;
            calibrarion(pSrc, pDest, length, coefB);
            return true;
        }
        static void calibrarion(const double* pSrc, double* pDest, int length, const std::vector<double>& coef){
            switch (coef.size()) {
            case 6:
                for(int i=0;i<length;++i){
                    *pDest= pow(*pSrc, 5) * coef[5]
                            + pow(*pSrc, 4) * coef[4]
                            + pow(*pSrc, 3) * coef[3]
                            + pow(*pSrc, 2) * coef[2]
                            + *pSrc * coef[1]
                            + coef[0];
                    ++pSrc;
                    ++pDest;
                }break;
            case 5:
                for(int i=0;i<length;++i){
                    *pDest= pow(*pSrc, 4) * coef[4]
                            + pow(*pSrc, 3) * coef[3]
                            + pow(*pSrc, 2) * coef[2]
                            + *pSrc * coef[1]
                            + coef[0];
                    ++pSrc;
                    ++pDest;
                }break;
            case 4:
                for(int i=0;i<length;++i){
                    *pDest= pow(*pSrc, 3) * coef[3]
                            + pow(*pSrc, 2) * coef[2]
                            + *pSrc * coef[1]
                            + coef[0];
                    ++pSrc;
                    ++pDest;
                }break;
            case 3:
                for(int i=0;i<length;++i){
                    *pDest= pow(*pSrc, 2) * coef[2]
                            + *pSrc * coef[1]
                            + coef[0];
                    ++pSrc;
                    ++pDest;
                }break;
            case 2:
                for(int i=0;i<length;++i){
                    *pDest= *pSrc * coef[1]
                            + coef[0];
                    ++pSrc;
                    ++pDest;
                }break;
            default:
                for(int i=0;i<length;++i){
                    *pDest= *pSrc;
                    ++pSrc;
                    ++pDest;
                }
                break;
            }
        }
    };
};

class _FUNTION_OMS: public QObject{
    Q_OBJECT
public:
    _FUNTION_OMS(){}
    ~_FUNTION_OMS(){}
    //enum DETECT_MODE:quint32 {GUIYOU_MODE, H2O_MODE} ;
    static QString getFilePath(){
        return QCoreApplication::applicationDirPath()+"/DebugParam.ini";
    }
//    static DETECT_MODE& getactualValuesMode(){
//        static DETECT_MODE staticSelectMode;
//        return staticSelectMode;
//    }
//    static QString getTextModeButton(){
//        if(GUIYOU_MODE== getactualValuesMode()){
//            return tr("硅油模式");
//        }else if(H2O_MODE== getactualValuesMode()){
//            return tr("水模式");
//        }
//        return QString();
//    }

    static QString machineNumber(QString number={}){
        static QString tempN;
        if(number.isEmpty())
            return tempN;
        else{
            tempN= number;
            return tempN;
        }
    }

    static QString staticParamXIC(QString number={}){
        static QString tempN;
        if(number.isEmpty())
            return tempN;
        else{
            tempN= number;
            return tempN;
        }
    }


    //    #define SIZE_LINEEDIT 6
    //    static bool splitCalibrat(QString strFIT, _CONGIG_OMS::_PARAM_FIT& pPARAM_FIT){
    //        QStringList tmpList= strFIT.split("&");
    //        if(tmpList.size()== SIZE_LINEEDIT){
    //            pPARAM_FIT.enable= tmpList[0].toUInt();
    //            pPARAM_FIT.ployD= tmpList[1].toUInt();
    //            QStringList tempactualValues= tmpList[2].split("@");
    //            QStringList temppredictedValues= tmpList[3].split("@");
    //            QStringList tempCF= tmpList[4].split("@");
    //            QStringList tempCB= tmpList[5].split("@");
    //            if(tempactualValues.size()==temppredictedValues.size()){
    //                //pCALIBRATE.lock();
    //                pPARAM_FIT.actualValues.clear();
    //                pPARAM_FIT.predictedValues.clear();
    //                pPARAM_FIT.coefF.clear();
    //                pPARAM_FIT.coefB.clear();
    //                foreach (QString var, tempactualValues)
    //                    pPARAM_FIT.actualValues.push_back(var);
    //                foreach (QString var, temppredictedValues)
    //                    pPARAM_FIT.predictedValues.push_back(var);
    //                foreach (QString var, tempCF)
    //                    pPARAM_FIT.coefF.push_back(var.toDouble());
    //                foreach (QString var, tempCB)
    //                    pPARAM_FIT.coefB.push_back(var.toDouble());
    //            }else
    //                return false;
    //        }else
    //            return false;
    //        return true;
    //    }



    static void calibrationF(const _CONGIG_OMS::_PARAM_FIT& pPARAM_FIT,
                             const double* pdbOffset,
                             const cParamValue::_EventSIM* pEventSIM,
                             std::vector<double>& pBufferX,
                             std::vector<double>& pBufferY, std::vector<quint32>& pTimePointSIM,
                             _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA)
    {
        tempSTRUCT_DATA->uDelayPoint= tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->delayTimeMs/ pEventSIM->holdTimeMs;
        tempSTRUCT_DATA->uPrePoint= tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->preReadyTimeMs/ pEventSIM->holdTimeMs;
        tempSTRUCT_DATA->uPostPoint=  tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->postReadyTimeMs/ pEventSIM->holdTimeMs;
        pBufferX.clear();
        pBufferY.clear();
        pTimePointSIM.clear();
        for(int indexM=0; indexM< pEventSIM->length(); ++indexM){
            if(pEventSIM->mass[indexM]<0.0000001)
                break;
            pBufferX.push_back(pEventSIM->mass[indexM]);
            quint32 ePoint= tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->timeMs[indexM]/ pEventSIM->holdTimeMs;
            pTimePointSIM.push_back(ePoint);
        }
        pBufferY.resize(pBufferX.size());
    }

    static void calibrationF(_CONGIG_OMS::_PARAM_FIT& pPARAM_FIT,
                             std::vector<double>& pBuffer,
                             cParamValue::_EventLIT* pEventLIT,
                             _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA){
        tempSTRUCT_DATA->uDelayPoint= tempSTRUCT_DATA->uEvtValidPoint* pEventLIT->delayTimeMs/ pEventLIT->holdTimeMs;
        tempSTRUCT_DATA->uPrePoint= tempSTRUCT_DATA->uEvtValidPoint* pEventLIT->preReadyTimeMs/ pEventLIT->holdTimeMs;
        tempSTRUCT_DATA->uPostPoint=  tempSTRUCT_DATA->uEvtValidPoint* pEventLIT->postReadyTimeMs/ pEventLIT->holdTimeMs;
        tempSTRUCT_DATA->uEventPoint= tempSTRUCT_DATA->uEvtValidPoint- tempSTRUCT_DATA->uDelayPoint-
                tempSTRUCT_DATA->uPrePoint- tempSTRUCT_DATA->uPostPoint;

        double tempStart= pEventLIT->msStart<= pEventLIT->msEnd?
                    pEventLIT->msStart- pEventLIT->msPrecursor: pEventLIT->msStart+ pEventLIT->msPrecursor;
        double tempEnd= pEventLIT->msStart<= pEventLIT->msEnd?
                    pEventLIT->msEnd+ pEventLIT->msPrecursor: pEventLIT->msEnd- pEventLIT->msPrecursor;
        double dbStep= (tempEnd - tempStart) / (tempSTRUCT_DATA->uEventPoint-1);//double dbStep= (tempEnd - tempStart) / tempSTRUCT_DATA->uEvtValidPoint;
        double* pFirst= pBuffer.data()+ tempSTRUCT_DATA->uDelayPoint+ tempSTRUCT_DATA->uPrePoint;
        double* pLast= pFirst+ tempSTRUCT_DATA->uEventPoint-1;

        if(pEventLIT->msStartOrig<= pEventLIT->msEndOrig){
            if(pPARAM_FIT.enable==1){
                switch (pPARAM_FIT.coefF.size()) {
                case 6:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 5) * pPARAM_FIT.coefF[5]
                                + pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                                + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 5:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                                + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 4:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 3:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 2:
                    while(pFirst<= pLast){
                        *pFirst= tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                default:
                    while(pFirst<= pLast){
                        *pFirst= tempStart;
                        if(*pFirst< pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst> pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }
                    break;
                }
            }else{
                while(pFirst<= pLast){
                    *pFirst= tempStart;
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst> pEventLIT->msEndOrig)
                        ++tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }
            }
        }else{
            if(pPARAM_FIT.enable==1){
                switch (pPARAM_FIT.coefF.size()) {
                case 6:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 5) * pPARAM_FIT.coefF[5]
                                + pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                                + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 5:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                                + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 4:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                                + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 3:
                    while(pFirst<= pLast){
                        *pFirst= pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                                + tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                case 2:
                    while(pFirst<= pLast){
                        *pFirst= tempStart * pPARAM_FIT.coefF[1]
                                + pPARAM_FIT.coefF[0];
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }break;
                default:
                    while(pFirst<= pLast){
                        *pFirst= tempStart;
                        if(*pFirst> pEventLIT->msStartOrig)
                            ++tempSTRUCT_DATA->uPrePoint;
                        if(*pFirst< pEventLIT->msEndOrig)
                            ++tempSTRUCT_DATA->uPostPoint;
                        tempStart += dbStep;
                        pFirst++;
                    }
                    break;
                }
            }else{
                while(pFirst<= pLast){
                    *pFirst= tempStart;
                    if(*pFirst> pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        ++tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }
            }
        }
        tempSTRUCT_DATA->uEventPoint= tempSTRUCT_DATA->uEvtValidPoint- tempSTRUCT_DATA->uDelayPoint
                -tempSTRUCT_DATA->uPrePoint- tempSTRUCT_DATA->uPostPoint;
    }

    static void calibrationF(const _CONGIG_OMS::_PARAM_FIT& pPARAM_FIT,
                             const double* pdbOffset,
                             const cParamValue::_EventSIM2019* pEventSIM,
                             std::vector<double>& pBufferX,
                             std::vector<double>& pBufferY,
                             std::vector<quint32>& pTimePointSIM,
                             _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA)
    {
        tempSTRUCT_DATA->uDelayPoint= 0;//tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->delayTime/ pEventSIM->holdTime;
        tempSTRUCT_DATA->uPrePoint= static_cast<quint32>(tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->preReadyTime/ pEventSIM->holdTime);
        tempSTRUCT_DATA->uPostPoint=  static_cast<quint32>(tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->postReadyTime/ pEventSIM->holdTime);
        pBufferX.clear();
        pBufferY.clear();
        pTimePointSIM.clear();
        for(int indexM=0; indexM<64; ++indexM){
            if(pEventSIM->mass[indexM]<0.0000001)
                break;
            pBufferX.push_back(pEventSIM->mass[indexM]);
            quint32 ePoint= static_cast<quint32>(tempSTRUCT_DATA->uEvtValidPoint* pEventSIM->time[indexM]/ pEventSIM->holdTime);
            pTimePointSIM.push_back(ePoint);
        }
        pBufferY.resize(pBufferX.size());

        double massactualValues=0;
        if(pPARAM_FIT.enable==1){
            switch (pPARAM_FIT.coefF.size()) {
            case 6:
                for(uint indexM=0; indexM< pBufferX.size(); ++indexM){
                    massactualValues= pBufferX[indexM];
                    pBufferX[indexM]= pow(massactualValues, 5) * pPARAM_FIT.coefF[5]
                            + pow(massactualValues, 4) * pPARAM_FIT.coefF[4]
                            + pow(massactualValues, 3) * pPARAM_FIT.coefF[3]
                            + pow(massactualValues, 2) * pPARAM_FIT.coefF[2]
                            + massactualValues * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                }break;
            case 5:
                for(uint indexM=0; indexM< pBufferX.size(); ++indexM){
                    massactualValues= pBufferX[indexM];
                    pBufferX[indexM]= pow(massactualValues, 4) * pPARAM_FIT.coefF[4]
                            + pow(massactualValues, 3) * pPARAM_FIT.coefF[3]
                            + pow(massactualValues, 2) * pPARAM_FIT.coefF[2]
                            + massactualValues * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                }break;
            case 4:
                for(uint indexM=0; indexM< pBufferX.size(); ++indexM){
                    massactualValues= pBufferX[indexM];
                    pBufferX[indexM]= pow(massactualValues, 3) * pPARAM_FIT.coefF[3]
                            + pow(massactualValues, 2) * pPARAM_FIT.coefF[2]
                            + massactualValues * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                }break;
            case 3:
                for(uint indexM=0; indexM< pBufferX.size(); ++indexM){
                    massactualValues= pBufferX[indexM];
                    pBufferX[indexM]= pow(massactualValues, 2) * pPARAM_FIT.coefF[2]
                            + massactualValues * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                }break;
            case 2:
                for(uint indexM=0; indexM< pBufferX.size(); ++indexM){
                    massactualValues= pBufferX[indexM];
                    pBufferX[indexM]= massactualValues * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                }break;
            default: break;
            }
        }
    }

    static void calibrationF(_CONGIG_OMS::_PARAM_FIT& pPARAM_FIT,
                             std::vector<double>& pBuffer,
                             cParamValue::_EventLIT2019* pEventLIT,
                             _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA){
        double tempStart= pEventLIT->msStart- pEventLIT->msPrecursor;
        double tempEnd=pEventLIT->msEnd+ pEventLIT->msPrecursor;
        double dbStep= (tempEnd - tempStart) / tempSTRUCT_DATA->uEvtValidPoint;
        double* pFirst= pBuffer.data();
        double* pLast= pFirst+ tempSTRUCT_DATA->uEvtValidPoint-1;

        tempSTRUCT_DATA->uDelayPoint= 0;//tempSTRUCT_DATA->uEvtValidPoint* pEventLIT->delayTime/ pEventLIT->holdTime;
        tempSTRUCT_DATA->uPrePoint= 0;
        tempSTRUCT_DATA->uPostPoint= tempSTRUCT_DATA->uEvtValidPoint;

        if(pPARAM_FIT.enable==1){
            switch (pPARAM_FIT.coefF.size()) {
            case 6:
                while(pFirst<= pLast){
                    *pFirst= pow(tempStart, 5) * pPARAM_FIT.coefF[5]
                            + pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                            + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                            + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                            + tempStart * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }break;
            case 5:
                while(pFirst<= pLast){
                    *pFirst= pow(tempStart, 4) * pPARAM_FIT.coefF[4]
                            + pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                            + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                            + tempStart * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }break;
            case 4:
                while(pFirst<= pLast){
                    *pFirst= pow(tempStart, 3) * pPARAM_FIT.coefF[3]
                            + pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                            + tempStart * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }break;
            case 3:
                while(pFirst<= pLast){
                    *pFirst= pow(tempStart, 2) * pPARAM_FIT.coefF[2]
                            + tempStart * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }break;
            case 2:
                while(pFirst<= pLast){
                    *pFirst= tempStart * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }break;
            default:
                while(pFirst<= pLast){
                    *pFirst= tempStart;
                    if(*pFirst< pEventLIT->msStartOrig)
                        ++tempSTRUCT_DATA->uPrePoint;
                    if(*pFirst< pEventLIT->msEndOrig)
                        --tempSTRUCT_DATA->uPostPoint;
                    tempStart += dbStep;
                    pFirst++;
                }
                break;
            }
        }else{
            while(pFirst<= pLast){
                *pFirst= tempStart;
                if(*pFirst< pEventLIT->msStartOrig)
                    ++tempSTRUCT_DATA->uPrePoint;
                if(*pFirst< pEventLIT->msEndOrig)
                    --tempSTRUCT_DATA->uPostPoint;
                tempStart += dbStep;
                pFirst++;
            }
        }
    }

    static void calibrationF(_CONGIG_OMS::_PARAM_FIT& pPARAM_FIT, double* pFirst, double* pLast,
                             double dbVal, double dbStep){
        if(pPARAM_FIT.enable==1){
            switch (pPARAM_FIT.coefF.size()) {
            case 6:
                while(pFirst<= pLast){
                    *pFirst= pow(dbVal, 5) * pPARAM_FIT.coefF[5]
                            + pow(dbVal, 4) * pPARAM_FIT.coefF[4]
                            + pow(dbVal, 3) * pPARAM_FIT.coefF[3]
                            + pow(dbVal, 2) * pPARAM_FIT.coefF[2]
                            + dbVal * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    dbVal += dbStep;
                    pFirst++;
                }break;
            case 5:
                while(pFirst<= pLast){
                    *pFirst= pow(dbVal, 4) * pPARAM_FIT.coefF[4]
                            + pow(dbVal, 3) * pPARAM_FIT.coefF[3]
                            + pow(dbVal, 2) * pPARAM_FIT.coefF[2]
                            + dbVal * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    dbVal += dbStep;
                    pFirst++;
                }break;
            case 4:
                while(pFirst<= pLast){
                    *pFirst= pow(dbVal, 3) * pPARAM_FIT.coefF[3]
                            + pow(dbVal, 2) * pPARAM_FIT.coefF[2]
                            + dbVal * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    dbVal += dbStep;
                    pFirst++;
                }break;
            case 3:
                while(pFirst<= pLast){
                    *pFirst= pow(dbVal, 2) * pPARAM_FIT.coefF[2]
                            + dbVal * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    dbVal += dbStep;
                    pFirst++;
                }break;
            case 2:
                while(pFirst<= pLast){
                    *pFirst= dbVal * pPARAM_FIT.coefF[1]
                            + pPARAM_FIT.coefF[0];
                    dbVal += dbStep;
                    pFirst++;
                }break;
            default:
                while(pFirst<= pLast){
                    *pFirst= dbVal;
                    dbVal += dbStep;
                    pFirst++;
                }
                break;
            }
        }else{
            while(pFirst<= pLast){
                *pFirst= dbVal;
                dbVal += dbStep;
                pFirst++;
            }
        }
    }
};
#endif // CCONFIGOMS_H
