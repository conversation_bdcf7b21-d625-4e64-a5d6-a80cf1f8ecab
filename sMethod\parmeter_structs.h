﻿#pragma once

#include <QObject>
#include <QString>
#include <QDebug>
//#include "LabSolutionsRW_global.h"

namespace LabSolutionsRW {
//Q_NAMESPACE_EXPORT(LABSOLUTIONSRW_EXPORT)
Q_NAMESPACE
enum EventType {
    /// 8040
    MRM,
    Q1Scan,
    Q1SIM,
    Q3Scan,
    Q3SIM,
    NeutralLossScan,
    PrecursorIonScan,
    ProductIonScan,
    /// 9030
    QtflMs,
    QtflMsMs,
    QtflMRM
};
Q_ENUM_NS(EventType);

inline QString msEeventType2String(EventType type){
    switch (type) {
    case MRM             :  return QStringLiteral("MRM");
    case Q1Scan          :  return QStringLiteral("Q1Scan");
    case Q1SIM           :  return QStringLiteral("Q1SIM");
    case Q3Scan          :  return QStringLiteral("Q3Scan");
    case Q3SIM           :  return QStringLiteral("Q3SIM");
    case NeutralLossScan :  return QStringLiteral("NeutralLossScan");
    case PrecursorIonScan:  return QStringLiteral("PrecursorIonScan");
    case ProductIonScan  :  return QStringLiteral("ProductIonScan");
    case QtflMs          :  return QStringLiteral("QtflMs");
    case QtflMsMs        :  return QStringLiteral("QtflMsMs");
    case QtflMRM         :  return QStringLiteral("QtflMRM");
    }
    return QString();
}


struct Event {
    EventType type;
    int polarity; // (+):1; (-):-1
    double eventTime = 0; ///< unit: sec
};

/* ******************** 8040 ************************/
struct MRMChanel {
    double precursor = 0;
    double product = 0;
    double dwell_ms = 0;
    double ce = 0;
    double pause_ms = 0;
};

struct SIMChanel {
    double precursor = 0;
    double dwell_ms = 0;
    double pause_ms = 0;
};

struct EventMRM : Event {
    QList<MRMChanel> chanels;
};

struct EventSIM : Event {
    QList<SIMChanel> chanels;
};

struct EventScan : Event {
    double startMz = 0;
    double endMz = 0;
    double scanSpeed = 0; ///< u/sec : can not access now.
};

struct EventIonScan : Event {
    double startMz = 0;
    double endMz = 0;
    double ionMz = 0;
    double ce = 0;
};

/* ******************** 9030 ************************/
// 9030 中QtfMS和QtfMSMS只有一个channel， QtfMRM会有多个通道

/**
 * @brief EventQtfMsMs
 * QtfMSMS
 */
typedef EventIonScan EventQtfMsMs;

/**
 * @brief The EventQtflMs struct
 * QtflMS
 */
struct EventQtflMs : Event {
    double startMz;
    double endMz;
    double ce;
};

struct ToFMRMChannel{
    double startMz = 0;
    double endMz = 0;
    double ce = 0;
};

/**
 * @brief The EventQtfMRM struct
 * QtfMRM
 */
struct EventQtfMRM : Event{
    QList<ToFMRMChannel> channels;
};


/*******************************new event structure*********************************/
class MassEvent;

class MassChannel{
public:
    float firstMz = 0;     ///< start mz
    float secondMz = 0;    ///< end mz
    float acqModeMz = 0;   ///< used in SIM\MRM\ mode, AcqModeMz
    uint prepareTime = 3;  ///< ms, setting time, pause time
    uint pureTime = 97;    ///< ms, the pure scan time, equal to scantime-prepareTime in scan mode, equal to dwell time in else mode.
    float ce = 0;          ///< collision energy
    MassEvent* parent = nullptr;
public:
    /// ms, equal prepareTime + pureTime;
    uint period(){
        return prepareTime + pureTime;
    }
    void print(int indent = 0){
        qDebug() << QString().fill(' ', indent)
                 << QString("firstMz: %1, secondMz: %2, acqModeMz: %3, prepareTime: %4, pureTime: %5, ce: %6")
                    .arg(firstMz).arg(secondMz).arg(acqModeMz).arg(prepareTime).arg(pureTime).arg(ce);
    }
};

class MassEvent
{
public:
    int index = 1;
    EventType type;
    uint startTime = 0; ///< unit: ms
    uint endTime = 0;   ///< unit: ms
    int polarity = 1;   ///< (+):1; (-):-1
    uint eventTime = 0; ///< unit: ms
    QList<MassChannel*> channels;

public:
    MassEvent& operator=(const MassEvent& other) = delete ;

    ~MassEvent(){
        qDeleteAll(channels);
    }
    void addChannel(MassChannel* ch){
        if (ch->parent)
            ch->parent->takeChannel(ch);

        channels.append(ch);
        ch->parent = this;
    }
    void removeChannel(MassChannel* ch){
        channels.removeOne(ch);
        delete ch;
    }
    void takeChannel(MassChannel* ch)
    {
        channels.removeOne(ch);
    }

    void print() const {
        qDebug() << "index" << index << "type:" << type << "startTime" << startTime << "endTime" << endTime
                 << "polarity" << polarity << "eventTime" << eventTime;
        for(auto pCh : channels)
        {
            pCh->print(4);
        }
    }
};

}

Q_DECLARE_METATYPE(LabSolutionsRW::EventType)

