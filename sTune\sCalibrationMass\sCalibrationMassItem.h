#pragma once

#include <QWidget>
#include <cConfigTQ_HZH.h>
#include <uiCalibrationMass/uiCalibrationMassItem.h>

class sCalibrationMassItem : public uiCalibrationMassItem
{
    Q_OBJECT

public:

    explicit sCalibrationMassItem(QWidget *parent = nullptr);
    ~sCalibrationMassItem();
private:
    virtual bool calcFun(
                         const _CONGIG_OMS::_PARAM_FIT& tmpPARAM_FIT, std::vector<double>& predictedValues,
                         std::vector<double>& correction, QString& Formula, double& R2);

};

