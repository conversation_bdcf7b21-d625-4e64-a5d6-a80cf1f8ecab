/********************************************************************************
** Form generated from reading UI file 'uiSystem.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UISYSTEM_H
#define UI_UISYSTEM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiSystem
{
public:
    QHBoxLayout *horizontalLayout;
    QListWidget *UI_LISTW_MENU_LEFT_SYSTEM;
    QWidget *UI_W_GROUP_RIGHT_SYSTEM;
    QVBoxLayout *UI_LAYOUT_GROUP_RIGHT_SYSTEM;

    void setupUi(QWidget *uiSystem)
    {
        if (uiSystem->objectName().isEmpty())
            uiSystem->setObjectName(QString::fromUtf8("uiSystem"));
        uiSystem->resize(771, 515);
        horizontalLayout = new QHBoxLayout(uiSystem);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        UI_LISTW_MENU_LEFT_SYSTEM = new QListWidget(uiSystem);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        new QListWidgetItem(UI_LISTW_MENU_LEFT_SYSTEM);
        UI_LISTW_MENU_LEFT_SYSTEM->setObjectName(QString::fromUtf8("UI_LISTW_MENU_LEFT_SYSTEM"));
        UI_LISTW_MENU_LEFT_SYSTEM->setMaximumSize(QSize(240, 16777215));
        QFont font;
        font.setFamily(QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221"));
        font.setPointSize(24);
        UI_LISTW_MENU_LEFT_SYSTEM->setFont(font);

        horizontalLayout->addWidget(UI_LISTW_MENU_LEFT_SYSTEM);

        UI_W_GROUP_RIGHT_SYSTEM = new QWidget(uiSystem);
        UI_W_GROUP_RIGHT_SYSTEM->setObjectName(QString::fromUtf8("UI_W_GROUP_RIGHT_SYSTEM"));
        UI_LAYOUT_GROUP_RIGHT_SYSTEM = new QVBoxLayout(UI_W_GROUP_RIGHT_SYSTEM);
        UI_LAYOUT_GROUP_RIGHT_SYSTEM->setSpacing(0);
        UI_LAYOUT_GROUP_RIGHT_SYSTEM->setObjectName(QString::fromUtf8("UI_LAYOUT_GROUP_RIGHT_SYSTEM"));
        UI_LAYOUT_GROUP_RIGHT_SYSTEM->setContentsMargins(0, 0, 0, 0);

        horizontalLayout->addWidget(UI_W_GROUP_RIGHT_SYSTEM);


        retranslateUi(uiSystem);

        QMetaObject::connectSlotsByName(uiSystem);
    } // setupUi

    void retranslateUi(QWidget *uiSystem)
    {
        uiSystem->setWindowTitle(QApplication::translate("uiSystem", "Form", nullptr));

        const bool __sortingEnabled = UI_LISTW_MENU_LEFT_SYSTEM->isSortingEnabled();
        UI_LISTW_MENU_LEFT_SYSTEM->setSortingEnabled(false);
        QListWidgetItem *___qlistwidgetitem = UI_LISTW_MENU_LEFT_SYSTEM->item(0);
        ___qlistwidgetitem->setText(QApplication::translate("uiSystem", "\344\273\252\345\231\250\351\205\215\347\275\256", nullptr));
        QListWidgetItem *___qlistwidgetitem1 = UI_LISTW_MENU_LEFT_SYSTEM->item(1);
        ___qlistwidgetitem1->setText(QApplication::translate("uiSystem", "\351\241\271\347\233\256\347\256\241\347\220\206", nullptr));
        QListWidgetItem *___qlistwidgetitem2 = UI_LISTW_MENU_LEFT_SYSTEM->item(2);
        ___qlistwidgetitem2->setText(QApplication::translate("uiSystem", "\347\224\250\346\210\267\347\256\241\347\220\206", nullptr));
        QListWidgetItem *___qlistwidgetitem3 = UI_LISTW_MENU_LEFT_SYSTEM->item(3);
        ___qlistwidgetitem3->setText(QApplication::translate("uiSystem", "\351\231\244\345\210\227\346\235\241\344\273\266", nullptr));
        QListWidgetItem *___qlistwidgetitem4 = UI_LISTW_MENU_LEFT_SYSTEM->item(4);
        ___qlistwidgetitem4->setText(QApplication::translate("uiSystem", "\350\275\257\344\273\266\346\277\200\346\264\273", nullptr));
        QListWidgetItem *___qlistwidgetitem5 = UI_LISTW_MENU_LEFT_SYSTEM->item(5);
        ___qlistwidgetitem5->setText(QApplication::translate("uiSystem", "\350\275\257\344\273\266\345\215\207\347\272\247", nullptr));
        UI_LISTW_MENU_LEFT_SYSTEM->setSortingEnabled(__sortingEnabled);

    } // retranslateUi

};

namespace Ui {
    class uiSystem: public Ui_uiSystem {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UISYSTEM_H
