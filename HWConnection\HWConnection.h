﻿#ifndef HWCONNECTION_H
#define HWCONNECTION_H

//#include "SocketConnecttion/AsioSocketCommunication.h"
//#include "USBConnection/sCommandUSB.h"
#include "USBConnection/HCSCommandUSB.h"

//#include "HWConnection_global.h"

class /*HWCONNECTION_EXPORT*/ HWConnection
{
public:
    HWConnection();

    static UMA_HCS::HCSCommandUSB* getUSB(){
//        static CommandUSB m_usb;
        static UMA_HCS::HCSCommandUSB m_usb;
        return &m_usb;
    }

//    static AsioSocketCommunication* getSocket(){
//        static AsioSocketCommunication socket;
//        return &socket;
//    }
    HCSCommunicationCMD a;
};

#endif // HWCONNECTION_H
