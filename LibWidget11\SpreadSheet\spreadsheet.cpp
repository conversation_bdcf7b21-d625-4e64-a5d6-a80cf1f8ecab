﻿#include <QtGui>

#include "cell.h"
#include "spreadsheet.h"
#include <QMessageBox>
#include <QApplication>
#include <QHeaderView>
#include <QInputDialog>


Spreadsheet::Spreadsheet(QWidget *parent)
    : QTableWidget(parent),
      m_customMenu(new QMenu(this))
{
    autoRecalc = true;

    createCustomActions();
    setItemPrototype(new Cell);
    setSelectionMode(ContiguousSelection);
    setContextMenuPolicy(Qt::CustomContextMenu);

    connect(this, SIGNAL(itemChanged(QTableWidgetItem *)), this, SLOT(somethingChanged()));
    connect(horizontalHeader(), SIGNAL(sectionResized(int,int,int)), this, SIGNAL(modified()));
    connect(this, &Spreadsheet::customContextMenuRequested, this, &Spreadsheet::onCustomContextMenuRequested);

    resetTableSize(80, 26);
}

QString Spreadsheet::currentLocation() const
{
    return QChar('A' + currentColumn())
           + QString::number(currentRow() + 1);
}

QString Spreadsheet::currentFormula() const
{
    return formula(currentRow(), currentColumn());
}

QTableWidgetSelectionRange Spreadsheet::selectedRange() const
{
    QList<QTableWidgetSelectionRange> ranges = selectedRanges();
    if (ranges.isEmpty())
        return QTableWidgetSelectionRange();

#if 1
    /*
     * Using the setSpan method causes the selectedRanges method to return a
     * QTableWidgetSelectionRange with a separate range for each selected cell.
    */
    if (ranges.count() > 1)
    {
        std::list<int> cols;
        std::list<int> rows;
        foreach (const QTableWidgetSelectionRange& range, ranges) {
            cols.push_back(range.leftColumn());
            rows.push_back(range.topRow());
        }

        int left = *std::min_element(cols.begin(), cols.end());
        int right = *std::max_element(cols.begin(),cols.end());
        int top = *std::min_element(rows.begin(), rows.end());
        int bottom = *std::max_element(rows.begin(), rows.end());

        ranges.first() = QTableWidgetSelectionRange(top, left, bottom, right);
    }
#endif

    return ranges.first();
}

void Spreadsheet::resetTableSize(int row, int column)
{
//    setRowCount(0);
//    setColumnCount(0);
    setRowCount(row);
    setColumnCount(column);

    for (int i = 0; i < column; ++i) {
        QTableWidgetItem *item = new QTableWidgetItem;
        item->setText(QString(QChar('A' + i)));
        setHorizontalHeaderItem(i, item);
    }
    verticalHeader()->setDefaultSectionSize(16);
    setCurrentCell(0, 0);
}

//bool Spreadsheet::readFile(const QString &fileName)
//{
//    QFile file(fileName);
//    if (!file.open(QIODevice::ReadOnly)) {
//        QMessageBox::warning(this, tr("Spreadsheet"),
//                             tr("Cannot read file %1:\n%2.")
//                             .arg(file.fileName())
//                             .arg(file.errorString()));
//        return false;
//    }

//    QDataStream in(&file);
//    in.setVersion(QDataStream::Qt_5_8);

//    quint32 magic;
//    in >> magic;
//    if (magic != MagicNumber) {
//        QMessageBox::warning(this, tr("Spreadsheet"),
//                             tr("The file is not a Spreadsheet file."));
//        return false;
//    }

//    clear();

//    quint16 row;
//    quint16 column;
//    QString str;

//    QApplication::setOverrideCursor(Qt::WaitCursor);
//    while (!in.atEnd()) {
//        in >> row >> column >> str;
//        setFormula(row, column, str);
//    }
//    QApplication::restoreOverrideCursor();
//    return true;
//}

//bool Spreadsheet::writeFile(const QString &fileName)
//{
//    QFile file(fileName);
//    if (!file.open(QIODevice::WriteOnly)) {
//        QMessageBox::warning(this, tr("Spreadsheet"),
//                             tr("Cannot write file %1:\n%2.")
//                             .arg(file.fileName())
//                             .arg(file.errorString()));
//        return false;
//    }

//    QDataStream out(&file);
//    out.setVersion(QDataStream::Qt_5_8);

//    out << quint32(MagicNumber);

//    QApplication::setOverrideCursor(Qt::WaitCursor);
//    for (int row = 0; row < RowCount; ++row) {
//        for (int column = 0; column < ColumnCount; ++column) {
//            QString str = formula(row, column);
//            if (!str.isEmpty())
//                out << quint16(row) << quint16(column) << str;
//        }
//    }
//    QApplication::restoreOverrideCursor();
//    return true;
//}

void Spreadsheet::sort(const SpreadsheetCompare &compare)
{
    QList<QStringList> rows;
    QTableWidgetSelectionRange range = selectedRange();
    int i;

    for (i = 0; i < range.rowCount(); ++i) {
        QStringList row;
        for (int j = 0; j < range.columnCount(); ++j)
            row.append(formula(range.topRow() + i,
                               range.leftColumn() + j));
        rows.append(row);
    }

    std::stable_sort(rows.begin(), rows.end(), compare);

    for (i = 0; i < range.rowCount(); ++i) {
        for (int j = 0; j < range.columnCount(); ++j)
            setFormula(range.topRow() + i, range.leftColumn() + j,
                       rows[i][j]);
    }

    clearSelection();
    somethingChanged();
}

QDataStream &operator <<(QDataStream &out, const Spreadsheet &sp)
{
    qint32 row = sp.rowCount();
    qint32 col = sp.columnCount();
    qint32 colWidth;
    //行数，列数
    out << row << col;
    //列宽
    for (int c = 0; c < col; ++c) {
        colWidth = sp.columnWidth(c);
        out << colWidth;
    }
    //内容
    for (int r = 0; r < row; r++){
        for (int c = 0; c < col; ++c) {
            out << sp.formula(r, c).toUtf8();
        }
    }
    return out;
}

QDataStream &operator >>(QDataStream &in, Spreadsheet &sp)
{
    qint32 row;
    qint32 column;
    qint32 colWidth;
    QByteArray str;
    try {
        in >> row >> column;

        sp.blockSignals(true);
        sp.resetTableSize(row, column);
        for (int c = 0; c < column; ++c) {
            in >> colWidth;
            sp.setColumnWidth(c, colWidth);
        }

        for (int r = 0; r < row; ++r) {
            for (int c = 0; c < column; ++c) {
                in >> str;
                sp.setFormula(r, c, str);
            }
        }
        sp.blockSignals(false);
    } catch (...) {

    }
    sp.recalculate();
    return in;
}

void Spreadsheet::cut()
{
    copy();
    del();
}

void Spreadsheet::copy()
{
    QTableWidgetSelectionRange range = selectedRange();
    QString str;

    for (int i = 0; i < range.rowCount(); ++i) {
        for (int j = 0; j < range.columnCount(); ++j) {
            if (j > 0)
                str += "\t";
            str += formula(range.topRow() + i, range.leftColumn() + j);
        }
        str += "\n";
    }
    QApplication::clipboard()->setText(str);
}

void Spreadsheet::paste()
{
    QTableWidgetSelectionRange range = selectedRange();
    QString str = QApplication::clipboard()->text();
    QStringList rows = str.split('\n');
    int numRows = rows.count() - 1;//支持从Excel拷贝的数据
    int numColumns = rows.first().count('\t') + 1;
    auto iter = rows.begin();
    while (iter != rows.end() - 1) {//判断各行长度是否一致,避免从Excel中拷贝到含有\n的单元格数据
        if (numColumns != iter->count('\t')+1){
            QMessageBox::information(this, tr("data sheet"),
                                     tr("The data source is illegal and "
                                        "there may be line breaks in the data."));
            return;
        }
        ++iter;
    }

    if (range.rowCount() * range.columnCount() != 1
            && (range.rowCount() != numRows
                || range.columnCount() != numColumns)) {
        QMessageBox::information(this, tr("data sheet"),
                tr("The information cannot be pasted because the copy "
                   "and paste areas aren't the same size."));
        return;
    }
    blockSignals(true);
    for (int i = 0; i < numRows; ++i) {
        QStringList columns = rows[i].split('\t');
        for (int j = 0; j < numColumns; ++j) {
            int row = range.topRow() + i;
            int column = range.leftColumn() + j;
            if (row < rowCount() && column < columnCount())
                setFormula(row, column, columns[j]);
        }
    }
    blockSignals(false);
    somethingChanged();
}

void Spreadsheet::del()
{
    QList<QTableWidgetItem *> items = selectedItems();
    if (!items.isEmpty()) {
        foreach (QTableWidgetItem *item, items)
            delete item;
        somethingChanged();
    }
}

void Spreadsheet::selectCurrentRow()
{
    selectRow(currentRow());
}

void Spreadsheet::selectCurrentColumn()
{
    selectColumn(currentColumn());
}

void Spreadsheet::recalculate()
{
    for (int row = 0; row < rowCount(); ++row) {
        for (int column = 0; column < columnCount(); ++column) {
            if (cell(row, column))
                cell(row, column)->setDirty();
        }
    }
    viewport()->update();
}

void Spreadsheet::setAutoRecalculate(bool recalc)
{
    autoRecalc = recalc;
    if (autoRecalc)
        recalculate();
}

void Spreadsheet::findNext(const QString &str, Qt::CaseSensitivity cs)
{
    int row = currentRow();
    int column = currentColumn() + 1;

    while (row < rowCount()) {
        while (column < columnCount()) {
            if (text(row, column).contains(str, cs)) {
                clearSelection();
                setCurrentCell(row, column);
                activateWindow();
                return;
            }
            ++column;
        }
        column = 0;
        ++row;
    }
    QApplication::beep();
}

void Spreadsheet::findPrevious(const QString &str,
                               Qt::CaseSensitivity cs)
{
    int row = currentRow();
    int column = currentColumn() - 1;

    while (row >= 0) {
        while (column >= 0) {
            if (text(row, column).contains(str, cs)) {
                clearSelection();
                setCurrentCell(row, column);
                activateWindow();
                return;
            }
            --column;
        }
        column = columnCount() - 1;
        --row;
    }
    QApplication::beep();
}

void Spreadsheet::removeCurColumn()
{
    //sure ?
    // ...
    int col = currentColumn();
    if (col > 0)
        removeColumn(col);
    //update header
    // ...
}

void Spreadsheet::find()
{
    QString str = QInputDialog::getText(this, tr("Find"), tr("find the input from current cell."));
    findNext(str, Qt::CaseInsensitive);
}

void Spreadsheet::somethingChanged()
{
    if (autoRecalc)
        recalculate();
    emit modified();
}

void Spreadsheet::onCustomContextMenuRequested(const QPoint &/*pos*/)
{
    m_customMenu->exec(QCursor::pos());
}

void Spreadsheet::createCustomActions()
{
    m_customMenu->addAction("Copy", this, &Spreadsheet::copy);
    m_customMenu->addAction("Cut", this, &Spreadsheet::cut);
    m_customMenu->addAction("Paste", this, &Spreadsheet::paste);
    m_customMenu->addSeparator();
    m_customMenu->addAction("Find", this, &Spreadsheet::find);
}

Cell *Spreadsheet::cell(int row, int column) const
{
    return static_cast<Cell *>(item(row, column));
}

void Spreadsheet::setFormula(int row, int column,
                             const QString &formula)
{
    Cell *c = cell(row, column);
    if (!c) {
        c = new Cell;
        setItem(row, column, c);
    }
    c->setFormula(formula);
}

QString Spreadsheet::formula(int row, int column) const
{
    Cell *c = cell(row, column);
    if (c) {
        return c->formula();
    } else {
        return "";
    }
}

QString Spreadsheet::text(int row, int column) const
{
    Cell *c = cell(row, column);
    if (c) {
        return c->text();
    } else {
        return "";
    }
}

/**
 * @brief Spreadsheet::load
 * 将导出的sxlsFile文件中的内容加载到表格中
 * @param sxlsFile
 * @return true: 成功； false: 失败
 */
bool Spreadsheet::load(const QString &sxlsFile)
{
    QFile file(sxlsFile);
    if (!file.exists() ||
        !file.open(QIODevice::ReadOnly))
        return false;
    QDataStream ds(&file);
    ds >> *this;

    return true;
}

bool SpreadsheetCompare::operator()(const QStringList &row1,
                                    const QStringList &row2) const
{
    for (int i = 0; i < KeyCount; ++i) {
        int column = keys[i];
        if (column != -1) {
            if (row1[column] != row2[column]) {
                if (ascending[i]) {
                    return row1[column] < row2[column];
                } else {
                    return row1[column] > row2[column];
                }
            }
        }
    }
    return false;
}
