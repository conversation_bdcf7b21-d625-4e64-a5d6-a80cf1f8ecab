#include "uiUserManagement.h"
#include "ui_uiUserManagement.h"

uiUserManagement::uiUserManagement(QWidget *parent) :
    qUiWidget(parent),
    ui(new Ui::uiUserManagement)
{
    ui->setupUi(this);
}

uiUserManagement::~uiUserManagement()
{
    delete ui;
}

void uiUserManagement::initClass(QString& filePath)
{
    initUI(filePath);
}

bool uiUserManagement::initUI(QString& filePath)
{
    return true;
}
