/* const/gsl_const_num.h
 * 
 * Copyright (C) 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005,
 * 2006 <PERSON>
 * 
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 */

#ifndef __GSL_CONST_NUM__
#define __GSL_CONST_NUM__

#define GSL_CONST_NUM_FINE_STRUCTURE (7.297352533e-3) /* 1 */
#define GSL_CONST_NUM_AVOGADRO (6.02214199e23) /* 1 / mol */
#define GSL_CONST_NUM_YOTTA (1e24) /* 1 */
#define GSL_CONST_NUM_ZETTA (1e21) /* 1 */
#define GSL_CONST_NUM_EXA (1e18) /* 1 */
#define GSL_CONST_NUM_PETA (1e15) /* 1 */
#define GSL_CONST_NUM_TERA (1e12) /* 1 */
#define GSL_CONST_NUM_GIGA (1e9) /* 1 */
#define GSL_CONST_NUM_MEGA (1e6) /* 1 */
#define GSL_CONST_NUM_KILO (1e3) /* 1 */
#define GSL_CONST_NUM_MILLI (1e-3) /* 1 */
#define GSL_CONST_NUM_MICRO (1e-6) /* 1 */
#define GSL_CONST_NUM_NANO (1e-9) /* 1 */
#define GSL_CONST_NUM_PICO (1e-12) /* 1 */
#define GSL_CONST_NUM_FEMTO (1e-15) /* 1 */
#define GSL_CONST_NUM_ATTO (1e-18) /* 1 */
#define GSL_CONST_NUM_ZEPTO (1e-21) /* 1 */
#define GSL_CONST_NUM_YOCTO (1e-24) /* 1 */

#endif /* __GSL_CONST_NUM__ */
