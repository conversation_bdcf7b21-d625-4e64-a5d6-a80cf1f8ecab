/********************************************************************************
** Form generated from reading UI file 'uiMRMParameterEditor.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIMRMPARAMETEREDITOR_H
#define UI_UIMRMPARAMETEREDITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiMRMParameterEditor
{
public:
    QWidget *uiMRMParameterEditorWidget;
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget_chanel;

    void setupUi(QWidget *uiMRMParameterEditor)
    {
        if (uiMRMParameterEditor->objectName().isEmpty())
            uiMRMParameterEditor->setObjectName(QString::fromUtf8("uiMRMParameterEditor"));
        uiMRMParameterEditor->resize(586, 403);
        uiMRMParameterEditorWidget = new QWidget(uiMRMParameterEditor);
        uiMRMParameterEditorWidget->setObjectName(QString::fromUtf8("uiMRMParameterEditorWidget"));
        uiMRMParameterEditorWidget->setGeometry(QRect(140, 50, 278, 214));
        verticalLayout = new QVBoxLayout(uiMRMParameterEditorWidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        tableWidget_chanel = new QTableWidget(uiMRMParameterEditorWidget);
        if (tableWidget_chanel->columnCount() < 4)
            tableWidget_chanel->setColumnCount(4);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        tableWidget_chanel->setObjectName(QString::fromUtf8("tableWidget_chanel"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(tableWidget_chanel->sizePolicy().hasHeightForWidth());
        tableWidget_chanel->setSizePolicy(sizePolicy);
        tableWidget_chanel->setAlternatingRowColors(true);
        tableWidget_chanel->setSelectionMode(QAbstractItemView::ExtendedSelection);
        tableWidget_chanel->setSelectionBehavior(QAbstractItemView::SelectItems);
        tableWidget_chanel->horizontalHeader()->setDefaultSectionSize(95);
        tableWidget_chanel->horizontalHeader()->setStretchLastSection(true);
        tableWidget_chanel->verticalHeader()->setVisible(false);
        tableWidget_chanel->verticalHeader()->setMinimumSectionSize(20);
        tableWidget_chanel->verticalHeader()->setDefaultSectionSize(25);

        verticalLayout->addWidget(tableWidget_chanel);


        retranslateUi(uiMRMParameterEditor);

        QMetaObject::connectSlotsByName(uiMRMParameterEditor);
    } // setupUi

    void retranslateUi(QWidget *uiMRMParameterEditor)
    {
        uiMRMParameterEditor->setWindowTitle(QApplication::translate("uiMRMParameterEditor", "Form", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_chanel->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("uiMRMParameterEditor", "Precursor m/z", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_chanel->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("uiMRMParameterEditor", "Product m/z", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_chanel->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("uiMRMParameterEditor", "Dwell Time(ms)", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_chanel->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("uiMRMParameterEditor", "CE", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiMRMParameterEditor: public Ui_uiMRMParameterEditor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIMRMPARAMETEREDITOR_H
