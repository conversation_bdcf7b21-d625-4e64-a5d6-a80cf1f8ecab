#include "uiCalibrationMass.h"

#include <QSettings>


uiCalibrationMass::uiCalibrationMass(uiMapSetMZ* pMapSetMZ,
                                     QWidget *parent) :
    mMapSetMZ(pMapSetMZ),
    qUiWidget(parent)
{
ui.setupUi(this);
}

uiCalibrationMass::~uiCalibrationMass()
{

}

void uiCalibrationMass::initClass(QString& filePath)
{
    qUiWidget::initClass(filePath);
    mCalibrationViewQ1= new uiCalibrationView("Q1", mMapSetMZ, this);
    mCalibrationViewQ1->initClass(filePath);
    mCalibrationViewQ3= new uiCalibrationView("Q3", mMapSetMZ, this);
    mCalibrationViewQ3->initClass(filePath);
    initUI(filePath);
}

bool uiCalibrationMass::initUI(QString& filePath)
{
    ui.UI_LAYOUT_Q1ACQ_CAL->addWidget(mCalibrationViewQ1);
    //    connect(mCalibrationViewQ1, SIGNAL(sStartScan(sCalibrationView*)),
    //            parent, SLOT(onManualTuningScan(sCalibrationView*)));
    //connect(mCalibrationViewQ1, SIGNAL(setFormula(QString)), this, SLOT(onSetFormula(QString)));

    ui.UI_LAYOUT_Q3ACQ_CAL->addWidget(mCalibrationViewQ3);
    //    connect(mCalibrationViewQ3, SIGNAL(sStartScan(sCalibrationView*)),
    //            parent, SLOT(onManualTuningScan(sCalibrationView*)));
    //connect(mCalibrationViewQ1, SIGNAL(setFormula(QString)), this, SLOT(onSetFormula(QString)));
    createToolBar();

    return loadIniFromFile(filePath);
}

void uiCalibrationMass::createToolBar()
{

}

void uiCalibrationMass::on_UI_PB_SAVE_CAL_clicked()
{

}

void uiCalibrationMass::on_UI_PB_LOAD_CAL_clicked()
{

}

void uiCalibrationMass::on_UI_PB_SAVETO_CAL_clicked()
{

}

void uiCalibrationMass::on_UI_PB_APPLY_CAL_clicked()
{
    QString filePath ;//= QCoreApplication::applicationDirPath()+"/DebugParam.ini";
    if(!getFilePath(filePath))
        return;
    saveIniToFile(filePath);
}

void uiCalibrationMass::on_UI_PB_SCAN_CAL_clicked()
{
    if(ui.UI_TABWIDGET_PARAM_SINGLEACQ->currentIndex()== 0){
        emit sStartScan(mCalibrationViewQ1);
    }else{
        emit sStartScan(mCalibrationViewQ3);
    }
}

void uiCalibrationMass::on_UI_PB_STOP_CAL_clicked()
{
    if(ui.UI_TABWIDGET_PARAM_SINGLEACQ->currentIndex()== 0){
        emit sStopScan(mCalibrationViewQ1);
    }else{
        emit sStopScan(mCalibrationViewQ3);
    }
}
