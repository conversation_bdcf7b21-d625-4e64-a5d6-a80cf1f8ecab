#pragma once

#include <QtCore/qglobal.h>
#if defined(DATAFILTER_LIBRARY)
#  define DATAFILTER_LIBRARY_EXPORT Q_DECL_EXPORT
#else
#  define DATAFILTER_LIBRARY_EXPORT Q_DECL_IMPORT
#endif
#include <QStringList>
#include <QVector>
#include "Algorithm/FrameAverage.h"
#include "Processing/CSmoothingFilter.h"
#include "Processing/CNoiseSplineBaseline.h"

class DATAFILTER_LIBRARY_EXPORT cDataFilter
{
public:
    enum FilterDataType{
        MovingAverage=0,
        SavGol=1,
        FrameAverage=2,
        GainOffset=3,
        Baseline
    };
    struct FilterDataBase{
        double DataParam1;
    };
    struct MapParam{
        QVector<uint> Key;
        QVector<FilterDataBase*> Value;
    };
    struct FilterMovingAverage:FilterDataBase{};
    struct FilterGainOffset:FilterDataBase{
        bool bOffset=false;
        bool bGain=false;
        double DataParam2=1;
    };
    struct FilterFrameAverage:FilterDataBase{
        libDataFilter::cFrameAverage frameAverage;
    };
    struct FilterSavGol:FilterDataBase{
        int DataParam2;
    };
    enum BaseLineType{
        BasePoint=0,
        BaseLine=1,
        BaseDiagram=2
    };
    struct FilterBaseLine:FilterDataBase{
        BaseLineType DataType= BasePoint;
        QVector<double> DataParam2;
    };
    struct MethodParam{
        uint uVersion=0;
        QStringList ListParam;
    };
    cDataFilter(){}
    ~cDataFilter(){}
private:
    libDataFilter::HighFrequencyFilter mHighFrequencyFilter;
    libDataFilter::MovingAverageFilter mMovingAverageFilterServices;
    libDataFilter::WeightedMovingAverageFilter mWeightedMovingAverageFilterServices;
    libDataFilter::SavitzkyGolayFilter mSGFilterServices;
    uint mParamVersion = 0;
    MethodParam mMethodParam;
    MapParam mMapParam;
    std::vector<double> mDataTmp;
    QString mStrMethod;
    void updateMethod();
    double processData(std::vector<double>const& dataSrc, bool reStart= false);
    double processData(std::vector<double>const& dataSrc, std::vector<double>& dataDest, bool reStart= false);

    double processData(double* dataSrc, uint nSize, bool reStart= false);
    double processData(double* dataSrc, double* dataDest, uint nSize, bool reStart= false);


public:
    vector<double> HighFrequencySmooth(vector<double> data,double baseLine,int nPointFilter){
        return mHighFrequencyFilter.Filter(data,baseLine,nPointFilter);}
    vector<double> MovingAverageSmooth(vector<double>& data, int width){
        return mMovingAverageFilterServices.Filter(data, width);}
    vector<double> WeightedMovingAverageSmooth(vector<double>& data, int width){
        return mWeightedMovingAverageFilterServices.Filter(data, width);}
    vector<double> SavGolFilt(vector<double>& Val, int K, int F){
        return mSGFilterServices.Filter(Val, K, F);}

public:
    double getData(double* dataSrc, int sizeData, bool reStart= false){
        updateMethod();
        return processData(dataSrc, sizeData, reStart);
    }
    double getData(double* dataSrc, double* dataDest, int sizeData, bool reStart= false){
        updateMethod();
        return processData(dataSrc, dataDest, sizeData, reStart);
    }
    double getData(std::vector<double>const& dataSrc, std::vector<double>& dataDest, bool reStart= false){
        updateMethod();
        return processData(dataSrc, dataDest, reStart);
    }
    void setMethod(QString& strSructMethod);
    QString getMethod();
    void addTempMethod(QString& strSructMethod);
};

class sDataFilter0{
public:
    static cDataFilter* getDataProcess(){
        static sDataFilter0 insDataProcess;
        return &(insDataProcess.mDataProcess);
    }
private:
    cDataFilter mDataProcess;
    sDataFilter0(){}
    virtual ~sDataFilter0(){}
    sDataFilter0(const sDataFilter0&){}
    sDataFilter0& operator=(const sDataFilter0&){
        static sDataFilter0 insDataProcess;
        return insDataProcess;
    }
};

class sDataFilter1{
public:
    static cDataFilter* getDataProcess(){
        static sDataFilter1 insDataFilter1;
        return &(insDataFilter1.mDataProcess);
    }
private:
    cDataFilter mDataProcess;
    sDataFilter1(){}
    virtual ~sDataFilter1(){}
    sDataFilter1(const sDataFilter1&){}
    sDataFilter1& operator=(const sDataFilter1&){
        static sDataFilter1 insDataFilter1;
        return insDataFilter1;
    }
};

