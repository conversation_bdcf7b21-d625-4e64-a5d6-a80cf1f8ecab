#include "sCalibrationView.h"
#include <MassEventSplitter.h>
#include <UMA_HCS_Data.h>

void sCalibrationView::timerEvent(QTimerEvent *evt)
{
    if(evt->timerId() == mTimerRefreshID){
        if(mGraphBuffMutex.tryLock()){
            if(mIsNewMass){
                //for(int i=0; i< mCountEvt; i++){
                    //                    if(currentChart()==_CONGIG_OMS::STICK_CHART){//
                    //                        mMassChart[i]->setStyleM(TVPlot::Sticks);
                    //                        mMassChart[i]->setSamples((*mAbsc)[i],(*mOrd)[i], mMinLine);
                    //                    }else{
                    mChart->setStyleM(TVPlot::Lines);
                    mChart->setSamples(mGraphBuffX, mGraphBuffY);
                    //}
                    //mMassChart[0]->SetMarker((*mList_PEAK)[0].Absc,(*mList_PEAK)[i].Ord,(*mList_PEAK)[i].Marker);
                //}
            }
            mIsNewMass= false;
            mGraphBuffMutex.unlock();
        }
//        if(mSTRUCT_TIC.tryLock()){
//            if(mIsNewTIC){
//                quint32 sizeTIC= mSTRUCT_TIC.size();
//                if(mChartTIC)
//                    mChartTIC->setSamples(mSTRUCT_TIC.xTIC.data(), mSTRUCT_TIC.yTIC.data(), sizeTIC/*,false*/);
//                //mChartTIC->drawXIC(mSTRUCT_TIC.xTIC, sizeTIC);
//            }
//            mIsNewTIC= false;
//            mSTRUCT_TIC.unlock();
//        }
    }
}

void sCalibrationView::updateScanParam()
{
    //    m_pUMASegSplitter = HWConnection::getUSB()->getEvSegSplitterPtr();
    //    m_pMsEvtSplitter = ParamManager::global()->massEvtSplitter();
    //    m_umaEvents = ParamManager::global()->umaEvents();
    m_msSegments = m_pMsEvtSplitter->massSegments();
    //    uint accNum = QSettings().value(uma_project::getAccumulate(), 32).toUInt();
    //    uint daqFreq = QSettings().value(uma_project::getFrequency()).toUInt();
    //    m_umaPreparePoint = static_cast<uint>(daqFreq * m_prepareTime / 1000.0 / accNum);
    //    m_msPausePoint = static_cast<uint>(daqFreq * m_msPauseTime / 1000.0 / accNum);
    //    m_msSettingPoint = static_cast<uint>(daqFreq * m_msSettingTime / 1000.0 / accNum);
    //    muAccNumber = accNum;
    //    double refVol = QSettings().value(uma_project::getQqQRefVoltage(), 0).toDouble();///< 基准电压
    //    muRefVolDigit = static_cast<uint>((refVol + 4.197)/(2*4.197)*0xffff);//analog -> digit
    //    m_curMsSegment = -1;
}
/**
 * @brief QQQScanHandler::umaChMapMsChs
 * 匹配uma channel对应的ms channel列表，uma ch 的时间长度不能整除ms chs的时间时返回-1
 * @param chData 输入的uma ch数据
 * @param pMsChs 输出对应的ms chs
 * @return -1: 无法整除， 0： OK
 */
//int sCalibrationView::umaChMapMsChs(const UMA_HCS::HCSDataFrame::HCSData &chData)
//{
////    Q_ASSERT(pMsChs);
////    pMsChs->clear();
//    int msSegIndexB = 0, msEvtIndexB = 0, msChIndexB = 0;
//    int msSegIndexE = 0, msEvtIndexE = 0, msChIndexE = 0;
//    uint64_t lastTrigger = chData.lMassTriggerId + chData.lMassChannelCount - 1;
//    //first ms trigger
//    m_pMsEvtSplitter->getMsTriggerInfo(chData.lMassTriggerId, &msSegIndexB, &msEvtIndexB, &msChIndexB);
//    SDBG("ms begin: trigger,seg,evt,ch"
//         << chData.lMassTriggerId << msSegIndexB << msEvtIndexB << msChIndexB);
//    //last ms trigger
//    m_pMsEvtSplitter->getMsTriggerInfo(lastTrigger, &msSegIndexE, &msEvtIndexE, &msChIndexE);
//    SDBG("ms end..: trigger,seg,evt,ch" <<
//         lastTrigger << msSegIndexE << msEvtIndexE << msChIndexE);
//    if (msSegIndexB < 0 || msEvtIndexB < 0 || msChIndexB < 0 || msSegIndexE < 0 || msEvtIndexE < 0 || msChIndexE < 0)
//        return -2;
//    //get ms ch
////    while (msSegIndexB <= msSegIndexE){
////        auto& msSegment = m_msSegments[msSegIndexB];
////        while (msEvtIndexB <= msEvtIndexE)
////        {
////            auto& msEvt = msSegment.events[msEvtIndexB];
////            while (msChIndexB <= msChIndexE) {
////                pMsChs->append(QPair(msEvt.Type, msEvt.listChannel[msChIndexB]));
////                ++msChIndexB;
////            }
////            ++msEvtIndexB;
////        }
////        ++msSegIndexB;
////    }
////    uint msTime = 0;
////    for (auto& pMsCh : *pMsChs)
////        msTime += pMsCh.second.eventTime_ms();
//    return 0;
//}

//void sCalibrationView::updateScanInfo(int segIndex, int msEvtIndex, int msChIndex,
//                                        cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE umaType)
//{
//    Q_ASSERT(segIndex < m_msSegments.size());
//    mScanInfo.msEvtIndex = msEvtIndex;
//    mScanInfo.msChIndex = msChIndex;

//    if (segIndex == mScanInfo.segIndex && umaType == mScanInfo.type)
//        return;
//    mScanInfo.type = umaType;
//    mScanInfo.segIndex = segIndex;
//    //    emit scanInfoChanged(m_msSegments.at(segIndex), umaType);
//}

//void sCalibrationView::updateScanInfo(_STRUCT_ScanInfo& pScanInfo)
//{
//    Q_ASSERT(pScanInfo.segIndex < m_msSegments.size());
//    mScanInfo.msEvtIndex = pScanInfo.msEvtIndex;
//    mScanInfo.msChIndex = pScanInfo.msChIndex;

//    if (pScanInfo.segIndex == mScanInfo.segIndex && pScanInfo.type == mScanInfo.type)
//        return;
//    mScanInfo.type = pScanInfo.type;
//    mScanInfo.segIndex = pScanInfo.segIndex;
//}

int sCalibrationView::analyzeThread(void *pParam, const bool &bRunning)
{
    sCalibrationView* pCalibrationView = (sCalibrationView*)pParam;
    UMA_HCS::HCSCommandUSB *pHCSCommUSB= HWConnection::getUSB();

    pCalibrationView-> updateScanParam();

    double refVol = 0;//    double refVol = QSettings().value(uma_project::getQqQRefVoltage(), 0).toDouble();///< 基准电压
    uint    muRefVolDigit = 0;//analog -> digit
    if(!pCalibrationView->m_STRUCT_ADC_TDC.Mode){
        muRefVolDigit = static_cast<uint>((refVol + 4.197)/(2*4.197)*0xffff);//analog -> digit
    }

    UMA_HCS::HCSDataFrame dataFrame;
    double tic = 0;
    MsChData msChsData(0);//记录各ms ch的数据片

    const std::vector<MassEventSplitter::MassSegment>& tmpVector= pCalibrationView->m_pMsEvtSplitter->massSegments();

    while(bRunning){
        if (!pHCSCommUSB->getDataDAQ(&dataFrame)){
            QThread::msleep(10);
            continue;
        }
        auto chFrame = dataFrame.innerData();

        const MassEventSplitter::MassSegment& tmpMassSegment= tmpVector.at(chFrame.lUMASegmentNo);
        if(tmpMassSegment.events.size()<= chFrame.lUMAEventNo)
                    continue;
        const cTQ_StructCMD_HZH::_EVENT_PARAM& umaEvt= tmpMassSegment.events.at(chFrame.lUMAEventNo);
        int msChSize = umaEvt.listChannel.size();

        if (msChSize<= chFrame.lUMAChannelNo){
            msChsData.clear();
            continue;
        }

        if(msChsData.size()!= chFrame.lUMAChannelNo){
            msChsData.clear();
            continue;
        }
        
        if(!cAcqFunction::dataSplit(chFrame,
                                    pCalibrationView->m_STRUCT_ADC_TDC,
                                    muRefVolDigit,
                                    msChsData))
            continue;
        
        if ( msChsData.size()== msChSize){
            bool isOK= false;
            pCalibrationView->mGraphBuffMutex.lock();
            switch (umaEvt.Type) {
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SIM_NEG:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SIM_NEG:{

            break;}
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                    pCalibrationView->m_STRUCT_ADC_TDC,
                                                    msChsData/*[chIndex]*/,
                                                    1,
                                                    tic,
                                                    pCalibrationView->mGraphBuffX,
                                                    pCalibrationView->mGraphBuffY);
            break;}
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
            case cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:{
                isOK= cAcqFunction::fillChartBuffScan(umaEvt,
                                                    pCalibrationView->m_STRUCT_ADC_TDC,
                                                    msChsData/*[chIndex]*/,
                                                    3,
                                                    tic,
                                                    pCalibrationView->mGraphBuffX,
                                                    pCalibrationView->mGraphBuffY);
            break;}
            default:break;
            }
            if(isOK)
                pCalibrationView->mIsNewMass= true;
            pCalibrationView->mGraphBuffMutex.unlock();
        }
        QThread::usleep(1);
    }
    return 0;
}

