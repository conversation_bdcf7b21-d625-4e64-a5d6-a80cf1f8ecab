#include "cTQ_StructCMD_HZH.h"

cTQ_StructCMD_HZH::cTQ_StructCMD_HZH()
{

}

//void cTQ_StructCMD_HZH::Checksum(QVector<quint32>& pBuffer)
//{
//    quint32 lchksum= 0, nLength= pBuffer.size();
//    for(quint32 i=0;i<nLength;i++)
//        if(i!=127)
//            lchksum += pBuffer[i];
//    pBuffer[127]=lchksum;
//}

//void cTQ_StructCMD_HZH::Checksum(QByteArray& pBuffer)
//{
//    quint32 lchksum= 0, nLength= pBuffer.size()/sizeof(quint32);
//    quint32* tmpBuffer= (quint32*)(pBuffer.data());
//    for(quint32 i=0;i<nLength;i++)
//        if(i!=127)
//            lchksum += tmpBuffer[i];
//    tmpBuffer[127]=lchksum;
//}

//quint32 cTQ_StructCMD_HZH::checkSum(quint32* data, size_t size)
//{
//    quint32* pEnd = data + size;
//    quint32* pOffset = data;
//    quint32 uSum = 0;
//    while(pOffset < pEnd){
//        uSum += *pOffset;
//        pOffset++;
//    }
//    return uSum;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Start(QByteArray& pBuffer, bool extTrigger)
//{
//    pBuffer.resize(512);
//    memset(pBuffer.data(), 0, static_cast<quint32>(pBuffer.size()));
//    quint32* pUint32= reinterpret_cast<quint32*>(pBuffer.data());
//    pUint32[0] = TQ_CMD_MS_Start;
//    pUint32[1] = 0;//extTrigger;
//    pUint32[127]=pUint32[0]+pUint32[1];
//    return 0;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Start(QVector<quint32>& pBuffer, bool extTrigger)
//{
//    pBuffer.resize(128);
//    memset(pBuffer.data(), 0, static_cast<quint32>(pBuffer.size())* sizeof(quint32));
//    pBuffer[0] = TQ_CMD_MS_Start;
//    pBuffer[1] = 0;//extTrigger;
//    pBuffer[127]=pBuffer[0]+pBuffer[1];
//    return 0;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Halt(QByteArray& pBuffer)
//{
//    pBuffer.resize(512);
//    memset(pBuffer.data(), 0, static_cast<quint32>(pBuffer.size()));
//    quint32* pUint32= reinterpret_cast<quint32*>(pBuffer.data());
//    pUint32[0] = TQ_CMD_MS_Halt;
//    pUint32[127] = pUint32[0];
//    return 0;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Halt(QVector<quint32>& pBuffer)
//{
//    pBuffer.resize(128);
//    memset(pBuffer.data(), 0, static_cast<quint32>(pBuffer.size())* sizeof(quint32));
//    pBuffer[0] = TQ_CMD_MS_Halt;
//    pBuffer[127] = pBuffer[0];
//    return 0;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Configure(QVector<quint32>& pBuffer)
//{
//    pBuffer.resize(128);
//    memset(pBuffer.data(), 0, pBuffer.size()* sizeof(quint32));
//    pBuffer[0] = TQ_CMD_MS_Configure;
//    pBuffer[127] = pBuffer[0];
//    return 0;
//}

////int cTQ_StructCMD_HZH::cTQ_CMD_MS_Event_Param_Set(QVector<quint32>& pBuffer, _EVENT_PARAM_SET& pEVENT_PARAM_SET)
////{
////    pBuffer.resize(128);
////    memset(pBuffer.data(), 0, pBuffer.size()* sizeof(quint32));
////    pBuffer[0] = TQ_CMD_MS_Event_Param_Set;
////    pBuffer[127] = pBuffer[0];
////    return 0;
////}

//QByteArray cTQ_StructCMD_HZH::createEvent(_EVENT_PARAM& pEVENT_PARAM,
//                                    quint32& currentFrame,
//                                    quint32& currentEvent)
//{
//    quint32 nChannel= pEVENT_PARAM.listChannel.size();
//    if(nChannel< 1)
//        return QByteArray();
//    quint32 nChannelFrame= (nChannel+ 3)/ 4;
//    QByteArray data(FRAME_SIZE_UINT8, 0);
//    quint32* pDataHead32= reinterpret_cast<quint32*>(data.data());
//    pDataHead32[0]= currentFrame;           //指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
//    pDataHead32[1]= currentEvent;           //指示当前事件的序号(从0开始编号,最大编号为 <事件总数-1> )
//    pDataHead32[2]= pEVENT_PARAM.Type;      //指示当前事件的类型
//    pDataHead32[3]= *(reinterpret_cast<quint32*>(&(pEVENT_PARAM.TimeStartMin))); //指示当前事件的起始时间(时间单位为分钟)
//    pDataHead32[4]= *(reinterpret_cast<quint32*>(&(pEVENT_PARAM.TimeEndMin)));   //指示当前事件的结束时间(时间单位为分钟)
//    pDataHead32[5]= nChannel;               //指示当前事件包含通道数量
//    ++currentFrame;
//    quint32 currentChannel= 0;
//    for(quint32 indexChannelF= 0; indexChannelF< nChannelFrame; ++indexChannelF){
//        QByteArray tmpArray(FRAME_SIZE_UINT8, 0);

//        for(quint32 i= 0; i< 4; ++i){
//            quint32* pDataChannel32= reinterpret_cast<quint32*>(tmpArray.data())+ 0x20* i;
//            currentChannel= i+ indexChannelF* 4;
//            if(currentChannel== nChannel)
//                break;
//            _CHANNEL_EVENT_PARAM* pCHANNEL_EVENT_PARAM= &(pEVENT_PARAM.listChannel[currentChannel]);
//            if(i== 0){
//                pDataChannel32[0 ]= currentFrame;    //指示当前Frame的序号(从0开始编号，最大为<NODF - 1>)
//            }//pDataChannel32[1]= ;              //保留字段
//            pDataChannel32[2 ]= currentChannel;  //指示当前通道的序号(从0开始编号,最大编号为 <通道总数-1> )
//            pDataChannel32[3 ]= currentEvent;    //指示当前通道参数属于哪一个event
//            pDataChannel32[4 ]= pCHANNEL_EVENT_PARAM->VariableMask;//指示当前通道参数可变，每一位代表一个参数
//            //pDataChannel32[5]= ;              //保留字段
//            pDataChannel32[6 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q1_Mz_Start)));
//            pDataChannel32[7 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q1_Mz_End)));
//            pDataChannel32[8 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q2_Mz_Start)));
//            pDataChannel32[9 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q2_Mz_End)));
//            pDataChannel32[10 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q3_Mz_Start)));
//            pDataChannel32[11 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->Q3_Mz_End)));
//            float tmpT= pCHANNEL_EVENT_PARAM->PN_SwitchTimeMs/**1.05*/;
//            pDataChannel32[12 ]= *(reinterpret_cast<quint32*>(&(tmpT/*pCHANNEL_EVENT_PARAM->PN_SwitchTimeMs*/)));
//            tmpT= pCHANNEL_EVENT_PARAM->PauseTimeMs/**1.05*/;
//            pDataChannel32[13 ]= *(reinterpret_cast<quint32*>(&(tmpT/*pCHANNEL_EVENT_PARAM->PauseTimeMs*/)));
//            tmpT= pCHANNEL_EVENT_PARAM->WaitTimeMs/**1.05*/;
//            pDataChannel32[14 ]= *(reinterpret_cast<quint32*>(&(tmpT/*pCHANNEL_EVENT_PARAM->WaitTimeMs*/)));
//            tmpT= pCHANNEL_EVENT_PARAM->ScanTimeMs/**1.05*/;
//            pDataChannel32[15 ]= *(reinterpret_cast<quint32*>(&(tmpT/*pCHANNEL_EVENT_PARAM->ScanTimeMs*/)));
//            pDataChannel32[17 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->DC_CE)));
//            pDataChannel32[20 ]= *(reinterpret_cast<quint32*>(&(pCHANNEL_EVENT_PARAM->TDC_DAC)));
//            //pDataChannel32+= 16;
//        }
//        ++currentFrame;
//        data.append(tmpArray);
//    }
//    ++currentEvent;
//    return data;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_Event_Param_Set(QByteArray& pBuffer, _EVENT_PARAM_SET& pEVENT_PARAM_SET)
//{
//    int sizeEvent= pEVENT_PARAM_SET.listEvent.size();
//    if(sizeEvent< 1)
//        return -1;
//    pBuffer.resize(FRAME_SIZE_UINT8);
//    memset(pBuffer.data(), 0, FRAME_SIZE_UINT8);
//    quint32 noFrame=0;
//    quint32 noEvent=0;
//    for(int i= 0; i< sizeEvent; ++i)
//        pBuffer.append(createEvent(pEVENT_PARAM_SET.listEvent[i], noFrame, noEvent));
//    quint32* pData32= reinterpret_cast<quint32*>(pBuffer.data());
//    pData32[0]= TQ_CMD_MS_Event_Param_Set;//指令ID 指示当前指令事件配置指令
//    pData32[1]= noFrame;//指示当前指令后面DataFrame的个数
//    pData32[2]= noEvent;//指示当前扫描周期中事件的数量
//    pData32[3]= pEVENT_PARAM_SET.MinimumPeriod;//时钟最小周期数
//    return 0;
//}

//QByteArray cTQ_StructCMD_HZH::createParamFrameMZ(_MZ_PARAM_SET& p_MZ_PARAM_SET,
//                                           quint32& currentFrame)
//{
//    QByteArray data;
//    int sizeList= p_MZ_PARAM_SET.listMZ_PARAM.size();
//    if(sizeList> 21)
//        return data;
//    quint32 nMZ= (sizeList+ 6)/ 7;
//    int offsetFrame=0;
//    float tmpV=0;
//    for(int iFrame= 0; iFrame< nMZ; ++iFrame){
//        QByteArray tmpData;//(FRAME_SIZE_UINT8, '\0');
//        tmpData.resize(FRAME_SIZE_UINT8);
//        memset(tmpData.data(), 0, FRAME_SIZE_UINT8);
//        quint32* pDataHead32= reinterpret_cast<quint32*>(tmpData.data());
//        pDataHead32[0x00]= currentFrame+ iFrame;
//        pDataHead32[0x01]= p_MZ_PARAM_SET.Type;
//        if(iFrame< nMZ-1){
//            for(int i= 0/*iFrame* 7*/; i< 7; ++i, ++offsetFrame){
//                pDataHead32[0x10+ 16* i]= *reinterpret_cast<quint32*>(&(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].mz));
////                for(int offsetAddr= 0; offsetAddr< 12; ++offsetAddr){
////                    tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[offsetAddr]* 6553.5;
////                    pDataHead32[0x11+ offsetAddr+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
////                }
//                tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[0]* 6553.5;//Q1RF
//                pDataHead32[0x11+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[1]+ 10)* 65535.0/ 20.0;//Q1RESO
//                pDataHead32[0x12+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[2]* 6553.5;//Q3RF
//                pDataHead32[0x13+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[3]+ 10)* 65535.0/ 20.0;//Q3RESO
//                pDataHead32[0x14+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//            }
//        }else{//(sizeList% 3> 0? sizeList% 3: 3)
//            for(int i= 0/*iFrame* 7*/; i< (sizeList% 7> 0? sizeList% 7: 7); ++i, ++offsetFrame){
//                pDataHead32[0x10+ 16* i]= *reinterpret_cast<quint32*>(&(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].mz));
////                for(int offsetAddr= 0; offsetAddr< 12; ++offsetAddr){
////                    tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[offsetAddr]* 6553.5;
////                    pDataHead32[0x11+ offsetAddr+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
////                }
//                tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[0]* 6553.5;//Q1RF
//                pDataHead32[0x11+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[1]+ 10)* 65535.0/ 20.0;//Q1RESO
//                pDataHead32[0x12+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[2]* 6553.5;//Q3RF
//                pDataHead32[0x13+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//                tmpV=(p_MZ_PARAM_SET.listMZ_PARAM[offsetFrame].U[3]+ 10)* 65535.0/ 20.0;//Q3RESO
//                pDataHead32[0x14+ 16* i]= *reinterpret_cast<quint32*>(&tmpV);
//            }
//        }
//        data.append(tmpData);
//    }
//    currentFrame+= nMZ;
//    return data;
//}

//int cTQ_StructCMD_HZH::cTQ_CMD_MS_MZ_Param_Set(QByteArray& pBuffer,
//                                         QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET)
//{
//    int sizeEvent= list_MZ_PARAM_SET.size();
//    if(sizeEvent< 1)
//        return -1;
//    pBuffer.resize(FRAME_SIZE_UINT8);
//    memset(pBuffer.data(), 0, FRAME_SIZE_UINT8);
//    quint32 noFrame=0;
//    quint32 noMZ=0;
//    for(int i= 0; i< sizeEvent; ++i){
//        pBuffer.append(createParamFrameMZ(list_MZ_PARAM_SET[i], noFrame));
//        quint32* pData32= reinterpret_cast<quint32*>(pBuffer.data());
//        pData32[2+ i]= list_MZ_PARAM_SET[i].listMZ_PARAM.size();
//    }
//    quint32* pData32= reinterpret_cast<quint32*>(pBuffer.data());
//    pData32[0] = TQ_CMD_MS_MZ_Param_Set;
//    pData32[1] = noFrame;
//    return 0;
//}

//#define Q1_SCAN_RF_DC 0//7
//#define Q3_SCAN_RF_DC 2//10

//int cTQ_StructCMD_HZH::linearInterpolation(_EVENT_PARAM& p_EVENT_PARAM,
//                                     const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET)
//{
//    bool ok= false;
//    for(const auto& p_MZ_PARAM_SET: list_MZ_PARAM_SET){
//        if(p_EVENT_PARAM.Type!= p_MZ_PARAM_SET.Type)
//            continue;
//        int listSize= p_EVENT_PARAM.listChannel.size();
//        if(listSize< 1)
//            return -1;
//        for(auto& pChannel: p_EVENT_PARAM.listChannel){
//            switch (p_EVENT_PARAM.Type) {
//            case EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
//            case EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
//                _MZ_PARAM_FRAME_DATA start_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_Start, &ok);
//                if(!ok)
//                    return -1;
//                _MZ_PARAM_FRAME_DATA end_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_End, &ok);
//                if(!ok)
//                    return -1;
//                int stepPoint= pChannel.ScanTimeMs/ 0.01;
//                _MZ_PARAM_FRAME_DATA step(0);
//                //for(int i=0; i< 12; ++i){
//                step.U[Q1_SCAN_RF_DC]= (end_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC])/ stepPoint;
//                int stepV= static_cast<int>(step.U[Q1_SCAN_RF_DC]* 1e8);
//                stepV= (stepV+ 118)/ 119* 119;//电压步进1.19e-6v
//                int excessV= static_cast<int>(static_cast<double>(stepV* stepPoint)-
//                                              (end_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC])
//                                              *1e8);
//                stepPoint-= qAbs(excessV+ 118)/ 119;
//                pChannel.WaitTimeMs+= pChannel.ScanTimeMs- static_cast<float>(stepPoint* 0.01);
//                pChannel.ScanTimeMs= static_cast<float>(stepPoint* 0.01);
//                break;}
//            case EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
//            case EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:{
//                _MZ_PARAM_FRAME_DATA start_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_Start, &ok);
//                if(!ok)
//                    return -1;
//                _MZ_PARAM_FRAME_DATA end_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_End, &ok);
//                if(!ok)
//                    return -1;
//                int stepPoint= pChannel.ScanTimeMs/ 0.01;
//                _MZ_PARAM_FRAME_DATA step(0);
//                //for(int i=0; i< 12; ++i){
//                step.U[Q1_SCAN_RF_DC]= (end_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC])/ stepPoint;
//                int stepV= static_cast<int>(step.U[Q3_SCAN_RF_DC]* 1e8);
//                stepV= (stepV+ 118)/ 119* 119;//电压步进1.19e-6v
//                int excessV= static_cast<int>(static_cast<double>(stepV* stepPoint)-
//                                              (end_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC])
//                                              *1e8);
//                stepPoint-= qAbs(excessV+ 118)/ 119;
//                pChannel.WaitTimeMs+= pChannel.ScanTimeMs- static_cast<float>(stepPoint* 0.01);
//                pChannel.ScanTimeMs= static_cast<float>(stepPoint* 0.01);
//                break;}
//            default:break;
//            }
//        }
//    }
//    return 1;
//}

//int cTQ_StructCMD_HZH::linearInterpolation(const EVENT_PARAM_HEAD_TYPE Type,
//                                     _CHANNEL_EVENT_PARAM& pChannel,
//                                     const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET)
//{
//    bool ok= false;
//    for(const auto& p_MZ_PARAM_SET: list_MZ_PARAM_SET){
//        if(Type!= p_MZ_PARAM_SET.Type)
//            continue;
//        switch (Type) {
//        case EVENT_PARAM_HEAD_TYPE_Q1SCAN_POS:
//        case EVENT_PARAM_HEAD_TYPE_Q1SCAN_NEG:{
//            _MZ_PARAM_FRAME_DATA start_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_Start, &ok);
//            if(!ok)
//                return -1;
//            _MZ_PARAM_FRAME_DATA end_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_End, &ok);
//            if(!ok)
//                return -1;
//            int stepPoint= pChannel.ScanTimeMs/ 0.01;
//            _MZ_PARAM_FRAME_DATA step(0);
//            //for(int i=0; i< 12; ++i){
//            step.U[Q1_SCAN_RF_DC]= qAbs(end_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC])/ stepPoint;
//            int stepV= static_cast<int>(step.U[Q1_SCAN_RF_DC]* 1e8);
//            stepV= (stepV+ 118)/ 119* 119;//电压步进1.19e-6v
//            if(stepV<1)
//                return -2;
//            int excessV= static_cast<int>(static_cast<double>(stepV* stepPoint)-
//                                          qAbs(end_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q1_SCAN_RF_DC])
//                                          *1e8);
//            stepPoint-= qAbs(excessV+ stepV-1)/ stepV;
//            pChannel.WaitTimeMs+= pChannel.ScanTimeMs- static_cast<float>(stepPoint* 0.01);
//            pChannel.ScanTimeMs= static_cast<float>(stepPoint* 0.01);
//            break;}
//        case EVENT_PARAM_HEAD_TYPE_Q3SCAN_POS:
//        case EVENT_PARAM_HEAD_TYPE_Q3SCAN_NEG:{
//            _MZ_PARAM_FRAME_DATA start_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_Start, &ok);
//            if(!ok)
//                return -1;
//            _MZ_PARAM_FRAME_DATA end_MZ_PARAM_FRAME_DATA= p_MZ_PARAM_SET.getMZParameters(pChannel.Q1_Mz_End, &ok);
//            if(!ok)
//                return -1;
//            int stepPoint= pChannel.ScanTimeMs/ 0.01;
//            _MZ_PARAM_FRAME_DATA step(0);
//            //for(int i=0; i< 12; ++i){
//            step.U[Q1_SCAN_RF_DC]= qAbs(end_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC])/ stepPoint;
//            int stepV= static_cast<int>(step.U[Q3_SCAN_RF_DC]* 1e8);
//            stepV= (stepV+ 118)/ 119* 119;//电压步进1.19e-6v
//            if(stepV<1)
//                return -2;
//            int excessV= static_cast<int>(static_cast<double>(stepV* stepPoint)-
//                                          qAbs(end_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC]- start_MZ_PARAM_FRAME_DATA.U[Q3_SCAN_RF_DC])
//                                          *1e8);
//            stepPoint-= qAbs(excessV+ stepV-1)/ stepV;
//            pChannel.WaitTimeMs+= pChannel.ScanTimeMs- static_cast<float>(stepPoint* 0.01);
//            pChannel.ScanTimeMs= static_cast<float>(stepPoint* 0.01);
//            break;}
//        default:break;
//        }
//    }
//    return 1;
//}
