<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PreferenceEditor</class>
 <widget class="QWidget" name="PreferenceEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>654</width>
    <height>840</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>635</width>
        <height>963</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QGroupBox" name="groupBox_analysisMode">
         <property name="title">
          <string>Analysis Mode</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButtonFilterScan">
            <property name="text">
             <string>Filter-Scan</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButtonSIM">
            <property name="text">
             <string>Filter-SIM</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButtonTrapScan">
            <property name="text">
             <string>Trap-Scan</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButtonThrough">
            <property name="text">
             <string>Through(non-molibity)</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_MassRange">
         <property name="title">
          <string>Mass Range</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_MassParameter">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QLabel" name="label">
              <property name="font">
               <font>
                <italic>true</italic>
               </font>
              </property>
              <property name="text">
               <string>Method:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_method">
              <property name="enabled">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_loadMethod">
              <property name="statusTip">
               <string>Load parameters in LabSolution method file.</string>
              </property>
              <property name="text">
               <string>LoadFromMethod</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="Line" name="line">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,1,1,1">
            <property name="spacing">
             <number>20</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <widget class="QLabel" name="label_3">
                <property name="font">
                 <font>
                  <family>Arial</family>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>Event NO.</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_EventNO">
                <property name="styleSheet">
                 <string notr="true">color: rgb(85, 170, 255);</string>
                </property>
                <property name="text">
                 <string>#</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QLabel" name="label_4">
                <property name="font">
                 <font>
                  <family>Arial</family>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>Polarity</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_Polarity">
                <property name="styleSheet">
                 <string notr="true">color: rgb(85, 170, 255);</string>
                </property>
                <property name="text">
                 <string>#</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <item>
               <widget class="QLabel" name="label_7">
                <property name="font">
                 <font>
                  <family>Arial</family>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>Type</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_EventType">
                <property name="styleSheet">
                 <string notr="true">color: rgb(85, 170, 255);</string>
                </property>
                <property name="text">
                 <string>#</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_umaParameter">
         <property name="title">
          <string>UMA Parameters</string>
         </property>
         <layout class="QGridLayout" name="gridLayout" rowstretch="0,0,0" columnstretch="0,0,0">
          <property name="horizontalSpacing">
           <number>20</number>
          </property>
          <item row="0" column="2">
           <widget class="ParameterEditor" name="editor_E_End" native="true"/>
          </item>
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="font">
               <font>
                <family>Arial</family>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>Polarity</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="comboBox_PNMode">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <family>Arial</family>
               </font>
              </property>
              <property name="statusTip">
               <string>Ion polarity, modify by method file.</string>
              </property>
              <property name="whatsThis">
               <string>Ion polarity, modify by method file.</string>
              </property>
              <item>
               <property name="text">
                <string>Positive</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Negative</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="1">
           <widget class="ParameterEditor" name="editor_DE" native="true"/>
          </item>
          <item row="0" column="1">
           <widget class="ParameterEditor" name="editor_E_Start" native="true"/>
          </item>
          <item row="2" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_11">
            <item>
             <widget class="QLabel" name="label_5">
              <property name="font">
               <font>
                <family>Arial</family>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>Resolution</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="comboBox_resolution">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="font">
               <font>
                <family>Arial</family>
               </font>
              </property>
              <property name="statusTip">
               <string/>
              </property>
              <property name="whatsThis">
               <string/>
              </property>
              <item>
               <property name="text">
                <string>High</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Low</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Custom</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item row="2" column="2">
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>17</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="2">
           <widget class="ParameterEditor" name="editor_AccPeriod" native="true"/>
          </item>
          <item row="1" column="0">
           <widget class="ParameterEditor" name="editor_ScanPeroid" native="true"/>
          </item>
          <item row="2" column="1">
           <widget class="ParameterEditor" name="editor_GasSpeed" native="true"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_firmware_tof" native="true"/>
       </item>
       <item>
        <widget class="QWidget" name="widget_firmware_QQQ" native="true"/>
       </item>
       <item>
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>368</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_advance">
           <property name="statusTip">
            <string>Calibration electrode parameters.</string>
           </property>
           <property name="text">
            <string>Advance...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QWidget" name="widget_advance" native="true">
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_9">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>9</number>
            </property>
            <property name="bottomMargin">
             <number>9</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_pic">
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="../images/images.qrc">:/UMA1.png</pixmap>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_9">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_10">
            <property name="leftMargin">
             <number>9</number>
            </property>
            <property name="rightMargin">
             <number>9</number>
            </property>
            <item>
             <widget class="ParameterEditor" name="currentMZ" native="true">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>170</width>
                <height>16777215</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_getMZParam">
              <property name="statusTip">
               <string>Get the electrode parameters of the current mass</string>
              </property>
              <property name="whatsThis">
               <string>Get the electrode parameters of the current mass</string>
              </property>
              <property name="accessibleName">
               <string/>
              </property>
              <property name="text">
               <string>Get</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_10">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_SaveMZ">
              <property name="statusTip">
               <string>Save the electrode parameters of the current mass to project.</string>
              </property>
              <property name="whatsThis">
               <string>Save the electrode parameters of the current mass to project.</string>
              </property>
              <property name="text">
               <string>Save MZ</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ParameterEditor</class>
   <extends>QWidget</extends>
   <header>LibWidget/ParameterEditor.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../images/images.qrc"/>
 </resources>
 <connections/>
</ui>
