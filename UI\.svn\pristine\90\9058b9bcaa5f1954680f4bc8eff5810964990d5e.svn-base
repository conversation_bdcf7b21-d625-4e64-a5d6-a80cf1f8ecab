#include "uiDeviceControlMASS.h"
#include "ui_uiDeviceControlMASS.h"

uiDeviceControlMASS::uiDeviceControlMASS(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::uiDeviceControlMASS)
{
    ui->setupUi(this);
    mEditMRM= new uiEditMRM(this);
    ui->UI_SW_DCM->addWidget(mEditMRM);
    mNeutralLoss= new uiNeutralLoss(this);
    ui->UI_SW_DCM->addWidget(mNeutralLoss);
    mEditProductIon= new uiEditProductIon(this);
    ui->UI_SW_DCM->addWidget(mEditProductIon);
    mPrecursorIon= new uiPrecursorIon(this);
    ui->UI_SW_DCM->addWidget(mPrecursorIon);
    mQ1FullScan= new uiQ1FullScan(this);
    ui->UI_SW_DCM->addWidget(mQ1FullScan);
    mQ1SIM= new uiQ1SIM(this);
    ui->UI_SW_DCM->addWidget(mQ1SIM);
    mQ3FullScan= new uiQ3FullScan(this);
    ui->UI_SW_DCM->addWidget(mQ3FullScan);
    mQ3SIM= new uiQ3SIM(this);
    ui->UI_SW_DCM->addWidget(mQ3SIM);
    ui->UI_SW_DCM->setCurrentIndex(0);
}

uiDeviceControlMASS::~uiDeviceControlMASS()
{
    delete ui;
}

void uiDeviceControlMASS::on_UI_RECIPE_LIST_DCM_itemClicked(QListWidgetItem *item)
{
    //int currentRow= ui->UI_RECIPE_LIST_DCM->row(item);
    QString str;
    foreach (auto& key, mListExpenment.keys()) {
        if(key!= item)
            continue;

        QString typeName= mListExpenment[key].first;
        if(typeName== "Scheduled MRM"){
            mEditMRM->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(0);
        }else if(typeName== "Neutral Loss"){
            mNeutralLoss->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(1);
        }else if(typeName== "Procursor Ion"){
            mEditProductIon->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(2);
        }else if(typeName== "Product Ion"){
            mPrecursorIon->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(3);
        }else if(typeName== "Q1 Scan"){
            mQ1FullScan->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(4);
        }else if(typeName== "Q1 SIM"){
            mQ1SIM->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(5);
        }else if(typeName== "Q3 Scan"){
            mQ3FullScan->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(6);
        }else if(typeName== "Q3 SIM"){
            mQ3SIM->setParam(str);
            ui->UI_SW_DCM->setCurrentIndex(7);
        }else
            return;

    }
}

void uiDeviceControlMASS::on_UI_RECIPE_ADD_DCM_clicked()
{
    QString typeName;
    switch (ui->UI_RECIPE_CB_DCM->currentIndex()) {
    case 0://"Scheduled MRM"
        typeName= "Scheduled MRM";
        break;
    case 1://"Neutral Loss"
        typeName= "Neutral Loss";
        break;
    case 2://"Procursor Ion"
        typeName= "Procursor Ion";
        break;
    case 3://
        typeName= "Product Ion";
        break;
    case 4://
        typeName="Q1 Scan";
        break;
    case 5://
        typeName="Q1 SIM";
        break;
    case 6://
        typeName="Q3 Scan";
        break;
    case 7://
        typeName="Q3 SIM";
        break;
    default:
        return;
    }
    QListWidgetItem *item =new QListWidgetItem(ui->UI_RECIPE_CB_DCM->currentText());
    ui->UI_RECIPE_LIST_DCM->addItem(item);
    QString str= mEditMRM->getParam();
    mListExpenment[item]= QPair(typeName, str);
}
