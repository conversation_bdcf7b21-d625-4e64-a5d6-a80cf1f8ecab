#pragma once

#include <uiMethod/cTQ_StructCMD_AD81416.h>

class cTQ_FunctionCMD_AD81416
{
public:
    cTQ_FunctionCMD_AD81416();
    static int cCMD_AD81416_Start(QByteArray& p<PERSON><PERSON>er, bool extTrigger= false);
    static int cCMD_AD81416_Start(QVector<quint32>& pBuffer, bool extTrigger= false);
    static int cTQ_CMD_MS_Halt(QByteArray& pBuffer);
    static int cTQ_CMD_MS_Halt(QVector<quint32>& pBuffer);
    static int cTQ_CMD_MS_Configure(QVector<quint32>& pBuffer);
    static int cTQ_CMD_MS_Event_Param_Set(QByteArray& pBuffer,
                                          cTQ_StructCMD_AD81416::_EVENT_PARAM_SET& pEVENT_PARAM_SET);
    static int cTQ_CMD_MS_MZ_Param_Set(QByteArray& pBuffer,
                                       QList<cTQ_StructCMD_AD81416::_MZ_PARAM_SET>& list_MZ_PARAM_SET);
//    static int linearInterpolation(_EVENT_PARAM& p_EVENT_PARAM,
//                                   const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET);
//    static int linearInterpolation(const cTQ_StructCMD_HZH::EVENT_PARAM_HEAD_TYPE Type,
//                                   _CHANNEL_EVENT_PARAM& pChannel,
//                                   const QList<_MZ_PARAM_SET>& list_MZ_PARAM_SET);

private:
    static QByteArray createEvent(cTQ_StructCMD_AD81416::_EVENT_PARAM& pEVENT_PARAM,
                                  quint32& currentFrame,
                                  quint32& currentEvent);
    static QByteArray createParamFrameMZ(cTQ_StructCMD_AD81416::_MZ_PARAM_SET& p_MZ_PARAM_SET,
                                  quint32& currentFrame);
};

