#pragma once

#include <QWidget>
#include <qUiWidget.h>
#include "ui_uiState.h"
#include "uiState/uiCtrlTFG.h"
#include <uiMainWindow/sListWidgetForLog.h>

class uiState : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiState(QWidget *parent = nullptr);
    ~uiState();
    virtual void initClass(QString& filePath);
    sListWidgetForLog* mListWidgetForLog= nullptr;
    uiCtrlTFG* mCtrlTFG= nullptr;

protected:
    bool initUI(QString& filePath);
    virtual void createToolBar();

private:
    Ui::uiState ui;

};

