#include "uiCalibrationView.h"
#include <QSettings>

uiCalibrationView::uiCalibrationView(QString pro,
                                   uiMapSetMZ* pMapSetMZ,
                                   QWidget *parent) :
    mPro(pro),
    mMapSetMZ(pMapSetMZ),
    qUiWidget(parent)
{
    ui.setupUi(this);
}

uiCalibrationView::~uiCalibrationView()
{
    for(auto CalibrationMassItem: mCalibrationMassItemMap){
        if(CalibrationMassItem)
            delete CalibrationMassItem;
        CalibrationMassItem= nullptr;
    }
    mCalibrationMassItemMap.clear();
//    if(mTimerRefreshID!=-1)
//        killTimer(mTimerRefreshID);
//    mTimerRefreshID=-1;
//    if(mAnalyzeThread){
//        if(mAnalyzeThread->isRunning()){
//            mAnalyzeThread->stop();
//            mAnalyzeThread->wait();
//        }
//        delete mAnalyzeThread;
//        mAnalyzeThread= nullptr;
//    }
    if(mChart)
        delete mChart;
    mChart= nullptr;
}

void uiCalibrationView::initClass(QString& filePath)
{
    mQ13ScanParamEditor= new uiQ13ScanParamEditor(mPro,
                                                //pMapSetMZ,
                                                this);
    mQ13ScanParamEditor->initClass(filePath);


    //m_pMsEvtSplitter= new MassEventSplitter();
    initUI(filePath);
}

bool uiCalibrationView::initUI(QString& filePath)
{
    ui.UI_LAYOUT_METHOD_CAL->insertWidget(0, mQ13ScanParamEditor);
    mChart = new sChartWidget(sChartWidget::_TUNING_CHART);
    ui.UI_LAYOUT_ACQ_CAL->addWidget(mChart);

    createToolBar();
    return true;
}

void uiCalibrationView::createToolBar()
{

}

void uiCalibrationView::on_UI_PB_ADDMASS_CAL_clicked()
{
    QString str= ui.UI_LE_SPEED_CAL->text();
    bool ok=false;
    double speed= str.toDouble(&ok);
    if(!ok)
        return;
    uiCalibrationMassItem* pCalibrationMassItem= new uiCalibrationMassItem(this);
    pCalibrationMassItem->setScanSpeed(str);
    connect(pCalibrationMassItem, SIGNAL(focused(QString)), this, SLOT(onFocused(QString)));
    mCalibrationMassItemMap.insert(speed, pCalibrationMassItem);
    //ui.UI_LAYOUT_SPEEDMAP_CAL->addWidget(pCalibrationMassItem);
    for(auto CalibrationMassItem: mCalibrationMassItemMap){
        if(CalibrationMassItem){
            //ui.UI_LAYOUT_SPEEDMAP_CAL->removeWidget(CalibrationMassItem);
            ui.UI_LAYOUT_SPEEDMAP_CAL->insertWidget(0, CalibrationMassItem);
        }
    }
}

void uiCalibrationView::on_UI_PB_DELETEMASS_CAL_clicked()
{
    if(!mCurrentFocus)
        return;
    for(auto CalibrationMassItem: mCalibrationMassItemMap){
        if(CalibrationMassItem){
            ui.UI_LAYOUT_SPEEDMAP_CAL->removeWidget(CalibrationMassItem);
            disconnect(CalibrationMassItem, nullptr, nullptr, nullptr);
            delete CalibrationMassItem;
            CalibrationMassItem= nullptr;
            return;
        }
    }

}

//void uiCalibrationView::on_UI_PB_SCAN_CAL_clicked()
//{
//    emit sStartScan(this);
//}

//void uiCalibrationView::on_UI_PB_STOP_CAL_clicked()
//{
//    emit sStopScan(this);
//}
