/* gsl_types.h
 * 
 * Copyright (C) 2001 <PERSON>
 * 
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 */

#ifndef __GSL_TYPES_H__
#define __GSL_TYPES_H__

#ifndef GSL_VAR

#ifdef WIN32
#  ifdef GSL_DLL
#    ifdef DLL_EXPORT
#      define GSL_VAR extern __declspec(dllexport)
#      define GSL_EXPORT __declspec(dllexport)
#    else
#      define GSL_VAR extern __declspec(dlli<PERSON>rt)
#      define GSL_EXPORT __declspec(dllimport)
#    endif
#  else
#    define GSL_VAR extern
#    define GSL_EXPORT
#  endif
#else
#  define GSL_VAR extern
#  define GSL_EXPORT
#endif

#endif

#endif /* __GSL_TYPES_H__ */
