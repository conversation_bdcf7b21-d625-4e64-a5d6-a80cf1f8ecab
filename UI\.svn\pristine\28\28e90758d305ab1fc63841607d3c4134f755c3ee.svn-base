#pragma once

#include <qwidget.h>

#include <QFile>
#include <QListWidget>
#include <qUiWidget.h>
//#include <QMainWindow>
#include <QTextStream>

/**
 * @brief
 *
 */
class sListWidgetForLog : public qUiWidget
{
    Q_OBJECT
public:
    /**
     * @brief
     *
     * @param mainWindow
     * @param parent
     */
    explicit sListWidgetForLog(/*QMainWindow *mainWindow, */QWidget *parent = 0);

    ~sListWidgetForLog();
    void initClass(QString& filePath);

    protected:
        bool initUI(QString& filePath);


    /**
     * @brief
     *
     * @param evt
     */
    void timerEvent(QTimerEvent* evt);


    QListWidget* m_logList; /**< TODO */
    int m_timerID = 0; /**< TODO */
    int m_logID = 0; /**< TODO */
    QFile m_file;
    QTextStream m_fileTextStream;
};

