#pragma once

#include "uiEditMRM.h"
#include "uiEditProductIon.h"
#include "uiNeutralLoss.h"
#include "uiPrecursorIon.h"
#include "uiQ1FullScan.h"
#include "uiQ3FullScan.h"
#include "uiQ1SIM.h"
#include "uiQ3SIM.h"
#include <QListWidgetItem>
#include <QWidget>
#include <uiMethod/cTQ_StructCMD_HZH.h>

namespace Ui {
class uiDeviceControlMASS;
}

class uiDeviceControlMASS : public QWidget
{
    Q_OBJECT

public:
    explicit uiDeviceControlMASS(QWidget *parent = nullptr);
    ~uiDeviceControlMASS();

private slots:
    void on_UI_RECIPE_LIST_DCM_itemClicked(QListWidgetItem *item);

    void on_UI_RECIPE_ADD_DCM_clicked();

private:
    Ui::uiDeviceControlMASS *ui;
    QHash<QListWidgetItem *,QPair<QString, QString>> mListExpenment;
    uiEditMRM* mEditMRM= nullptr;
    uiEditProductIon* mEditProductIon= nullptr;
    uiNeutralLoss* mNeutralLoss= nullptr;
    uiPrecursorIon* mPrecursorIon= nullptr;
    uiQ1FullScan* mQ1FullScan= nullptr;
    uiQ3FullScan* mQ3FullScan= nullptr;
    uiQ1SIM* mQ1SIM= nullptr;
    uiQ3SIM* mQ3SIM= nullptr;
};

