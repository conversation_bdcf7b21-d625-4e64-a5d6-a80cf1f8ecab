#pragma once
#include <QWidget>
#include <qUiWidget.h>

namespace Ui {
class uiDeviceControl;
}

class uiDeviceControl : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiDeviceControl(QWidget *parent = nullptr);
    ~uiDeviceControl();
    void initClass(QString& filePath){
        initUI(filePath);
    }

protected:
    virtual bool initUI(QString& filePath){

    }
private:
    Ui::uiDeviceControl *ui;
};

