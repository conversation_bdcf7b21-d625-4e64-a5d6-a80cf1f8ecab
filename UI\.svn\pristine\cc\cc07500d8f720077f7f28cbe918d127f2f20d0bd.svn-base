#ifndef CMATHHZH_H
#define CMATHHZH_H
#include <QString>
#include <QtMath>
#include <cMatrix.h>

class cMathHZH{
public:
static double calculateR2(const std::vector<double> &actualValues, const std::vector<double> &predictedValues)
{
    if (actualValues.size() != predictedValues.size()) {
        return 0.0;
    }

    double sumSquaresTotal = 0.0;
    double sumSquaresResidual = 0.0;

    for (int i = 0; i < actualValues.size(); ++i) {
        double actual = actualValues[i];
        double predicted = predictedValues[i];

        sumSquaresTotal += qPow(actual, 2);
        double residual = actual - predicted;
        sumSquaresResidual += qPow(residual, 2);
    }

    double R2 = 1 - (sumSquaresResidual / sumSquaresTotal);
    return qBound(0.0, R2, 1.0);
}


};

#endif // CMATHHZH_H
