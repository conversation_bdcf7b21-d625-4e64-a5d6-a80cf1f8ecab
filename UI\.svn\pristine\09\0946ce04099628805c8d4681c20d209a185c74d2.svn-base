#ifndef UIDEVICECONTROLMASS_H
#define UIDEVICECONTROLMASS_H

#include "uiEditMRM.h"

#include <QListWidgetItem>
#include <QWidget>

namespace Ui {
class uiDeviceControlMASS;
}

class uiDeviceControlMASS : public QWidget
{
    Q_OBJECT

public:
    explicit uiDeviceControlMASS(QWidget *parent = nullptr);
    ~uiDeviceControlMASS();

private slots:
    void on_UI_RECIPE_LIST_DCM_itemClicked(QListWidgetItem *item);

    void on_UI_RECIPE_ADD_DCM_clicked();

private:
    Ui::uiDeviceControlMASS *ui;
    QList<QPair<QString, QString>> mListExpenment;
    uiEditMRM* mEditMRM;
};

#endif // UIDEVICECONTROLMASS_H
