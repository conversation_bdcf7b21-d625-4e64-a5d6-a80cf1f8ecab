#pragma once

#include "ui_uiWindowMenu.h"
#include <QMouseEvent>
#include <QWidget>
#include <LibWidget/sMyButton.h>
#include <LibWidget/sClickWidget.h>

namespace Ui {
class uiWindowMenu;
}

class uiWindowMenu : public sClickWidget
{
    Q_OBJECT

public:
    explicit uiWindowMenu(QWidget *parent = nullptr);
    ~uiWindowMenu();

private:
    Ui::uiWindowMenu ui;
    MyWidget::sMyButton* mMinButton;
    MyWidget::sMyButton* mMaxButton;
    MyWidget::sMyButton* mCloseButton;

private slots:
    void onMouseMove(){
        if(!mWorkWidget)
            return;
//        if(mParent->isMaximized()){
//            mMaxButton->setPicture(QPixmap(":/Menu/sMainWindow/picture/max_32.png"),
//                                   QPixmap(":/Menu/sMainWindow/picture/max_32.png"),
//                                   QPixmap(":/Menu/sMainWindow/picture/max_press_32.png"));
//            mParent->showNormal();
//        }
    }
    void onClickedShowNormal(){
        if(!mWorkWidget)
            return;
        if(mWorkWidget->isMaximized()){
            mMaxButton->setPicture(QPixmap(":/Menu/sMainWindow/picture/max_32.png"),
                                   QPixmap(":/Menu/sMainWindow/picture/max_32.png"),
                                   QPixmap(":/Menu/sMainWindow/picture/max_press_32.png"));
            mWorkWidget->showNormal();
        }else{
            mMaxButton->setPicture(QPixmap(":/Menu/sMainWindow/picture/Normal_32.png"),
                                   QPixmap(":/Menu/sMainWindow/picture/Normal_32.png"),
                                   QPixmap(":/Menu/sMainWindow/picture/Normal_blue_32.png"));
            mWorkWidget->showMaximized();
        }
    }
};

