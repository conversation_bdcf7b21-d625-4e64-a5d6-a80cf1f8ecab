﻿#ifndef PREFERENCEEDITOR_H
#define PREFERENCEEDITOR_H

#include <QButtonGroup>
#include <QWidget>
#include <QFileSystemWatcher>

#include "AbstractParamEditor.h"
#include <LibWidget/ParameterEditor.h>
#include "MSParameterParser.h"
//#include "UMAStructure/UMA_Public.h"
#include "sMethod/parmeter_structs.h" //LabSolutions

namespace Ui {
class PreferenceEditor;
}

//class PreferenceManager;

class PreferenceEditor : public QWidget {
    Q_OBJECT

public:
    explicit PreferenceEditor(QWidget *parent = nullptr/*PreferenceManager* manager = nullptr*/);
    ~PreferenceEditor();
    //void setPreferenceManager(PreferenceManager* manager);
    //friend class PreferenceManager;
    LabSolutionsRW::Event *getEventParamPtr() const;
    void setMassEditable(bool able);

//public slots:
//    void setNormalParam(const uma_project::ParamNormal& param);

private slots:
    /*! --------- UI -----------*/
    void on_pushButton_loadMethod_clicked();
    void on_pushButton_advance_clicked();
    /*! ---------- operation --------- */
    //void onAnalysisModeClicked(QAbstractButton* button);
    //void onTextEdited();

    void onMzVolParamModified();
    //void on_pushButton_getMZParam_clicked();
    //void on_pushButton_SaveMZ_clicked();

    //void on_comboBox_resolution_currentIndexChanged(int index);
    void on_voltageRangeChanged(QPair<double, double> range);
    void on_gasSpeedRangeChanged(QPair<double, double> range);
//signals:
//    void normalParamEdited(const uma_project::ParamNormal& param);

private:


    void initialize();
    void initConfig();
    void signalSlotManager();
    //void initMzVolParamStyle();
    //void updateNormalParam(const uma_project::ParamNormal& param);
    //void updateAnalysisMode(uma_project::AnalysisMode mode);
    void updateMethodFile(const QString& methodFile, bool aways = false);
    //void updateResolution(uma_project::Resolution resolution);
    QList<QDomElement>* getMethodParamEvents(const QString& methodFile);
    QDomElement getFirstMassEventElement(const QString& methodFile);
    void updateFromMethodEvent(const QDomElement& element);
    //void updatePNMode(uma_project::PN_Mode mode);
    //void updateMzPreference(double mz, const uma_project::ParamAdvanceBase& param);
    //uma_project::ParamAdvanceBase getAdvanceBaseInputParam();
    void updateNormalParamFromLabEvent(LabSolutionsRW::Event* p_evt);
    //bool methodTypeCheck(LabSolutionsRW::Event* pEvt);

private:
    Ui::PreferenceEditor* ui;
    //PreferenceManager* m_prfsManager;
    QButtonGroup* m_analysisModeGroup;
    AbstractParamEditor* m_ParameterWidgetPtr;
    ParameterEditor* m_pValatageEditor[12]; ///< 12 U
    ParameterEditor* m_pRfEditor[3]; ///< 3 RF
    //uma_project::ParamNormal m_param;
    //! LabSoulution
    MSParameterParser m_MSParamParser;
    LabSolutionsRW::Event* m_eventParamPtr;
    QFileSystemWatcher m_fileWatcher;
};

#endif // PREFERENCEEDITOR_H
