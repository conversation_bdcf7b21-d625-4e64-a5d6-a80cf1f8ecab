#pragma once
#include <QtCore/qglobal.h>

#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif

#include <QMutex>
#include <QVector>
#include <cParamCCS.h>
#include "cMidWareUSB.h"

namespace libControlCCS{
/**
 * @brief The CommandUSB class
 * CCS,ASG,DAQ指令的具体实现，内含DAQ采集线程，getDataDAQ为数据提取接口
 */
#define SIZE_BLOCK_BUFF 3
class CONTROLCCS_LIBRARY_EXPORT CommunicationCMD: public QObject
{
    Q_OBJECT
public:
    struct _DAQ_DATA{
        volatile uint nVersion= 0;
        QMutex qLock;
        QVector<uint> pBuff;
    }p_DAQ_DATA[SIZE_BLOCK_BUFF];
    CommunicationCMD(QObject *parent = nullptr);
    virtual ~CommunicationCMD(void);
    bool isThreadRun(){
        return mThreadDAQ.isRunning();
    }
    QList<_DAQ_DATA*>* getDataDAQ(){
        return &m_DAQ_DATA;
    }
    int safeCCS(double* pDAC= nullptr);
    int safeCCS(QVector<quint16>& pDAC);
    int updateCCS(QByteArray& pArrayCCS);
    int updateCCS(char* pData, uint nLength);
    int scanCCS(ParamCCS::CCS_SCAN& pCCS_SCAN);
    int scanCCS(QByteArray& pArrayCCS);
    int scanCCS(char* pData, uint nLength);
    int stopCCS();

    int updateASG(QByteArray& pArrayASG);
    int updateASG(char* pData, uint nLength);
    int scanASG();
    int scanASG(QByteArray& pArrayASG);
    int scanASG(char* pData, uint nLength);
    int stopASG();

    virtual int startDAQ(ParamCCS::_DAQ_CONFIG& p_DAQ_CONFIG);
    virtual int stopDAQ();

    int startPWM(ParamCCS::PWM_GENETATE& pPWM_GENETATE);
    int sendCMD(const QByteArray& pArrayCMD, QByteArray& pArrayBack, uint sizeBack= 512);
    int sendCMD(const char* pW, const uint nW, char* const pR, const uint nR);

private:
    //SThread mThreadDAQ; /**< TODO */
    //ParamCCS::_DAQ_CONFIG m_DAQ_CONFIG;
    //MidWareUSB mComm;
    //int mCurrentBuff=0;
    //volatile bool mSentDaqStop= false;
    //QList<_DAQ_DATA*> m_DAQ_DATA;
    static int daqThread(void *pParam, const bool &bRunning);
    bool init_DAQ_DATA(int nSize=0);
    inline bool daqGetData(QVector<uint>& recvBuffer,uint* pLastPkt,QVector<uint>& frameBuff,
                    uint& uFrameCount,uint& uPacketCount,bool& isSameFrame,bool& isRightPacket,
                    int uValidSize,int uLastPktSize);

protected:
    SThread mThreadDAQ;
    ParamCCS::_DAQ_CONFIG m_DAQ_CONFIG;
    MidWareUSB mComm;
    int mCurrentBuff=0;
    volatile bool mSentDaqStop= false;
    QList<_DAQ_DATA*> m_DAQ_DATA;
};
}
