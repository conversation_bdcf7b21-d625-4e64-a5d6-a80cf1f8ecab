/********************************************************************************
** Form generated from reading UI file 'uiCalibrationMass.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UICALIBRATIONMASS_H
#define UI_UICALIBRATIONMASS_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_uiCalibrationMass
{
public:
    QVBoxLayout *verticalLayout_3;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *UI_PB_LOAD_CAL;
    QPushButton *UI_PB_APPLY_CAL;
    QPushButton *UI_PB_SAVE_CAL;
    QPushButton *UI_PB_SAVETO_CAL;
    QSpacerItem *horizontalSpacer;
    QCheckBox *UI_CB_ENABLE_CAL;
    QSpacerItem *horizontalSpacer_2;
    QTabWidget *UI_TABWIDGET_PARAM_SINGLEACQ;
    QWidget *UI_W_Q1ACQ_CAL;
    QVBoxLayout *UI_LAYOUT_Q1ACQ_CAL;
    QWidget *UI_W_Q3ACQ_CAL;
    QVBoxLayout *UI_LAYOUT_Q3ACQ_CAL;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *UI_PB_SCAN_CAL;
    QPushButton *UI_PB_STOP_CAL;

    void setupUi(QWidget *uiCalibrationMass)
    {
        if (uiCalibrationMass->objectName().isEmpty())
            uiCalibrationMass->setObjectName(QString::fromUtf8("uiCalibrationMass"));
        uiCalibrationMass->resize(863, 576);
        verticalLayout_3 = new QVBoxLayout(uiCalibrationMass);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        UI_PB_LOAD_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_LOAD_CAL->setObjectName(QString::fromUtf8("UI_PB_LOAD_CAL"));

        horizontalLayout_3->addWidget(UI_PB_LOAD_CAL);

        UI_PB_APPLY_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_APPLY_CAL->setObjectName(QString::fromUtf8("UI_PB_APPLY_CAL"));

        horizontalLayout_3->addWidget(UI_PB_APPLY_CAL);

        UI_PB_SAVE_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_SAVE_CAL->setObjectName(QString::fromUtf8("UI_PB_SAVE_CAL"));

        horizontalLayout_3->addWidget(UI_PB_SAVE_CAL);

        UI_PB_SAVETO_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_SAVETO_CAL->setObjectName(QString::fromUtf8("UI_PB_SAVETO_CAL"));

        horizontalLayout_3->addWidget(UI_PB_SAVETO_CAL);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer);

        UI_CB_ENABLE_CAL = new QCheckBox(uiCalibrationMass);
        UI_CB_ENABLE_CAL->setObjectName(QString::fromUtf8("UI_CB_ENABLE_CAL"));

        horizontalLayout_3->addWidget(UI_CB_ENABLE_CAL);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_2);


        verticalLayout_3->addLayout(horizontalLayout_3);

        UI_TABWIDGET_PARAM_SINGLEACQ = new QTabWidget(uiCalibrationMass);
        UI_TABWIDGET_PARAM_SINGLEACQ->setObjectName(QString::fromUtf8("UI_TABWIDGET_PARAM_SINGLEACQ"));
        UI_TABWIDGET_PARAM_SINGLEACQ->setMaximumSize(QSize(16777215, 16777215));
        UI_W_Q1ACQ_CAL = new QWidget();
        UI_W_Q1ACQ_CAL->setObjectName(QString::fromUtf8("UI_W_Q1ACQ_CAL"));
        UI_LAYOUT_Q1ACQ_CAL = new QVBoxLayout(UI_W_Q1ACQ_CAL);
        UI_LAYOUT_Q1ACQ_CAL->setObjectName(QString::fromUtf8("UI_LAYOUT_Q1ACQ_CAL"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q1ACQ_CAL, QString());
        UI_W_Q3ACQ_CAL = new QWidget();
        UI_W_Q3ACQ_CAL->setObjectName(QString::fromUtf8("UI_W_Q3ACQ_CAL"));
        UI_LAYOUT_Q3ACQ_CAL = new QVBoxLayout(UI_W_Q3ACQ_CAL);
        UI_LAYOUT_Q3ACQ_CAL->setObjectName(QString::fromUtf8("UI_LAYOUT_Q3ACQ_CAL"));
        UI_TABWIDGET_PARAM_SINGLEACQ->addTab(UI_W_Q3ACQ_CAL, QString());

        verticalLayout_3->addWidget(UI_TABWIDGET_PARAM_SINGLEACQ);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalSpacer_3 = new QSpacerItem(716, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);

        UI_PB_SCAN_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_SCAN_CAL->setObjectName(QString::fromUtf8("UI_PB_SCAN_CAL"));

        horizontalLayout->addWidget(UI_PB_SCAN_CAL);

        UI_PB_STOP_CAL = new QPushButton(uiCalibrationMass);
        UI_PB_STOP_CAL->setObjectName(QString::fromUtf8("UI_PB_STOP_CAL"));

        horizontalLayout->addWidget(UI_PB_STOP_CAL);


        verticalLayout_3->addLayout(horizontalLayout);

        QWidget::setTabOrder(UI_PB_SCAN_CAL, UI_PB_STOP_CAL);
        QWidget::setTabOrder(UI_PB_STOP_CAL, UI_CB_ENABLE_CAL);
        QWidget::setTabOrder(UI_CB_ENABLE_CAL, UI_PB_LOAD_CAL);
        QWidget::setTabOrder(UI_PB_LOAD_CAL, UI_PB_SAVE_CAL);
        QWidget::setTabOrder(UI_PB_SAVE_CAL, UI_PB_SAVETO_CAL);
        QWidget::setTabOrder(UI_PB_SAVETO_CAL, UI_TABWIDGET_PARAM_SINGLEACQ);
        QWidget::setTabOrder(UI_TABWIDGET_PARAM_SINGLEACQ, UI_PB_APPLY_CAL);

        retranslateUi(uiCalibrationMass);

        UI_TABWIDGET_PARAM_SINGLEACQ->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(uiCalibrationMass);
    } // setupUi

    void retranslateUi(QWidget *uiCalibrationMass)
    {
        uiCalibrationMass->setWindowTitle(QApplication::translate("uiCalibrationMass", "Form", nullptr));
        UI_PB_LOAD_CAL->setText(QApplication::translate("uiCalibrationMass", "Load", nullptr));
        UI_PB_APPLY_CAL->setText(QApplication::translate("uiCalibrationMass", "Apply", nullptr));
        UI_PB_SAVE_CAL->setText(QApplication::translate("uiCalibrationMass", "Save", nullptr));
        UI_PB_SAVETO_CAL->setText(QApplication::translate("uiCalibrationMass", "Save To", nullptr));
        UI_CB_ENABLE_CAL->setText(QApplication::translate("uiCalibrationMass", "Enable", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q1ACQ_CAL), QApplication::translate("uiCalibrationMass", "Q1", nullptr));
        UI_TABWIDGET_PARAM_SINGLEACQ->setTabText(UI_TABWIDGET_PARAM_SINGLEACQ->indexOf(UI_W_Q3ACQ_CAL), QApplication::translate("uiCalibrationMass", "Q3", nullptr));
        UI_PB_SCAN_CAL->setText(QApplication::translate("uiCalibrationMass", "Scan", nullptr));
        UI_PB_STOP_CAL->setText(QApplication::translate("uiCalibrationMass", "Stop", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiCalibrationMass: public Ui_uiCalibrationMass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UICALIBRATIONMASS_H
