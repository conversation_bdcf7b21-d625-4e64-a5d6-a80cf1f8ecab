#pragma once

#include "ui_uiSoftwareUpdates.h"
#include <QWidget>
#include <qUiWidget.h>

namespace Ui {
class uiSoftwareUpdates;
}

class uiSoftwareUpdates : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiSoftwareUpdates(QWidget *parent = nullptr);
    ~uiSoftwareUpdates();
    void initClass(QString& filePath);

protected:
    bool initUI(QString& filePath);
    Ui::uiSoftwareUpdates ui;
};

