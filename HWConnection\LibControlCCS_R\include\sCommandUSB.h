/**
 * @brief The CommandUSB class
 * CCS,ASG,DAQ指令的封装，内含DAQ数据处理线程，getDataDAQ为数据提取接口
 */

#pragma once
#include "cCommunicationCMD.h"
#include <QFile>
#include <QTextStream>


class CONTROLCCS_LIBRARY_EXPORT CommandUSB: public QObject
{
    Q_OBJECT
public:
    struct SFileHead{
        quint32 vid = 0; /**< TODO */
        quint32 pid = 0; /**< TODO */
        quint32 paramSize = 0; /**< TODO */
    };
    struct SDaqFileHead{
        quint32 accumulate; /**< TODO */
        quint32 framePoint; /**< TODO */
        double frequency; /**< TODO */
        double dateTime; /**< TODO */
        double massStart; /**< TODO */
        double massEnd; /**< TODO */
    };
    struct _DAQ_DATA{
        QMutex locker;
        volatile quint32 nVersion= 0;
        QByteArray pBuff;
    };
    CommandUSB(QObject *parent = nullptr);
    ~CommandUSB();
    void clearBuff();
    inline int setDataDAQ(const QVector<uint>& pSrcData, const int nDaqShiftAcc,
                          _StreamBody::Type_Data typeData, QByteArray& pStream);
    int getDataDAQ(QByteArray& srcData, bool always = false);
    void zeroVersion();

private:
    //SThread mProcessThread;
    //libControlCCS::CommunicationCMD* mCommunicationCMD;
    //virtual void createCommunicationHandle();
    quint32 mVersionGet=1;
    _DAQ_DATA mSampledData;
    //libControlCCS::ParamCCS::_DAQ_CONFIG m_DAQ_CONFIG;
    static int processDataThread(void *pParam, const bool &bRunning);
    //QByteArray mDaqData;
    _StreamBody::Type_Data mTypeData;
template<typename T>
    double fillData(const QVector<uint>& pSrcData, const int nDaqShiftAcc, const uint offset);
protected:
    SThread mProcessThread;
    libControlCCS::CommunicationCMD* mCommunicationCMD;
    libControlCCS::ParamCCS::_DAQ_CONFIG m_DAQ_CONFIG;

    virtual void createCommunicationHandle();
public:
    bool isRunningDAQ();
    int safeCCS(double* pDAC= nullptr);
    int safeCCS(QVector<quint16>& pDAC);
    int updateCCS(char* pData, uint nLength);                   //下发stopCCS，下发扫描方法
    int scanCCS(libControlCCS::ParamCCS::CCS_SCAN& pCCS_SCAN);  //根据参数开始扫描
    int scanCCS(char* pData, quint32 nLength);                  //下发stopCCS，下发pData扫描方法，下发默认开始扫描
    int scanCCS(QByteArray pArrayCCS);                          //下发stopCCS，下发pArrayCCS扫描方法，下发默认开始扫描
    int stopCCS();                                              //下发stopCCS
    int scanASG(char* pData, quint32 nLength);
    int scanASG(QByteArray pArrayASG);
    int stopASG();
    int startDAQ(libControlCCS::ParamCCS::_DAQ_CONFIG& p_DAQ_CONFIG, _StreamBody::Type_Data typeData);
    int stopDAQ();
    int startPWM(libControlCCS::ParamCCS::PWM_GENETATE& pPWM_GENETATE);
    int sendCMD(const char* pW, const quint32 nW, char* const pR, const quint32 nR);
    int sendCMD(const QByteArray& pArrayCMD, QByteArray& pArrayBack, quint32 sizeBack= 512);
    _StreamBody::Type_Data currentTypeData();
};

class sCommandUSB{
public:
    static CommandUSB* getCommandUSB(){
        static sCommandUSB insmCommandUSB;
        return &(insmCommandUSB.mCommandUSB);
    }
private:
    CommandUSB mCommandUSB;
    sCommandUSB(){}
    virtual ~sCommandUSB(){}
    sCommandUSB(const sCommandUSB&){}
    sCommandUSB& operator=(const sCommandUSB&){
        static sCommandUSB insCommandUSB;
        return insCommandUSB;
    }
};
