#pragma once

#include <QtCore/qglobal.h>
#include "cDataFit.h"

#if defined(MATRIX_LIBRARY)
#  define MATRIX_EXPORT Q_DECL_EXPORT
#else
#  define MATRIX_EXPORT Q_DECL_IMPORT
#endif

class MATRIX_EXPORT cMatrix
{
public:
    cMatrix();
    ~cMatrix();
    static int polyfit(const double *x
                       ,const double *y
                       ,size_t xyLength
                       ,unsigned poly_n
                       ,std::vector<double>& out_factor
                       ,double& out_chisq);//拟合曲线与数据点的优值函数最小值 ,χ2 检验
    static int polyfit(const std::vector<double>& x
                       ,const std::vector<double>& y
                       ,unsigned poly_n
                       ,std::vector<double>& out_factor
                       ,double& out_chisq);//拟合曲线与数据点的优值函数最小值 ,χ2 检验
    static bool linearFit(const std::vector<double>& x,
                   const std::vector<double>& y,
                   double& slope, double& intercept, double& squareR);
    static bool linearFit(const double *x,
                          const double *y,
                          size_t xyLength,
                   double& slope, double& intercept, double& squareR);
};

