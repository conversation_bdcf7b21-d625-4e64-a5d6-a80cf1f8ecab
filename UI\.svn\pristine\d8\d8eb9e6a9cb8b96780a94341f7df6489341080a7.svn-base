/********************************************************************************
** Form generated from reading UI file 'uiMainWindow.ui'
**
** Created by: Qt User Interface Compiler version 5.12.5
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UIMAINWINDOW_H
#define UI_UIMAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "LibWidget/sMyButton.h"

QT_BEGIN_NAMESPACE

class Ui_uiMainWindow
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *UI_W_MENUBAR_MAINW;
    QHBoxLayout *UI_LAYOUT_MENUBAR_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_MAIN_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_SYSTEM_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_LIST_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_CREATE_LIST_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_MASS_METHOD_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_LIQUID_METHOD_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_DATA_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_TUNE_MAINW;
    MyWidget::sMyButton *UI_MPB_MENU_SINAL_SCAN_MAINW;
    QSpacerItem *horizontalSpacer_5;
    MyWidget::sMyButton *UI_MPB_MENU_STATE_MAINW;
    QWidget *UI_W_CONTAINER_WINDOW_MAINW;
    QVBoxLayout *UI_LAYOUT_CONTAINER_WINDOW_MAINW;
    QWidget *UI_W_MAINWIDOW;
    QHBoxLayout *horizontalLayout;
    QWidget *UI_W_MENU_MAINW;
    QGridLayout *UI_LAYOUT_MENU_MAINW;
    MyWidget::sMyButton *UI_MPB_LIQUID_METHOD_MAINW;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_3;
    MyWidget::sMyButton *UI_MPB_SIGNAL_SCAN_MAINW;
    MyWidget::sMyButton *UI_MPB_LIST_MAINW;
    QSpacerItem *verticalSpacer_2;
    MyWidget::sMyButton *UI_MPB_DATA_MAINW;
    QSpacerItem *verticalSpacer;
    MyWidget::sMyButton *UI_MPB_TUNE_MAINW;
    MyWidget::sMyButton *UI_MPB_SYSTEM_MAINW;
    MyWidget::sMyButton *UI_MPB_CREATE_LIST_MAINW;
    MyWidget::sMyButton *UI_MPB_MASS_METHOD_MAINW;
    QSpacerItem *verticalSpacer_4;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer_5;
    QSpacerItem *horizontalSpacer_3;
    QSpacerItem *horizontalSpacer;
    QWidget *UI_W_STATEBAR_MAINW;
    QGridLayout *gridLayout;
    QPushButton *pushButton_3;

    void setupUi(QWidget *uiMainWindow)
    {
        if (uiMainWindow->objectName().isEmpty())
            uiMainWindow->setObjectName(QString::fromUtf8("uiMainWindow"));
        uiMainWindow->resize(800, 600);
        verticalLayout = new QVBoxLayout(uiMainWindow);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        UI_W_MENUBAR_MAINW = new QWidget(uiMainWindow);
        UI_W_MENUBAR_MAINW->setObjectName(QString::fromUtf8("UI_W_MENUBAR_MAINW"));
        UI_LAYOUT_MENUBAR_MAINW = new QHBoxLayout(UI_W_MENUBAR_MAINW);
        UI_LAYOUT_MENUBAR_MAINW->setObjectName(QString::fromUtf8("UI_LAYOUT_MENUBAR_MAINW"));
        UI_MPB_MENU_MAIN_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_MAIN_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_MAIN_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_MAIN_MAINW);

        UI_MPB_MENU_SYSTEM_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_SYSTEM_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_SYSTEM_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_SYSTEM_MAINW);

        UI_MPB_MENU_LIST_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_LIST_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_LIST_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_LIST_MAINW);

        UI_MPB_MENU_CREATE_LIST_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_CREATE_LIST_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_CREATE_LIST_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_CREATE_LIST_MAINW);

        UI_MPB_MENU_MASS_METHOD_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_MASS_METHOD_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_MASS_METHOD_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_MASS_METHOD_MAINW);

        UI_MPB_MENU_LIQUID_METHOD_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_LIQUID_METHOD_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_LIQUID_METHOD_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_LIQUID_METHOD_MAINW);

        UI_MPB_MENU_DATA_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_DATA_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_DATA_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_DATA_MAINW);

        UI_MPB_MENU_TUNE_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_TUNE_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_TUNE_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_TUNE_MAINW);

        UI_MPB_MENU_SINAL_SCAN_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_SINAL_SCAN_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_SINAL_SCAN_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_SINAL_SCAN_MAINW);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        UI_LAYOUT_MENUBAR_MAINW->addItem(horizontalSpacer_5);

        UI_MPB_MENU_STATE_MAINW = new MyWidget::sMyButton(UI_W_MENUBAR_MAINW);
        UI_MPB_MENU_STATE_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MENU_STATE_MAINW"));

        UI_LAYOUT_MENUBAR_MAINW->addWidget(UI_MPB_MENU_STATE_MAINW);


        verticalLayout->addWidget(UI_W_MENUBAR_MAINW);

        UI_W_CONTAINER_WINDOW_MAINW = new QWidget(uiMainWindow);
        UI_W_CONTAINER_WINDOW_MAINW->setObjectName(QString::fromUtf8("UI_W_CONTAINER_WINDOW_MAINW"));
        UI_LAYOUT_CONTAINER_WINDOW_MAINW = new QVBoxLayout(UI_W_CONTAINER_WINDOW_MAINW);
        UI_LAYOUT_CONTAINER_WINDOW_MAINW->setSpacing(0);
        UI_LAYOUT_CONTAINER_WINDOW_MAINW->setObjectName(QString::fromUtf8("UI_LAYOUT_CONTAINER_WINDOW_MAINW"));
        UI_LAYOUT_CONTAINER_WINDOW_MAINW->setContentsMargins(0, 0, 0, 0);
        UI_W_MAINWIDOW = new QWidget(UI_W_CONTAINER_WINDOW_MAINW);
        UI_W_MAINWIDOW->setObjectName(QString::fromUtf8("UI_W_MAINWIDOW"));
        horizontalLayout = new QHBoxLayout(UI_W_MAINWIDOW);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        UI_W_MENU_MAINW = new QWidget(UI_W_MAINWIDOW);
        UI_W_MENU_MAINW->setObjectName(QString::fromUtf8("UI_W_MENU_MAINW"));
        UI_LAYOUT_MENU_MAINW = new QGridLayout(UI_W_MENU_MAINW);
        UI_LAYOUT_MENU_MAINW->setObjectName(QString::fromUtf8("UI_LAYOUT_MENU_MAINW"));
        UI_MPB_LIQUID_METHOD_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_LIQUID_METHOD_MAINW->setObjectName(QString::fromUtf8("UI_MPB_LIQUID_METHOD_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_LIQUID_METHOD_MAINW, 3, 3, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        UI_LAYOUT_MENU_MAINW->addItem(horizontalSpacer_4, 1, 2, 1, 1);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_MENU_MAINW->addItem(verticalSpacer_3, 2, 1, 1, 1);

        UI_MPB_SIGNAL_SCAN_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_SIGNAL_SCAN_MAINW->setObjectName(QString::fromUtf8("UI_MPB_SIGNAL_SCAN_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_SIGNAL_SCAN_MAINW, 7, 3, 1, 1);

        UI_MPB_LIST_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_LIST_MAINW->setObjectName(QString::fromUtf8("UI_MPB_LIST_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_LIST_MAINW, 1, 1, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_MENU_MAINW->addItem(verticalSpacer_2, 8, 1, 1, 1);

        UI_MPB_DATA_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_DATA_MAINW->setObjectName(QString::fromUtf8("UI_MPB_DATA_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_DATA_MAINW, 5, 1, 1, 1);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_MENU_MAINW->addItem(verticalSpacer, 0, 1, 1, 1);

        UI_MPB_TUNE_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_TUNE_MAINW->setObjectName(QString::fromUtf8("UI_MPB_TUNE_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_TUNE_MAINW, 5, 3, 1, 1);

        UI_MPB_SYSTEM_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_SYSTEM_MAINW->setObjectName(QString::fromUtf8("UI_MPB_SYSTEM_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_SYSTEM_MAINW, 7, 1, 1, 1);

        UI_MPB_CREATE_LIST_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_CREATE_LIST_MAINW->setObjectName(QString::fromUtf8("UI_MPB_CREATE_LIST_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_CREATE_LIST_MAINW, 1, 3, 1, 1);

        UI_MPB_MASS_METHOD_MAINW = new MyWidget::sMyButton(UI_W_MENU_MAINW);
        UI_MPB_MASS_METHOD_MAINW->setObjectName(QString::fromUtf8("UI_MPB_MASS_METHOD_MAINW"));

        UI_LAYOUT_MENU_MAINW->addWidget(UI_MPB_MASS_METHOD_MAINW, 3, 1, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_MENU_MAINW->addItem(verticalSpacer_4, 4, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        UI_LAYOUT_MENU_MAINW->addItem(horizontalSpacer_2, 0, 0, 1, 1);

        verticalSpacer_5 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        UI_LAYOUT_MENU_MAINW->addItem(verticalSpacer_5, 6, 1, 1, 1);


        horizontalLayout->addWidget(UI_W_MENU_MAINW);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);

        horizontalSpacer = new QSpacerItem(385, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        UI_LAYOUT_CONTAINER_WINDOW_MAINW->addWidget(UI_W_MAINWIDOW);


        verticalLayout->addWidget(UI_W_CONTAINER_WINDOW_MAINW);

        UI_W_STATEBAR_MAINW = new QWidget(uiMainWindow);
        UI_W_STATEBAR_MAINW->setObjectName(QString::fromUtf8("UI_W_STATEBAR_MAINW"));
        gridLayout = new QGridLayout(UI_W_STATEBAR_MAINW);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(-1, 6, -1, 6);
        pushButton_3 = new QPushButton(UI_W_STATEBAR_MAINW);
        pushButton_3->setObjectName(QString::fromUtf8("pushButton_3"));

        gridLayout->addWidget(pushButton_3, 0, 0, 1, 1);


        verticalLayout->addWidget(UI_W_STATEBAR_MAINW);


        retranslateUi(uiMainWindow);

        QMetaObject::connectSlotsByName(uiMainWindow);
    } // setupUi

    void retranslateUi(QWidget *uiMainWindow)
    {
        uiMainWindow->setWindowTitle(QApplication::translate("uiMainWindow", "sMainWindow", nullptr));
        pushButton_3->setText(QApplication::translate("uiMainWindow", "PushButton", nullptr));
    } // retranslateUi

};

namespace Ui {
    class uiMainWindow: public Ui_uiMainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UIMAINWINDOW_H
