#include "SValidator.h"


SDoubleValidator::SDoubleValidator(double bottom, double top, int decimals, QObject *parent)
    : QDoubleValidator(bottom, top, decimals, parent)
{

}

QValidator::State SDoubleValidator::validate(QString &input, int &pos) const
{
    const State originalRes = QDoubleValidator::validate(input,pos);
    if(originalRes==Intermediate){
        const auto extracted = locale().toDouble(input);
        if(extracted>0){
            if(extracted>top() && -extracted<bottom())
                return Invalid;
        }
        else if(extracted<bottom())
            return Invalid;
    }
    return  originalRes;
}
