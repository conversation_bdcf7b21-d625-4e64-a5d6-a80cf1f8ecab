#pragma once

#include <QScriptEngine>
#include <QWidget>
#include <xlsxdocument.h>
#include <QDateTime>
#include <QList>
#include <QMap>
#include <qUiWidget.h>
#include <uiBatch/uiPlateLayout.h>
#include "ui_uiBatch.h"

class uiBatch : public qUiWidget
{
    Q_OBJECT

public:
    explicit uiBatch(QScriptEngine* pScriptEngine,
                    QWidget *parent = nullptr);
    ~uiBatch();
    virtual void initClass(QString& filePath);

    void onTimer(){
        ui.UI_L_DATE_BATCH->setText(QDate::currentDate().toString("yyyyMMdd"));
    }
    QString getDate(){
        return ui.UI_L_DATE_BATCH->text();
    }
    bool getBatch(QList<QStringList>& pList);
    QList<QMap<QString, QString>>& getBatch();
    uiPlateLayout* mPlateLayout= nullptr;

protected:
    Ui::uiBatch ui;

    QList<QMap<QString, QString>> mParamBatch;

    bool initUI(QString& filePath);
    virtual void createToolBar();
    //QXlsx::Document* xlsx;

private slots:
    void onUI_MB_NEW_BATCH();
    void onUI_MB_OPEN_BATCH();
    void onUI_MB_SAVE_BATCH();
    void onUI_MB_SAVEAS_BATCH();
    void onUI_MB_PLATE_LAYOUT_BATCH();
    void onUI_MB_SUBMIT_BATCH();

signals:
    void sSubmit();
};

