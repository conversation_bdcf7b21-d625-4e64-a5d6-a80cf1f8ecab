#include "uiMainWindow.h"
#include <QDir>
#include <QMessageBox>
#include <QSettings>

uiMainWindow::uiMainWindow(QWidget *parent)
    : qUiWidget(parent)
{
    QDir::setCurrent(QApplication::applicationDirPath());
    //QCoreApplication::setOrganizationName("");
    //QCoreApplication::setApplicationName("");
    QSettings::setDefaultFormat(QSettings::NativeFormat);
    //qInstallMessageHandler(msgHandler);
    setWindowFlags(Qt::CustomizeWindowHint);//Qt::FramelessWindowHint



    GlobalConfigParam::getConfigParam()->setFilePathSysIni(QCoreApplication::applicationDirPath()+"/DebugParam.ini");

    //loadConfig(&mCONGIG_MAINWINDOW);
    qInstallMessageHandler(msgHandler);

    //initClass();

    mScriptEngine.globalObject().setProperty("MainWindow", mScriptEngine.newQObject(this));

}

void uiMainWindow::initClass(QString& filePath)
{
    mState= new uiState();
    mState->initClass(filePath);
    mSystem= new uiSystem(&mScriptEngine, this);
    mSystem->initClass(filePath);
    mManualMode= new uiManualMode(&mScriptEngine, this);
    mManualMode->initClass(filePath);
    mBatch= new uiBatch(&mScriptEngine, this);
    mBatch->initClass(filePath);
    mQueue= new uiQueue(&mScriptEngine, this);
    mQueue->initClass(filePath);
    mTune= new uiTune(&mScriptEngine, this);
    mTune->initClass(filePath);
    mSingleAcquisition= new uiSingleAcquisition(&mScriptEngine, mTune, mSystem, this);
    mSingleAcquisition->initClass(filePath);

    initUI(filePath);
}

bool uiMainWindow::initUI(QString& filePath)
{
    ui.setupUi(this);
    mInitMainWindow=new uiInitMainWindow(this);
    mInitMainWindow->initUI();

    createToolBar();
    setCurrentWindow(ui.UI_W_MAINWIDOW);
    return true;
}

void uiMainWindow::createToolBar()
{

}

uiMainWindow::~uiMainWindow()
{
}

void uiMainWindow::setCurrentWindow(QWidget* pWidget)
{
    ui.UI_W_MAINWIDOW->hide();
    mSystem->hide();
    mManualMode->hide();
    mBatch->hide();
    mQueue->hide();
    mTune->hide();
    mSingleAcquisition->hide();
    pWidget->show();
    if(pWidget == mSystem){
        //mfriendMainWindow->showToolButton(true);
    }
}

void uiMainWindow::closeEvent ( QCloseEvent * e )
{
    if( QMessageBox::question(this, tr("Quit"), tr("Are you sure to quit this application?"),
                              QMessageBox::Yes, QMessageBox::No ) == QMessageBox::Yes){
        //stopAcquisiton();
        //onEI_Off();
        //onPumpOff();
        //oneKeyStop(false);
        e->accept();
    }else
        e->ignore();
}

void uiMainWindow::onSubmit()
{
    if(!mQueue)
        return;
    setCurrentWindow(mQueue);
    mQueue->onSubmit(mBatch->getBatch(), mBatch->getDate());
}
