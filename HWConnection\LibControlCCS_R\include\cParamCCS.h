#pragma once

#include <QtCore/qglobal.h>
#if defined(CONTROLCCS_LIBRARY)
#  define CONTROLCCS_LIBRARY_EXPORT Q_DECL_EXPORT
#else
#  define CONTROLCCS_LIBRARY_EXPORT Q_DECL_IMPORT
#endif

#include <QList>
#include <QMutex>
#include <QVector>
#include <vector>

namespace libControlCCS{
typedef unsigned int quint32;
/**
 * @brief ParamCCS
 * CCS,ASG,DAQ指令参数及数据生成
 */
class CONTROLCCS_LIBRARY_EXPORT ParamCCS
{
public:
    enum CCS_CMD_ID{
        STATE_REQ_ID			= 0xFFFFF001,
        SOFT_RST_ID				= 0xFFFFF002,
        VERSION_REQ_ID			= 0xFFFFF003,
        MS_METHOD_ID			=0xFFFFF065,
        MS_UPDATE_ID			=0xFFFFF066,
        MS_START_ID				=0xFFFFF067,
        MS_STOP_ID				=0xFFFFF068,
        BUFFER_GAS_CTRL_ID  = 0xFFFFF069,
        //BUFFER_GAS_EXT_ID	  = 0xFFFFF06A,
        PWM_GENERATE_ID	  = 0xFFFFF06B,
        PUMP_VENT_CTRL_ID   = 0xFFFFF0C9,
        GHV_ENABLE_CTRL_ID  = 0xFFFFF12D,
        TEMPERATURE_CTRL_ID = 0xFFFFF12E,
        TEMPERATURE_PID_ID  = 0xFFFFF130,
        HEATING_SWITCH_ID   = 0xFFFFF131,
        SAMPLE_GAS_CTRL_ID  = 0xFFFFF132,
        DAQ_COMMAND_ID			= 0xFFFFF201,
        ASG_COMMAND_ID			= 0xFFFFF301
    };
    enum DAQ_SUBCMD_ID{
        DAQ_CONFIG_ID			= 0x00000001,
        DAQ_START_ID			= 0x00000002,
        DAQ_DATAREQ_ID			= 0x00000004,
        DAQ_STOP_ID				= 0x00000008,
        ASG_POWERDOWN_ID		= 0x00000010
    };
    enum ASG_SUBCMD_ID{
        ASG_CONFIG_ID			= 0x00000001,
        ASG_SET_ID				= 0x00000002,
        ASG_UPDATE_ID			= 0x00000004,
        ASG_START_ID			= 0x00000008,
        ASG_HALT_ID				= 0x00000010,
        ASG_ADJUST_ID			= 0x00000020
    };
    struct _TEMPERATURE_PID{
        quint32 p_PID;
        quint32 i_PID;
        quint32 d_PID;
    };
    struct _DAQ_CONFIG{
        quint32 No_AQP;
        quint32 Frq_AQ;
        quint32 No_ACC;
        quint32 Mode;
    };
    struct CCS_SCAN{
        quint32 triggerEXT=0,
        scanTimes=0;
    };
    struct DAC_STRUCT{
        unsigned int
            DAQ_Trigger;
        double Frequency,
            Value_DAC[12],
            HoldTime;//ms
    };
    struct PARAM_STRUCT{
        double Gain_DAC[12],
            Offset_DAC[12];
        std::vector<DAC_STRUCT> pDACStruct;
    };
    struct PWM_GENETATE{
        unsigned long lPWMCycle[8];
        unsigned long lPWMUp[8];
        unsigned long lPWMDown[8];
        unsigned long lPWMEnable=0;
        void initPWM_GENETATE(){
            lPWMEnable=0;
            for(int i=0; i<8;++i){
                lPWMCycle[i]=0;
                lPWMUp[i]=0;
                lPWMDown[i]=0;
            }
        }
    };
    struct FREQUENCY{
        double Freq_MHz[6];
        unsigned long Delay_ns[6];
        double Freq_Duty[6];
        unsigned long Freq_Enable[6];
        unsigned long PWM_CTRL[6];
    };
    struct DMSIT_DAC_STRUCT{
        double	HoldTime;
        unsigned long	MaskDAC[8];
        double DacValue[160];
        FREQUENCY Freq;
    };
    struct _ASG_STRUCT{
        quint32	MethodValue;
        std::vector<DMSIT_DAC_STRUCT> pStructDAC;
        double DacOffset[160],
            DacGain[160];
    };
//    struct SDACConfigPara{
//        unsigned long	Offset0;
//        unsigned long	Offset1;
//    };
    struct SDACAdjustPara{
        float	ParaA[160];
        float	ParaB[160];
    };

    struct _STATE_REQUEST_CCS{
        double adcValue[4]={0,0,0,0};
    };

    ParamCCS(){}
    static void printfMethod(const QByteArray &buffer, bool ifAppend, QString fileName= "./MSMethod.txt");
    static void printfMethod(QVector<quint32>& buffer, bool ifAppend, QString fileName= "./command.txt");
    static void Checksum(QVector<quint32>& pBuffer);
    static void Checksum(QByteArray& pBuffer);
    static quint32 checkSum(quint32* data, size_t size);
    static bool isChecked(QVector<quint32>& pBuffer);

    static int bufferGasControlG(QVector<quint32>& pBuffer,bool bufferGasControl);
    static int pumpVentSystemG(QVector<quint32>& pBuffer, bool openPump);// 生成系统真空控制命令数据
    static int enableHV(QVector<quint32>& pBuffer, bool openGHV);// 生成全局高压系统及离子源使能命令数据
    static int tempControl(QVector<quint32>& pBuffer, quint32 temperature);// 生成离子源温度控制命令数据
    static int tempPID(QVector<quint32>& pBuffer, _TEMPERATURE_PID& p_TEMPERATURE_PID);// 生成离子源温度控制PID设置命令数据
    static int heatingSwitch(QVector<quint32>& pBuffer, quint32 heatingSwitch);// 生成离子源加热开关命令数据
    static int gasControl(QVector<quint32>& pBuffer, quint32 sampleGas);// 生成样品气体流速控制命令数据
    static int restart(QVector<quint32>& pBuffer);
    static int sysSoftReset(QVector<quint32>& pBuffer);
    static int sysVersionRequest(QVector<quint32>& pBuffer);
    static int generaterPWM(QVector<quint32>& pBuffer,PWM_GENETATE& pPWM_GENETATE);
    static QByteArray stateRequest();
    static int stateRequest(QVector<quint32>& pBuffer);

    static int CCS_Start(QVector<quint32>& pBuffer, CCS_SCAN& pCCS_SCAN);
    static int CCS_Start(QVector<quint32>& pBuffer);
    static int CCS_Halt(QVector<quint32>& pBuffer);
    static int CCS_Update(quint32* pBuffer,quint32* nBuffer,double dSingleFreq,PARAM_STRUCT* pPARAM_STRUCT);

    static int DAQ_Config(QVector<quint32>& pBuffer,const _DAQ_CONFIG &p_DAQ_CONFIG);
    static int DAQ_Request(quint32* pBuffer, quint32 uNoDataReq);
    static int DAQ_Request(QVector<quint32>& pBuffer, quint32 uNoDataReq);
    static int DAQ_Start(QVector<quint32>& pBuffer);
    static int DAQ_Stop(QVector<quint32>& pBuffer);
    static int DAQ_Powerdown(QVector<quint32>& pBuffer);

    static int ASG_Config(QVector<quint32>& pBuffer);//,SDACConfigPara *pDacConfigP);
    static int ASG_Adjust(QVector<quint32>& pBuffer,SDACAdjustPara *pDacAdjustP=nullptr);
    static int ASG_Update(QVector<quint32>& pBuffer,_ASG_STRUCT& p_ASG_STRUCT);
    static int ASG_Start(QVector<quint32>& pBuffer,_ASG_STRUCT& p_ASG_STRUCT);
    static int ASG_Start(QVector<quint32>& pBuffer);
    static int ASG_Stop(QVector<quint32>& pBuffer);

    static QByteArray uartCMDData(QByteArray& baReq_s_comm, quint32 baudrate= 9600u);
};
}
