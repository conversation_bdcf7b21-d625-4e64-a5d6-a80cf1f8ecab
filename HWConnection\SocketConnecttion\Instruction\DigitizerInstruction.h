﻿#ifndef DIGITIZERINSTRUCTION_H
#define DIGITIZERINSTRUCTION_H

#include <utility>
#include "HWConnection_global.h"

class HWCONNECTION_EXPORT DigitizerInstruction
{
public:
    DigitizerInstruction();
    static void StartDAQ(char * pData, int & nSize);
    static void StopDAQ(char * pData, int & nSize);
    static void ResetState(char * pData, int & nSize);
    static void SetState(char * pData, int & nSize);
    static void ResetWiznet(char * pData, int & nSize);
    static void ResetAll(char * pData, int & nSize);
    static void SwitchToUMA(char * pData, int & nSize);
    static void SwitchToTOF(char * pData, int & nSize);
    static void AccParam(char * pData, std::pair<unsigned short, unsigned short> pAcc, int & nSize);
    static void BaseLineParam(char * pData, unsigned short uVal, int & nSize);
    static void NoiseLevelParam(char * pData, unsigned short uVal, int & nSize);
};
#endif // DIGITIZERINSTRUCTION_H
