#include "sMainWindow.h"

int sMainWindow::m_logBeginID = 0;
sSafeData<QList<QString>> sMainWindow::m_logs;
void sMainWindow::msgHandler(QtMsgType type, const QMessageLogContext &content, const QString &msg)
{
    QString strMsg;
    strMsg = QTime::currentTime().toString("hhmmss");
    switch (type) {
    case QtDebugMsg:
        strMsg +=  "d:";
        break;
    case QtWarningMsg:

        strMsg +=  QString("w:%1 line %2 %3").arg(content.function).arg(content.line).arg(content.category) ;
        break;
    case QtCriticalMsg:
        strMsg +=  "c:";
        break;
    case QtFatalMsg:
        strMsg +=  "f:";
    }
    strMsg += msg;// + "\n";

    m_logs.lockForWrite();
    if(m_logs->size() > 1000){
        m_logs->erase(m_logs->begin(), m_logs->begin() + 500);
        m_logBeginID += 500;
    }
    m_logs->append(strMsg);
    m_logs.unlock();
    //ts<<strMsg;
    fprintf(stderr,"%s\n",strMsg.toLocal8Bit().constData());
    fflush(stderr);
    if(type == QtFatalMsg)abort();
}

QStringList sMainWindow::logs(int& nBeginID)
{
    QStringList lstLog;
    if(nBeginID > (m_logBeginID + m_logs->size() - 1)){
        return lstLog;
    }
    int nIndex;
    m_logs.lockForRead();
    if(m_logBeginID > nBeginID)
        nIndex = 0;
    else
        nIndex = nBeginID - m_logBeginID;
    nBeginID = m_logBeginID + m_logs->size();
    for(auto iterMsg = m_logs->begin() + nIndex; iterMsg != m_logs->end(); iterMsg++)
        lstLog.append(*iterMsg);
    m_logs.unlock();
    return lstLog;
}
